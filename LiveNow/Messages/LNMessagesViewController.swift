//
//  LNMessagesViewController.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/7/28.
//

import UIKit
import SnapKit
import JXSegmentedView
import JKSwiftExtension

/// 消息页视图控制器 - 聊天功能
class LNMessagesViewController: LNBaseController {
    // MARK: - 属性
    //顶部渐变背景
//    private lazy var topGradientView: UIView = {
//        let view = UIView(frame: CGRect(x: 0, y: 0, width: kscreenW, height: s(200)))
//        return view
//    }()
    override var useArcGradientBackground: Bool { return true }
    // 分段视图
    private lazy var segmentedView: JXSegmentedView = {
        let segmentedView = JXSegmentedView()
        segmentedView.contentEdgeInsetLeft = s(16)
        segmentedView.delegate = self

        return segmentedView
    }()
    
    // 分段数据源
    private lazy var segmentedDataSource: JXSegmentedTitleDataSource = {
        let dataSource = JXSegmentedTitleDataSource()
        dataSource.titles = ["Messages", "Call", "Follwed"]
        dataSource.titleNormalColor = UIColor.hex(hexString: "#B3EBE0")
        dataSource.titleSelectedColor = UIColor.hex(hexString: "#00DFAB")
        dataSource.titleNormalFont = LNFont.bold(18)
        dataSource.titleSelectedFont = LNFont.black(18)
        dataSource.isTitleColorGradientEnabled = true
        dataSource.itemSpacing = s(40)
        dataSource.isItemSpacingAverageEnabled = false
        return dataSource
    }()
    
    // 分段指示器
    private lazy var segmentedIndicator: JXSegmentedIndicatorImageView = {
        let indicator = JXSegmentedIndicatorImageView()
        indicator.indicatorWidth = s(26)
        indicator.indicatorHeight = s(10)
        indicator.image = UIImage(named: "ic_home_seg")
        indicator.indicatorCornerRadius = s(1.5)
        return indicator
    }()
    
    // 内容列表视图控制器
    private lazy var listContainerView: JXSegmentedListContainerView = {
        let containerView = JXSegmentedListContainerView(dataSource: self)
        return containerView
    }()
    
    // 子视图控制器
    private lazy var messagesController = LNMessagesListController()
    private lazy var callController = LNCallController()
    private lazy var followedController = LNFollowedController()
    
    override var prefersNavigationBarHide: Bool {
        return true
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupSegmentedView()
        setupUI()
        setupConstraints()
    }
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 顶部渐变背景
//        topGradientView.jk.gradientColor(.vertical, [UIColor.hex(hexString: "#B8FDFF").cgColor, UIColor.hex(hexString: "#FFFFFF").cgColor], [0.0, 1.0])
    }
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        print("Navigation bar is hidden: \(navigationController?.isNavigationBarHidden ?? false)")
    }
    
    // MARK: - 私有方法
    private func setupSegmentedView() {
        // 配置分段控制器
        segmentedView.dataSource = segmentedDataSource
        segmentedView.indicators = [segmentedIndicator]
        segmentedView.listContainer = listContainerView
        // 设置分段选中下标
        segmentedView.defaultSelectedIndex = 0
    }
    
    private func setupUI() {
        view.backgroundColor = .white
        // 添加子视图
//        view.addSubview(topGradientView)
        view.addSubview(segmentedView)
        view.addSubview(listContainerView)
    }
    
    private func setupConstraints() {
        // 分段控制器
        segmentedView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.height.equalTo(50)
        }
        
        // 内容容器视图
        listContainerView.snp.makeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }
}

// MARK: - JXSegmentedViewDelegate
extension LNMessagesViewController: JXSegmentedViewDelegate {
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        // 处理选中事件，可以在这里添加自定义逻辑
    }
}

// MARK: - JXSegmentedListContainerViewDataSource
extension LNMessagesViewController: JXSegmentedListContainerViewDataSource {
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return segmentedDataSource.titles.count
    }
    
    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        switch index {
        case 0:
            return messagesController
        case 1:
            return callController
        case 2:
            return followedController
        default:
            fatalError("未知的索引：\(index)")
        }
    }
}



