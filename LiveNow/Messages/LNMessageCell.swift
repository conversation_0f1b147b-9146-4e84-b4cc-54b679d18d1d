//
//  LNMessageCell.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import UIKit
import SnapKit

// 消息模型
struct MessageModel {
    let userName: String
    let content: String
    let time: String
    let avatar: UIImage?
    
    init(userName: String, content: String, time: String, avatar: UIImage? = nil) {
        self.userName = userName
        self.content = content
        self.time = time
        self.avatar = avatar
    }
}

// 消息单元格
class LNMessageCell: UITableViewCell {
    private let avatarImageView = UIImageView()
    private let nameLabel = UILabel()
    private let contentLabel = UILabel()
    private let timeLabel = UILabel()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupCell() {
        selectionStyle = .none
        
        // 配置头像
        avatarImageView.layer.cornerRadius = 25
        avatarImageView.clipsToBounds = true
        avatarImageView.backgroundColor = .lightGray
        
        // 配置标签
        nameLabel.font = LNFont.bold(18)
        nameLabel.textColor = .black
        
        contentLabel.font = LNFont.regular(14)
        contentLabel.textColor = .gray
        
        timeLabel.font = LNFont.regular(12)
        timeLabel.textColor = .gray
        timeLabel.textAlignment = .right
        
        // 添加子视图
        contentView.addSubview(avatarImageView)
        contentView.addSubview(nameLabel)
        contentView.addSubview(contentLabel)
        contentView.addSubview(timeLabel)
        
        // 布局约束
        avatarImageView.snp.makeConstraints { make in
            make.left.equalTo(contentView).offset(15)
            make.centerY.equalTo(contentView)
            make.width.height.equalTo(50)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(contentView).offset(15)
            make.left.equalTo(avatarImageView.snp.right).offset(10)
            make.right.equalTo(timeLabel.snp.left).offset(-10)
        }
        
        contentLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(5)
            make.left.equalTo(nameLabel)
            make.right.equalTo(contentView).offset(-15)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(contentView).offset(15)
            make.right.equalTo(contentView).offset(-15)
            make.width.equalTo(70)
        }
    }
    
    func configure(with message: MessageModel) {
        nameLabel.text = message.userName
        contentLabel.text = message.content
        timeLabel.text = message.time
        
        if let avatar = message.avatar {
            avatarImageView.image = avatar
        }
    }
}
