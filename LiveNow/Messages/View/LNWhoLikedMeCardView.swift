//
//  LNWhoLikedMeCardView.swift
//  LiveNow
//
//  Created by po on 2025/8/17.
//

import UIKit

class LNWhoLikedMeCardView: UIView {

    // 点击回调
    var onTapped: (() -> Void)?

    private lazy var bgView = UIView()
    private lazy var lockIcon = UIImageView()
    private lazy var label = UILabel()
    private lazy var avatarStackView = LNMessageAvatarStack()
    private lazy var countLabel = UILabel()
    private lazy var countBadge = UIView()
    private lazy var chevronIcon = UIImageView()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupGesture()
    }
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupGesture()
    }
    override func layoutSubviews() {
        super.layoutSubviews()
        bgView.jk.gradientColor(.horizontal, [UIColor.hex(hexString: "#AFFFE3").cg<PERSON>olor, UIColor.hex(hexString: "#B7FDFF").cgColor], [0.0, 1.0])
    }
    func setupUI() {
        addSubview(bgView)
        addSubview(lockIcon)
        addSubview(label)
        addSubview(avatarStackView)
        addSubview(countBadge)
        countBadge.addSubview(countLabel)
        addSubview(chevronIcon)
        
        lockIcon.image =  UIImage(named: "ic_msg_lock")
        label.text = "who liked me"
        label.textColor = UIColor.hex(hexString: "#00DFAB")
        label.font = LNFont.medium(18)
        
        countLabel.text = "10"
        countLabel.textColor = .white
        countLabel.font = LNFont.bold(14)
        countLabel.textAlignment = .center
        
        countBadge.backgroundColor = UIColor.hex(hexString: "#00DFAB")
        countBadge.layer.cornerRadius = 12
        
        chevronIcon.image = UIImage(named: "ic_msg_arrow")
        
        bgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        lockIcon.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        label.snp.makeConstraints { make in
            make.left.equalTo(lockIcon.snp.right).offset(8)
            make.centerY.equalToSuperview()
        }
        
        avatarStackView.snp.makeConstraints { make in
            make.right.equalTo(countBadge.snp.left).offset(-12)
            make.centerY.equalToSuperview()
            make.height.equalTo(30)
            make.width.equalTo(90)
        }
        
        countBadge.snp.makeConstraints { make in
            make.right.equalTo(chevronIcon.snp.left).offset(-8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        countLabel.snp.makeConstraints { make in
            make.edges.equalTo(countBadge)
        }
        
        chevronIcon.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(16)
            make.height.equalTo(16)
        }
    }

    private func setupGesture() {
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(cardTapped))
        addGestureRecognizer(tapGesture)
        isUserInteractionEnabled = true
    }

    @objc private func cardTapped() {
        // 触发回调
        onTapped?()
    }

    /// 更新喜欢我的人数
    func updateCount(_ count: Int) {
        countLabel.text = "\(count)"
    }
}


class LNMessageAvatarStack: UIView {
    
}
