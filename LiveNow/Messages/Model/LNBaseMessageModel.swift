//
//  LNBaseMessageModel.swift
//  LiveNow
//
//  Created by po on 2025/8/24.
//

import UIKit
import RongIMLibCore

class LNBaseMessageModel: NSObject {
    
    var cellHeight: CGFloat = 0.0
    var messageViewFrame: CGRect = .zero
    var avatorFrame: CGRect = .zero
    var timeFrame: CGRect = .zero
    var failTipFrame: CGRect = .zero
    var failIconFrame: CGRect = .zero
    // MARK: - 文字
    var textFrame: CGRect = .zero
    // MARK: - 语音
    /// 语音icon
    var soundIconFrame: CGRect = .zero
    /// 语音时长
    var soundTimeFrame: CGRect = .zero
    /// 语音是否已读红点
    var soundHasReadFrame: CGRect = .zero
    // MARK: - 图片
    var imageFrame: CGRect = .zero
    // MARK: - 礼物
    
    
    var originalMessage: RCMessage?
    
    var messageModel: LNBaseMessage = LNBaseMessage(time: "", avator: "", type: .LNMessageContentTypeText, userType: .LNMessageTypeOther, sendStatus: 0, hideTime: false) {
        didSet {
            // 间距
            let padding = s(20)
            // 1.时间
            if !messageModel.hideTime {
                timeFrame = CGRect.init(x: 0, y: 0, width: kscreenW, height: s(30))
            }
            // 2.头像
            let iconY: CGFloat = timeFrame.maxY + padding/2
            let iconW: CGFloat = s(36)
            let iconH: CGFloat = s(36)
            var iconX: CGFloat = 0
            if messageModel.userType == .LNMessageTypeOther {
                iconX = padding
            } else {
                iconX = kscreenW - padding - s(36)
            }
            avatorFrame = CGRect.init(x: iconX, y: iconY, width: iconW, height: iconH)
            // 消息实体
            if messageModel.type == .LNMessageContentTypeText {
                // 文本消息
                calculateTextFrame(message: messageModel)
            } else if messageModel.type == .LNMessageContentTypePhoto {
                calculatePhotoFrame(message: messageModel)
            } else if messageModel.type == .LNMessageContentTypeSound {
                calculateVoiceFrame(message: messageModel)
            } else if messageModel.type == .LNMessageContentTypeVideo {
                calculateVideoFrame(message: messageModel)
            } else if messageModel.type == .LNMessageContentTypeGift {
                calculateGiftFrame(message: messageModel)
            }
        }
    }
    // MARK: - 计算文本消息的Frame
    func calculateTextFrame(message: LNBaseMessage) {
        
    }
    // MARK: - 计算图片消息的Frame
    func calculatePhotoFrame(message: LNBaseMessage) {
        
    }
    // MARK: - 计算语音消息的Frame
    func calculateVoiceFrame(message: LNBaseMessage) {
        
    }
    // MARK: - 计算视频消息的Frame
    func calculateVideoFrame(message: LNBaseMessage) {
        
    }
    // MARK: - 计算礼物消息的Frame
    func calculateGiftFrame(message: LNBaseMessage) {
        
    }
}
