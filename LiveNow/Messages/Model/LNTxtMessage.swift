//
//  LNTxtMessage.swift
//  LiveNow
//
//  Created by po on 2025/8/24.
//

import UIKit
import RongIMLibCore

class LNTxtMessage: LNBaseMessage {
    var txt: RCTextMessage?
    init(txt: RCTextMessage,time: String, avator: String, type: LNMessageContentType, userType: LNMessageUserType, sendStatus: Int, hideTime: Bool) {
        super.init(time: time, avator: avator, type: type, userType: userType, sendStatus: sendStatus, hideTime: hideTime)
        self.txt = txt
    }
}


