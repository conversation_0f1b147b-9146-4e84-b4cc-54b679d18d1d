//
//  LNBaseMessage.swift
//  LiveNow
//
//  Created by po on 2025/8/24.
//

import UIKit

enum LNMessageUserType {
    case LNMessageTypeMe            // 自己发的消息
    case LNMessageTypeOther         // 别人发的消息
}
enum LNMessageContentType {
    case LNMessageContentTypeText               // 文本消息
    case LNMessageContentTypePhoto              // 图片消息
    case LNMessageContentTypeSound              // 语音消息
    case LNMessageContentTypeVideo              // 视频消息
    case LNMessageContentTypeGift               //礼物消息
}

class LNBaseMessage: NSObject {
    ///发送时间
    var time: String = ""
    ///用户头像
    var avator: String = ""
    ///消息类型
    var type: LNMessageContentType = .LNMessageContentTypeText
    ///发送用户类型
    var userType: LNMessageUserType = .LNMessageTypeMe
    ///消息发送状态
    var sendStatus: Int = 0
    ///是否隐藏时间
    var hideTime:Bool = false
    
    var data: [String: Any]?
    
    init(time: String, avator: String, type: LNMessageContentType, userType: LNMessageUserType, sendStatus: Int, hideTime: Bool, data: [String : Any]? = nil) {
        self.time = time
        self.avator = avator
        self.type = type
        self.userType = userType
        self.sendStatus = sendStatus
        self.hideTime = hideTime
        self.data = data
    }
}
