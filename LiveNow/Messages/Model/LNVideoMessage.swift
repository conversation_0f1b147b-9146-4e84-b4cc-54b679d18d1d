//
//  LNVideoMessage.swift
//  LiveNow
//
//  Created by po on 2025/8/24.
//

import UIKit
import RongIMLibCore

class LNVideoMessage: LNBaseMessage {
    var videoModel: RCSightMessage?
    init(video:RCSightMessage, time: String, avator: String, type: LNMessageContentType, userType: LNMessageUserType, sendStatus: Int, hideTime: Bool, data: [String : Any]? = nil) {
        super.init(time: time, avator: avator, type: type, userType: userType, sendStatus: sendStatus, hideTime: hideTime)
        self.videoModel = video
    }
}
