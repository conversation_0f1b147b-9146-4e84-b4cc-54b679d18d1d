//
//  LNVoiceMessage.swift
//  LiveNow
//
//  Created by po on 2025/8/24.
//

import UIKit
import RongIMLibCore

class LNVoiceMessage: LNBaseMessage {
    var voiceModel: RCHQVoiceMessage?
    init(voiceModel: RCHQVoiceMessage,  time: String, avator: String, type: LNMessageContentType, userType: LNMessageUserType, sendStatus: Int, hideTime: Bool) {
        super.init(time: time, avator: avator, type: type, userType: userType, sendStatus: sendStatus, hideTime: hideTime)
        self.voiceModel = voiceModel
    }
}
