//
//  LNImageMessage.swift
//  LiveNow
//
//  Created by po on 2025/8/24.
//

import UIKit
import RongIMLibCore
class LNImageMessage: LNBaseMessage {
    // 缩略图
    var thumbnail: UIImage?
    // 原图
    var originPic: UIImage?
    // 图片的发送路径
    var picPath: String = ""
    // 图片播放器显示
    var picUrl: String = ""
    // 图片上传进度
    var progress: Double = 1.0
    
    init(img: RCImageMessage,time: String, avator: String, type: LNMessageContentType, userType: LNMessageUserType, sendStatus: Int, hideTime: Bool, data: [String : Any]? = nil) {
        super.init(time: time, avator: avator, type: type, userType: userType, sendStatus: sendStatus, hideTime: hideTime)
        if let imgUrl = img.imageUrl {
            self.picPath = imgUrl
            self.picUrl  = imgUrl
        }
        if let thumbImage = img.thumbnailImage {
            self.thumbnail = thumbImage
        } else  {
            if let remoteUrl = img.remoteUrl,remoteUrl.count > 0 {
                self.picUrl = remoteUrl
            }
        }
        if let originalImg = img.originalImage {
            self.originPic = originalImg
        }
        
    }
    
}
