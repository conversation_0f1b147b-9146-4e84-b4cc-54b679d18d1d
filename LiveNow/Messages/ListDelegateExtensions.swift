//
//  ListDelegateExtensions.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import UIKit
import JXSegmentedView

// 扩展所有子控制器以支持JXSegmentedListContainerViewListDelegate协议
extension LNMessagesListController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}

extension LNCallController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}

extension LNFollowedController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}