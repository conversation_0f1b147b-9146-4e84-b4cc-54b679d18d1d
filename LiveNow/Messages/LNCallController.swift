//
//  LNCallController.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import UIKit

class LNCallController: LNBaseController {
    override var prefersNavigationBarHide: Bool {
        return true
    }
    
    // 通话记录列表
    private lazy var tableView: UITableView = {
        let table = UITableView()
        table.backgroundColor = .clear
        table.separatorStyle = .none
        table.showsVerticalScrollIndicator = false
        table.register(CallCell.self, forCellReuseIdentifier: "CallCell")
        table.delegate = self
        table.dataSource = self
        
        // 添加底部"No More Data"视图
        let footerView = UIView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 50))
        let footerLabel = UILabel()
        footerLabel.text = "No More Data"
        footerLabel.textColor = UIColor.lightGray
        footerLabel.font = LNFont.regular(16)
        footerLabel.textAlignment = .center
        footerView.addSubview(footerLabel)
        footerLabel.frame = footerView.bounds
        table.tableFooterView = footerView
        
        return table
    }()
    
    // 通话记录数据
    private var calls: [CallModel] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .clear
        setupUI()
        loadCalls()
    }
    
    private func setupUI() {
        view.addSubview(tableView)
        
        tableView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.left.right.bottom.equalToSuperview()
        }
    }
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        tableView.jk.addCorner(conrners: [.topLeft,.topRight], radius: s(30))
    }
    private func loadCalls() {
        // 创建模拟数据 - 根据截图调整
        let mockCalls = [
            CallModel(userName: "Usre name", callType: .completed, time: "08/20 20:18:34", avatar: nil),
            CallModel(userName: "Usre name", callType: .completed, time: "08/20 20:18:34", avatar: nil),
            CallModel(userName: "Usre name", callType: .completed, time: "08/20 20:18:34", avatar: nil)
        ]
        
        calls = mockCalls
        tableView.reloadData()
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension LNCallController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return calls.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "CallCell", for: indexPath) as! CallCell
        let call = calls[indexPath.row]
        cell.configure(with: call)
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }
}

// 通话记录模型
struct CallModel {
    enum CallType {
        case incoming
        case outgoing
        case missed
        case completed  // 添加已完成类型
    }
    
    let userName: String
    let callType: CallType
    let time: String
    let avatar: UIImage?
}

// 通话记录单元格
class CallCell: UITableViewCell {
    private let avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 25
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor.lightGray.withAlphaComponent(0.5)
        return imageView
    }()
    
    private let nameLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.bold(18)
        label.textColor = .black
        return label
    }()
    
    private let callStatusView: UIView = {
        let view = UIView()
        return view
    }()
    
    private let callStatusIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = UIColor.hex(hexString: "#00DFAB")
        return imageView
    }()
    
    private let callStatusLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.medium(14)
        label.textColor = UIColor.hex(hexString: "#00DFAB")
        return label
    }()
    
    private let timeLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.regular(14)
        label.textColor = .gray
        label.textAlignment = .right
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupCell() {
        // 设置样式和添加子视图
        backgroundColor = .clear
        selectionStyle = .none
        contentView.backgroundColor = .white
//        contentView.layer.cornerRadius = 20
//        contentView.layer.masksToBounds = true
        
        // 添加通话状态视图
        callStatusView.addSubview(callStatusIcon)
        callStatusView.addSubview(callStatusLabel)
        
        // 添加所有子视图
        contentView.addSubview(avatarImageView)
        contentView.addSubview(nameLabel)
        contentView.addSubview(callStatusView)
        contentView.addSubview(timeLabel)
        
        // 布局约束
        avatarImageView.snp.makeConstraints { make in
            make.left.equalTo(contentView).offset(15)
            make.centerY.equalTo(contentView)
            make.width.height.equalTo(50)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(10)
            make.top.equalTo(avatarImageView).offset(2)
            make.right.lessThanOrEqualTo(timeLabel.snp.left).offset(-10)
        }
        
        callStatusIcon.snp.makeConstraints { make in
            make.left.equalTo(callStatusView)
            make.centerY.equalTo(callStatusView)
            make.width.height.equalTo(16)
        }
        
        callStatusLabel.snp.makeConstraints { make in
            make.left.equalTo(callStatusIcon.snp.right).offset(4)
            make.centerY.equalTo(callStatusView)
            make.right.equalTo(callStatusView)
        }
        
        callStatusView.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(6)
            make.height.equalTo(20)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.right.equalTo(contentView).offset(-15)
            make.centerY.equalTo(contentView)
            make.width.greaterThanOrEqualTo(100)
        }
    }
    
    func configure(with call: CallModel) {
        nameLabel.text = call.userName
        timeLabel.text = call.time
        
        if let avatar = call.avatar {
            avatarImageView.image = avatar
        }
        
        // 设置通话状态图标和文本
        switch call.callType {
        case .incoming:
            callStatusIcon.image = UIImage(named: "ic_msg_call")
            callStatusLabel.text = "Incoming Call"
            callStatusIcon.tintColor = UIColor.hex(hexString: "#00DFAB")
            callStatusLabel.textColor = UIColor.hex(hexString: "#00DFAB")
            
        case .outgoing:
            callStatusIcon.image = UIImage(systemName: "phone.arrow.up.right.fill")
            callStatusLabel.text = "Outgoing Call"
            callStatusIcon.tintColor = UIColor.hex(hexString: "#00DFAB")
            callStatusLabel.textColor = UIColor.hex(hexString: "#00DFAB")
            
        case .missed:
            callStatusIcon.image = UIImage(systemName: "phone.down.fill")
            callStatusLabel.text = "Missed Call"
            callStatusIcon.tintColor = .red
            callStatusLabel.textColor = .red
            
        case .completed:
            // 根据截图设置已完成通话状态
            callStatusIcon.image = UIImage(named: "ic_msg_call")
            callStatusLabel.text = "Call Completed"
            callStatusIcon.tintColor = UIColor.hex(hexString: "#45D928")
            callStatusLabel.textColor = UIColor.hex(hexString: "#45D928")
        }
    }
}
