//
//  LNFollowedController.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import UIKit

class LNFollowedController: LNBaseController {
    override var prefersNavigationBarHide: Bool {
        return true
    }
    
    // 关注列表
    private lazy var tableView: UITableView = {
        let table = UITableView()
        table.backgroundColor = .clear
        table.separatorStyle = .none
        table.showsVerticalScrollIndicator = false
        table.register(FollowedUserCell.self, forCellReuseIdentifier: "FollowedUserCell")
        table.delegate = self
        table.dataSource = self
        // 添加底部文字
        let footerView = UIView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 50))
        let footerLabel = UILabel()
        footerLabel.text = "No More Data"
        footerLabel.textColor = UIColor.lightGray
        footerLabel.font = LNFont.regular(16)
        footerLabel.textAlignment = .center
        footerView.addSubview(footerLabel)
        footerLabel.frame = footerView.bounds
        table.tableFooterView = footerView
        return table
    }()
    
    // 关注数据
    private var followedUsers: [FollowedUserModel] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .clear
        setupUI()
        loadFollowedUsers()
    }
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        tableView.jk.addCorner(conrners: [.topLeft,.topRight], radius: s(30))
    }
    private func setupUI() {
        view.addSubview(tableView)
        
        tableView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    private func loadFollowedUsers() {
        // 创建模拟数据
        let mockUsers = [
            FollowedUserModel(name: "Usre name", age: 22, location: "Indonesia", isOnline: true, avatar: nil, buttonType: .videoCall),
            FollowedUserModel(name: "Usre name", age: 22, location: "Indonesia", isOnline: true, avatar: nil, buttonType: .videoCall),
            FollowedUserModel(name: "Usre name", age: 22, location: "Indonesia", isOnline: true, avatar: nil, buttonType: .message),
            FollowedUserModel(name: "Usre name", age: 22, location: "Indonesia", isOnline: true, avatar: nil, buttonType: .videoCall)
        ]
        
        followedUsers = mockUsers
        tableView.reloadData()
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension LNFollowedController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return followedUsers.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "FollowedUserCell", for: indexPath) as! FollowedUserCell
        let user = followedUsers[indexPath.row]
        cell.configure(with: user)
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }
}

// 关注用户模型
struct FollowedUserModel {
    enum ButtonType {
        case videoCall
        case message
    }
    
    let name: String
    let age: Int
    let location: String
    let isOnline: Bool
    let avatar: UIImage?
    let buttonType: ButtonType
}

// 关注用户单元格
class FollowedUserCell: UITableViewCell {
    private let avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 25
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor.lightGray.withAlphaComponent(0.5)
        return imageView
    }()
    
    private let nameLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.bold(18)
        label.textColor = .black
        return label
    }()
    
    private let ageLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.medium(14)
        label.textColor = .white
        label.textAlignment = .center
        label.backgroundColor = UIColor.hex(hexString: "#FF6EB4") // 粉红色
        label.layer.cornerRadius = 10
        label.clipsToBounds = true
        return label
    }()
    
    private let locationLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.medium(14)
        label.textColor = .white
        label.textAlignment = .center
        label.backgroundColor = UIColor.hex(hexString: "#40E0D0") // 绿松石色
        label.layer.cornerRadius = 10
        label.clipsToBounds = true
        return label
    }()
    
    private let actionButton: UIButton = {
        let button = UIButton(type: .custom)
        button.backgroundColor = UIColor.hex(hexString: "#00DFAB")
        button.layer.cornerRadius = 25
        button.clipsToBounds = true
        return button
    }()
    
    private let tagStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.spacing = 8
        stack.alignment = .center
        stack.distribution = .fillProportionally
        return stack
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupCell() {
        backgroundColor = .clear
        selectionStyle = .none
        contentView.backgroundColor = .white        
        // 添加子视图
        contentView.addSubview(avatarImageView)
        contentView.addSubview(nameLabel)
        contentView.addSubview(tagStackView)
        tagStackView.addArrangedSubview(ageLabel)
        tagStackView.addArrangedSubview(locationLabel)
        contentView.addSubview(actionButton)
        
        // 设置约束
        avatarImageView.snp.makeConstraints { make in
            make.left.equalTo(contentView).offset(15)
            make.centerY.equalTo(contentView)
            make.width.height.equalTo(50)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(10)
            make.top.equalTo(contentView).offset(15)
            make.right.lessThanOrEqualTo(actionButton.snp.left).offset(-10)
        }
        
        tagStackView.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(8)
        }
        
        ageLabel.snp.makeConstraints { make in
            make.height.equalTo(20)
            make.width.greaterThanOrEqualTo(40)
        }
        
        locationLabel.snp.makeConstraints { make in
            make.height.equalTo(20)
            make.width.greaterThanOrEqualTo(80)
        }
        
        actionButton.snp.makeConstraints { make in
            make.right.equalTo(contentView).offset(-15)
            make.centerY.equalTo(contentView)
            make.width.height.equalTo(50)
        }
        
        // 调整内边距
        ageLabel.layoutMargins = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: 8)
        locationLabel.layoutMargins = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: 8)
    }
    
    func configure(with user: FollowedUserModel) {
        nameLabel.text = user.name
        ageLabel.text = "\(user.age)"
        locationLabel.text = user.location
        
        // 设置间距
//        ageLabel.contentEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 10)
//        locationLabel.contentEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 10)
        
        if let avatar = user.avatar {
            avatarImageView.image = avatar
        }
        
        // 设置按钮图标
        switch user.buttonType {
        case .videoCall:
            actionButton.setImage(UIImage(named: "ic_msg_call")?.withRenderingMode(.alwaysTemplate), for: .normal)
            actionButton.tintColor = .white
            actionButton.imageView?.contentMode = .scaleAspectFit
            actionButton.imageEdgeInsets = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        case .message:
            actionButton.setImage(UIImage(named: "ic_msg_call")?.withRenderingMode(.alwaysTemplate), for: .normal)
            actionButton.tintColor = .white
            actionButton.imageView?.contentMode = .scaleAspectFit
            actionButton.imageEdgeInsets = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        }
    }
}
