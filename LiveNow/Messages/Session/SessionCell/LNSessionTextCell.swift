//
//  LNSessionTextCell.swift
//  LiveNow
//
//  Created by po on 2025/8/16.
//

import UIKit

class LNSessionTextCell: UITableViewCell {
    private let avatarImageView = UIImageView()
    private let messageBubbleView = UIView()
    private let messageLabel = UILabel()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupCell() {
        backgroundColor = .clear
        selectionStyle = .none
        
        // 头像
        avatarImageView.backgroundColor = UIColor.lightGray.withAlphaComponent(0.5)
        avatarImageView.layer.cornerRadius = 20
        avatarImageView.clipsToBounds = true
        contentView.addSubview(avatarImageView)
        
        // 消息气泡
        messageBubbleView.layer.cornerRadius = 16
        contentView.addSubview(messageBubbleView)
        
        // 消息文本
        messageLabel.font = LNFont.regular(16)
        messageLabel.numberOfLines = 0
        messageBubbleView.addSubview(messageLabel)
        
        // 消息文本内边距
        messageLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 12, bottom: 8, right: 12))
        }
    }
    
    func configure(with message: LNSessionViewController.Message) {
        if message.direction == .sent {
            // 发送的消息在右侧
            avatarImageView.snp.remakeConstraints { make in
                make.right.equalTo(contentView).offset(-16)
                make.top.equalTo(contentView).offset(8)
                make.width.height.equalTo(40)
            }
            
            messageBubbleView.snp.remakeConstraints { make in
                make.right.equalTo(avatarImageView.snp.left).offset(-8)
                make.top.equalTo(contentView).offset(8)
                make.left.greaterThanOrEqualTo(contentView).offset(70)
            }
            
            messageBubbleView.backgroundColor = UIColor.hex(hexString: "#E4FFE7") // 浅绿色
            messageLabel.textColor = .black
        } else {
            // 接收的消息在左侧
            avatarImageView.snp.remakeConstraints { make in
                make.left.equalTo(contentView).offset(16)
                make.top.equalTo(contentView).offset(8)
                make.width.height.equalTo(40)
            }
            
            messageBubbleView.snp.remakeConstraints { make in
                make.left.equalTo(avatarImageView.snp.right).offset(8)
                make.top.equalTo(contentView).offset(8)
                make.right.lessThanOrEqualTo(contentView).offset(-70)
            }
            
            messageBubbleView.backgroundColor = .white
            messageLabel.textColor = .black
        }
        
        messageLabel.text = message.content
    }
}
