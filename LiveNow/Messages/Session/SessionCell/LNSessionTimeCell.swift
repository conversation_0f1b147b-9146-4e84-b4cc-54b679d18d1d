//
//  LNSessionTimeCell.swift
//  LiveNow
//
//  Created by po on 2025/8/16.
//

import UIKit

class LNSessionTimeCell: UITableViewCell {
    private let timeLabel = UILabel()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupCell() {
        backgroundColor = .clear
        selectionStyle = .none
        
        timeLabel.font = LNFont.regular(12)
        timeLabel.textColor = .gray
        timeLabel.textAlignment = .center
        contentView.addSubview(timeLabel)
        
        timeLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }
    
    func configure(time: String) {
        timeLabel.text = time
    }
}
