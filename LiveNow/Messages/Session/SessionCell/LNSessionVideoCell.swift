//
//  LNSessionVideoCell.swift
//  LiveNow
//
//  Created by po on 2025/8/16.
//

import UIKit

class LNSessionVideoCell: UITableViewCell {
    private let avatarImageView = UIImageView()
    private let mediaContainerView = UIView()
    private let mediaImageView = UIImageView()
    private let playButton = UIButton()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupCell() {
        backgroundColor = .clear
        selectionStyle = .none
        
        // 头像
        avatarImageView.backgroundColor = UIColor.lightGray.withAlphaComponent(0.5)
        avatarImageView.layer.cornerRadius = 20
        avatarImageView.clipsToBounds = true
        contentView.addSubview(avatarImageView)
        
        // 媒体容器
        mediaContainerView.layer.cornerRadius = 16
        mediaContainerView.clipsToBounds = true
        contentView.addSubview(mediaContainerView)
        
        // 媒体图片
        mediaImageView.contentMode = .scaleAspectFill
        mediaImageView.backgroundColor = UIColor.lightGray.withAlphaComponent(0.3)
        mediaContainerView.addSubview(mediaImageView)
        
        // 播放按钮
        playButton.setImage(UIImage(systemName: "play.circle.fill"), for: .normal)
        playButton.tintColor = .white
        playButton.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        playButton.layer.cornerRadius = 25
        mediaContainerView.addSubview(playButton)
        
        // 布局
        mediaImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        playButton.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(50)
        }
    }
    
    func configure(with message: LNSessionViewController.Message) {
        if message.direction == .sent {
            // 发送的消息在右侧
            avatarImageView.snp.remakeConstraints { make in
                make.right.equalTo(contentView).offset(-16)
                make.top.equalTo(contentView).offset(8)
                make.width.height.equalTo(40)
            }
            
            mediaContainerView.snp.remakeConstraints { make in
                make.right.equalTo(avatarImageView.snp.left).offset(-8)
                make.top.equalTo(contentView).offset(8)
                make.left.greaterThanOrEqualTo(contentView).offset(70)
                make.height.equalTo(180)
                make.width.equalTo(160)
            }
        } else {
            // 接收的消息在左侧
            avatarImageView.snp.remakeConstraints { make in
                make.left.equalTo(contentView).offset(16)
                make.top.equalTo(contentView).offset(8)
                make.width.height.equalTo(40)
            }
            
            mediaContainerView.snp.remakeConstraints { make in
                make.left.equalTo(avatarImageView.snp.right).offset(8)
                make.top.equalTo(contentView).offset(8)
                make.right.lessThanOrEqualTo(contentView).offset(-70)
                make.height.equalTo(180)
                make.width.equalTo(160)
            }
        }
    }
}
