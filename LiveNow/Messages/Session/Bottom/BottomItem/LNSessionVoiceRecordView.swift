//
//  LNSessionVoiceRecordView.swift
//  LiveNow
//
//  Created by po on 2025/8/19.
//

import UIKit

class LNSessionVoiceRecordView: UIView {
    // 回调闭包
    var startRecordingHandler: (() -> Void)?
    var finishRecordingHandler: ((TimeInterval) -> Void)?
    var cancelRecordingHandler: (() -> Void)?
    
    // 录制状态
    private var isRecording = false
    private var recordingDuration: TimeInterval = 0
    private var recordingTimer: Timer?
    
    // MARK: - UI元素
    
    // 左侧声波图标
    private lazy var leftWaveImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_session_wave_normal")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 右侧声波图标
    private lazy var rightWaveImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_session_wave_normal")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 计时器标签
    private lazy var timerLabel: UILabel = {
        let label = UILabel()
        label.text = "0s"
        label.textColor = .black
        label.font = LNFont.medium(24)
        label.textAlignment = .center
        return label
    }()
    
    // "Hold To Talk" 提示标签
    private lazy var instructionLabel: UILabel = {
        let label = UILabel()
        label.text = "Hold To Talk"
        label.textColor = .lightGray
        label.font = LNFont.medium(16)
        label.textAlignment = .center
        return label
    }()
    
    // 录音按钮
    private lazy var recordButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "ic_session_voice_normal"), for: .normal)
        // 设置按钮手势
        button.addTarget(self, action: #selector(recordButtonTouchDown), for: .touchDown)
        button.addTarget(self, action: #selector(recordButtonTouchUp), for: .touchUpInside)
        button.addTarget(self, action: #selector(recordButtonTouchUpOutside), for: .touchUpOutside)
        
        return button
    }()
    
    // 虚线边框容器
    private lazy var dashedBorderView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // 保存虚线边框图层的引用以便更新
    private var dashedBorderLayer: CAShapeLayer?
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // 更新虚线边框路径
        if let layer = dashedBorderLayer {
            let path = UIBezierPath(roundedRect: dashedBorderView.bounds, cornerRadius: s(8))
            layer.path = path.cgPath
            layer.frame = dashedBorderView.bounds
        }
    }
    
    private func setupUI() {
        backgroundColor = .white
        
        // 添加子视图
        addSubview(dashedBorderView)
        dashedBorderView.addSubview(leftWaveImageView)
        dashedBorderView.addSubview(timerLabel)
        dashedBorderView.addSubview(rightWaveImageView)
        
        addSubview(instructionLabel)
        addSubview(recordButton)
        
        // 设置约束
        dashedBorderView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(24))
            make.centerX.equalToSuperview()
            make.width.equalTo(s(160))
            make.height.equalTo(s(40))
        }
        
        leftWaveImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(10))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(32))
        }
        
        timerLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        rightWaveImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(10))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(32))
        }
        
        instructionLabel.snp.makeConstraints { make in
            make.top.equalTo(dashedBorderView.snp.bottom).offset(s(16))
            make.centerX.equalToSuperview()
        }
        
        recordButton.snp.makeConstraints { make in
            make.top.equalTo(instructionLabel.snp.bottom).offset(s(20))
            make.centerX.equalToSuperview()
            make.width.height.equalTo(s(70))
            make.bottom.lessThanOrEqualToSuperview().offset(-s(20))
        }
    }
    
    // 准备录音
    func prepareForRecording() {
        resetUI()
    }
    
    // 重置UI
    private func resetUI() {
        isRecording = false
        recordingDuration = 0
        timerLabel.text = "0s"
        recordButton.setImage(UIImage(named: "ic_session_voice_normal"), for: .normal)
        leftWaveImageView.alpha = 0.5
        rightWaveImageView.alpha = 0.5
        stopTimer()
    }
    
    // MARK: - 录音计时功能
    
    // 开始计时
    private func startTimer() {
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            self.recordingDuration += 1.0
            self.updateTimerLabel()
            self.animateWaveforms()
        }
    }
    
    // 停止计时
    private func stopTimer() {
        recordingTimer?.invalidate()
        recordingTimer = nil
    }
    
    // 更新计时器标签
    private func updateTimerLabel() {
        timerLabel.text = "\(Int(recordingDuration))s"
    }
    
    // 动画显示声波
    private func animateWaveforms() {
        // 左侧声波动画
        UIView.animate(withDuration: 0.3, animations: {
            self.leftWaveImageView.alpha = 1.0
            self.leftWaveImageView.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        }) { _ in
            UIView.animate(withDuration: 0.3) {
                self.leftWaveImageView.alpha = 0.5
                self.leftWaveImageView.transform = .identity
            }
        }
        
        // 右侧声波动画 - 稍微延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
            UIView.animate(withDuration: 0.3, animations: {
                self.rightWaveImageView.alpha = 1.0
                self.rightWaveImageView.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
            }) { _ in
                UIView.animate(withDuration: 0.3) {
                    self.rightWaveImageView.alpha = 0.5
                    self.rightWaveImageView.transform = .identity
                }
            }
        }
    }
    
    // MARK: - 按钮事件
    
    @objc private func recordButtonTouchDown() {
        // 开始录音
        isRecording = true
        instructionLabel.text = "Recording..."
        recordButton.setImage(UIImage(named: "ic_session_voice_touchUp"), for: .normal)
        startTimer()
        startRecordingHandler?()
    }
    
    @objc private func recordButtonTouchUp() {
        guard isRecording else { return }
        
        // 结束录音
        let duration = recordingDuration
        stopTimer()
        isRecording = false
        instructionLabel.text = "Hold To Talk"
        finishRecordingHandler?(duration)
    
        // 重置UI
        resetUI()
    }
    
    @objc private func recordButtonTouchUpOutside() {
        guard isRecording else { return }
        
        // 取消录音
        stopTimer()
        isRecording = false
        instructionLabel.text = "Hold To Talk"
        cancelRecordingHandler?()
        // 重置UI
        resetUI()
    }
}
