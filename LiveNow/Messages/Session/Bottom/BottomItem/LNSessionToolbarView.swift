//
//  LNSessionToolbarView.swift
//  LiveNow
//
//  Created by po on 2025/8/16.
//

import UIKit

class LNSessionToolbarView: UIView {
    
    enum BottonBtnEventType {
        case voice
        case image
        case video
        case gift
    }
    
    // 正确定义闭包类型
    typealias BottonBtnEventBlock = (BottonBtnEventType) -> Void
    
    // 回调属性
    var sessionToolEvent: BottonBtnEventBlock?
    
    private lazy var voiceButton: UIButton = createToolbarButton(icon: "ic_session_voice", title: "Voice")
    private lazy var imageButton: UIButton = createToolbarButton(icon: "ic_session_image", title: "Image")
    private lazy var videoButton: UIButton = createToolbarButton(icon: "ic_session_video", title: "Video")
    private lazy var giftButton: UIButton = createToolbarButton(icon: "ic_session_gift", title: "Gift")
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .white
        addSubview(voiceButton)
        addSubview(imageButton)
        addSubview(videoButton)
        addSubview(giftButton)
        
        let buttonWidth = UIScreen.main.bounds.width / 4
        voiceButton.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(buttonWidth)
            make.height.equalTo(self)
        }
        
        imageButton.snp.makeConstraints { make in
            make.left.equalTo(voiceButton.snp.right)
            make.centerY.equalToSuperview()
            make.width.equalTo(buttonWidth)
            make.height.equalTo(self)
        }
        
        videoButton.snp.makeConstraints { make in
            make.left.equalTo(imageButton.snp.right)
            make.centerY.equalToSuperview()
            make.width.equalTo(buttonWidth)
            make.height.equalTo(self)
        }
        
        giftButton.snp.makeConstraints { make in
            make.left.equalTo(videoButton.snp.right)
            make.centerY.equalToSuperview()
            make.width.equalTo(buttonWidth)
            make.height.equalTo(self)
        }
        
        voiceButton.addTarget(self, action: #selector(clickVoiceEvent), for: .touchUpInside)
        imageButton.addTarget(self, action: #selector(clickImageEvent), for: .touchUpInside)
        videoButton.addTarget(self, action: #selector(clickVideoEvent), for: .touchUpInside)
        giftButton.addTarget(self, action: #selector(clickGiftEvent), for: .touchUpInside)
    }

    @objc func clickVoiceEvent() {
        sessionToolEvent?(.voice)
    }
    
    @objc func clickImageEvent() {
        sessionToolEvent?(.image)
    }
    
    @objc func clickVideoEvent() {
        sessionToolEvent?(.video)
    }
    
    @objc func clickGiftEvent() {
        sessionToolEvent?(.gift)
    }
    
    // MARK: - 辅助方法
    private func createToolbarButton(icon: String, title: String) -> UIButton {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: icon), for: .normal)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = LNFont.regular(12)
        button.setTitleColor(.gray, for: .normal)
        button.tintColor = .gray
        // 设置图片在上，文字在下
        button.centerVertically(spacing: 5)
        return button
    }
}

// MARK: - 辅助扩展
extension UIButton {
    func centerVertically(spacing: CGFloat = 6.0) {
        guard let imageSize = imageView?.image?.size,
              let text = titleLabel?.text,
              let font = titleLabel?.font
        else { return }
        
        let titleSize = text.size(withAttributes: [.font: font])
        
        let titleOffset = -(imageSize.height + spacing)
        titleEdgeInsets = UIEdgeInsets(top: 0.0, left: -imageSize.width, bottom: titleOffset, right: 0.0)
        
        let imageOffset = -(titleSize.height + spacing)
        imageEdgeInsets = UIEdgeInsets(top: imageOffset, left: 0.0, bottom: 0.0, right: -titleSize.width)
        
        contentEdgeInsets = UIEdgeInsets(top: spacing, left: 0.0, bottom: 0.0, right: 0.0)
    }
}
