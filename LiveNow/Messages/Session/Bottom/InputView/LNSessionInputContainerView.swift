//
//  LNSessionInputContainerView.swift
//  LiveNow
//
//  Created by po on 2025/8/16.
//

import UIKit

class LNSessionInputContainerView: UIView {

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        backgroundColor = .white
        addSubview(inputTextField)
        addSubview(sendButton)
        
        inputTextField.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(15)
            make.centerY.equalToSuperview()
            make.right.equalTo(sendButton.snp.left).offset(-10)
            make.height.equalTo(40)
        }
        
        sendButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-15)
            make.centerY.equalToSuperview()
            make.width.equalTo(100)
            make.height.equalTo(40)
        }
    }
    private lazy var inputTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "Say Something"
        textField.font = LNFont.regular(16)
        textField.backgroundColor = UIColor.lightGray.withAlphaComponent(0.1)
        textField.layer.cornerRadius = 20
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 10, height: 40))
        textField.leftViewMode = .always
        textField.rightView = emojiButton
        textField.rightViewMode = .always
        return textField
    }()
    
    private lazy var emojiButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(systemName: "face.smiling"), for: .normal)
        button.tintColor = .gray
        button.frame = CGRect(x: 0, y: 0, width: 40, height: 40)
        return button
    }()
    
    private lazy var sendButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Send", for: .normal)
        button.setImage(UIImage(systemName: "paperplane.fill"), for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.backgroundColor = UIColor.hex(hexString: "#00DFAB")
        button.tintColor = .white
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 20
        button.imageEdgeInsets = UIEdgeInsets(top: 0, left: -5, bottom: 0, right: 0)
        button.titleEdgeInsets = UIEdgeInsets(top: 0, left: 5, bottom: 0, right: 0)
        return button
    }()
    /*
    // Only override draw() if you perform custom drawing.
    // An empty implementation adversely affects performance during animation.
    override func draw(_ rect: CGRect) {
        // Drawing code
    }
    */

}
