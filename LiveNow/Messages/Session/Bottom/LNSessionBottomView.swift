//
//  LNSessionBottomView.swift
//  LiveNow
//
//  Created by po on 2025/8/17.
//

import UIKit

protocol LNSessionBottomViewDelegate: AnyObject {
    // 展开或收起语音录制面板
    func sessionBottomView(_ bottomView: LNSessionBottomView, didToggleVoicePanel expanded: Bool)
    // 开始录音
    func sessionBottomViewDidStartRecording(_ bottomView: LNSessionBottomView)
    // 结束录音
    func sessionBottomViewDidFinishRecording(_ bottomView: LNSessionBottomView, duration: TimeInterval)
    // 取消录音
    func sessionBottomViewDidCancelRecording(_ bottomView: LNSessionBottomView)
    // 工具栏按钮点击
    func sessionBottomView(_ bottomView: LNSessionBottomView, didSelectToolWithType type: LNSessionToolbarView.BottonBtnEventType)
}

class LNSessionBottomView: UIView {
    
    weak var delegate: LNSessionBottomViewDelegate?
    // 是否处于展开状态
    private var isExpanded = false
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .white
        setupUI()
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    func setupUI() {
        addSubview(inputContainerView)
        addSubview(toolbarView)
        addSubview(voiceRecordView)
        
        // 底部输入区域
        inputContainerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(s(60))
        }
        // 底部工具栏
        toolbarView.snp.makeConstraints { make in
            make.top.equalTo(inputContainerView.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(s(50))
        }
        voiceRecordView.snp.makeConstraints { make in
            make.top.equalTo(toolbarView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(self.safeAreaLayoutGuide.snp.bottom)
        }
    }
    private func toggleVoicePanel(expanded: Bool) {
        isExpanded = expanded
        // 显示或隐藏语音录制视图
        voiceRecordView.isHidden = !expanded
        // 更新视图高度
        let totalHeight = s(isExpanded ? 300 : 110) // 110 = 60(输入区域) + 50(工具栏)
        
        // 通知父视图更新我们的高度约束
        if let superview = self.superview {
            self.snp.updateConstraints { make in
                make.height.equalTo(s(totalHeight))
            }
            superview.setNeedsLayout()
            // 执行动画
            UIView.animate(withDuration: 0.3, animations: {
                superview.layoutIfNeeded()
            })
        }
        // 如果展开则准备录音视图
        if expanded {
            self.delegate?.sessionBottomView(self, didToggleVoicePanel: true)
            voiceRecordView.prepareForRecording()
        }else {
            self.delegate?.sessionBottomView(self, didToggleVoicePanel: false)
        }
    }
    // 底部输入区域
    private lazy var inputContainerView: LNSessionInputContainerView = {
        let view = LNSessionInputContainerView()
        view.backgroundColor = .white
        return view
    }()
    // 底部功能栏
    private lazy var toolbarView: LNSessionToolbarView = {
        let view = LNSessionToolbarView()
        view.backgroundColor = .white
        view.sessionToolEvent = { [weak self] type in
            guard let self = self else { return }
            switch type {
            case .voice:
                toggleVoicePanel(expanded: !isExpanded)
                break
            case .image:
                break
            case .video:
                break
            case .gift:
                
                break
            }
        }
        return view
    }()
    private lazy var voiceRecordView: LNSessionVoiceRecordView = {
        let view = LNSessionVoiceRecordView()
        view.backgroundColor = .white
        view.isHidden = true
        // 设置语音录制视图的回调
        view.startRecordingHandler = { [weak self] in
            guard let self = self else { return }
            self.delegate?.sessionBottomViewDidStartRecording(self)
        }
        view.finishRecordingHandler = { [weak self] duration in
            guard let self = self else { return }
            self.delegate?.sessionBottomViewDidFinishRecording(self, duration: duration)
            // 录音完成后收起面板
            self.toggleVoicePanel(expanded: false)
        }
        view.cancelRecordingHandler = { [weak self] in
            guard let self = self else { return }
            self.delegate?.sessionBottomViewDidCancelRecording(self)
        }
        return view
    }()
    /*
    // Only override draw() if you perform custom drawing.
    // An empty implementation adversely affects performance during animation.
    override func draw(_ rect: CGRect) {
        // Drawing code
    }
    */

}
