//
//  LNSessionViPLockView.swift
//  LiveNow
//
//  Created by po on 2025/8/16.
//

import UIKit

class LNSessionViPLockView: UIView {

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    func setupUI() {
        layer.cornerRadius = 12
        // 添加渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.hex(hexString: "#FFAA77").cgColor,
            UIColor.hex(hexString: "#FF7799").cgColor
        ]
        gradientLayer.locations = [0.0, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1.0, y: 0.5)
        gradientLayer.cornerRadius = 12
        layer.addSublayer(gradientLayer)
        
        vipIconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(15)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(30)
        }
        
        vipLabel.snp.makeConstraints { make in
            make.left.equalTo(vipIconImageView.snp.right).offset(10)
            make.centerY.equalToSuperview()
            make.right.lessThanOrEqualTo(openNowButton.snp.left).offset(-10)
        }
        
        openNowButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-15)
            make.centerY.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(36)
        }
        
    }
    private lazy var vipIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "crown.fill")
        imageView.tintColor = .white
        return imageView
    }()
    
    private lazy var vipLabel: UILabel = {
        let label = UILabel()
        label.text = "Purchase VIP To Unlock\nThe Chat Function"
        label.font = LNFont.medium(16)
        label.textColor = .white
        label.numberOfLines = 2
        return label
    }()
    
    private lazy var openNowButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Open Now", for: .normal)
        button.titleLabel?.font = LNFont.bold(16)
        button.backgroundColor = .yellow
        button.setTitleColor(.black, for: .normal)
        button.layer.cornerRadius = 18
        return button
    }()
    
    
    /*
    // Only override draw() if you perform custom drawing.
    // An empty implementation adversely affects performance during animation.
    override func draw(_ rect: CGRect) {
        // Drawing code
    }
    */

}
