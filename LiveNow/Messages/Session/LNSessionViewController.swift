//
//  LNSessionViewController.swift
//  LiveNow
//
//  Created by po on 2025/8/16.
//

import UIKit
import SnapKit

class LNSessionViewController: LNBaseController {
    
    // MARK: - 模型
    struct Message {
        enum MessageType {
            case text
            case image
            case video
        }
        
        enum MessageDirection {
            case sent
            case received
        }
        
        let content: String
        let type: MessageType
        let direction: MessageDirection
        let time: String
        let mediaURL: String?
        
        init(content: String, type: MessageType = .text, direction: MessageDirection, time: String, mediaURL: String? = nil) {
            self.content = content
            self.type = type
            self.direction = direction
            self.time = time
            self.mediaURL = mediaURL
        }
    }
    //自定义导航栏
    private lazy var navView: LNSessionNavView = {
        let view = LNSessionNavView()
        view.backgroundColor = UIColor.white
        view.backButtonAction = { [weak self] in
            guard let self = self else { return }
            self.navigationController?.popViewController(animated: true)
        }
        return view
    }()
    //顶部用户信息卡片
    private lazy var userInfoCardView: LNSessionHeadUserInfoCardView = {
        let cardView = LNSessionHeadUserInfoCardView()
        cardView.backgroundColor = UIColor.white
        cardView.layer.cornerRadius = s(8)
        cardView.layer.masksToBounds = true
        return cardView
    }()
    //底部输入容器视图
    private lazy var bottomContainerView: LNSessionBottomView = {
        let bottomView = LNSessionBottomView()
        bottomView.delegate = self
        return bottomView
    }()
    // 表格视图
    private lazy var tableView: UITableView = {
        let table = UITableView()
        table.backgroundColor = UIColor.clear
        table.separatorStyle = .none
        table.showsVerticalScrollIndicator = false
        table.register(LNSessionTextCell.self, forCellReuseIdentifier: "LNSessionTextCell")
        table.register(LNSessionVideoCell.self, forCellReuseIdentifier: "LNSessionVideoCell")
        table.register(LNSessionTimeCell.self, forCellReuseIdentifier: "LNSessionTimeCell")
        table.delegate = self
        table.dataSource = self
        return table
    }()
    // 聊天数据
    private var messages: [Any] = []
    
    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupMessages()
        setupUI()
        setupConstraints()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
    }
    
    override var prefersNavigationBarHide: Bool {
        return true
    }
    
    // MARK: - 初始化方法
    private func setupUI() {
        view.backgroundColor = UIColor.hex(hexString: "#F5F5F5")
        // 添加导航栏
        view.addSubview(navView)
        // 添加用户信息卡片
        view.addSubview(userInfoCardView)
        // 添加表格视图
        view.addSubview(tableView)
        // 添加底部输入容器视图
        view.addSubview(bottomContainerView)
    }
    
    private func setupConstraints() {
        navView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(knavH)
        }
        // 用户信息卡片
        userInfoCardView.snp.makeConstraints { make in
            make.top.equalTo(navView.snp.bottom).offset(s(12))
            make.left.right.equalToSuperview().inset(s(16))
            make.height.equalTo(s(124))
        }
        bottomContainerView.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(kbottomSafeH+s(50+60)) // 底部安全区高度 + 输入框高度
        }
        // 表格视图
        tableView.snp.makeConstraints { make in
            make.top.equalTo(userInfoCardView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(bottomContainerView.snp.top)
        }
    }
    
    private func setupMessages() {
        // 创建模拟数据
        messages = [
            "time:02-12 11:55",
            Message(content: "Hi~", direction: .received, time: "02-12 11:55"),
            "time:02-12 11:55",
            Message(content: "Hi~", direction: .sent, time: "02-12 11:55"),
            Message(content: "Hi~", direction: .received, time: "02-12 11:55"),
            Message(content: "https://example.com/video.mp4", type: .video, direction: .received, time: "02-12 11:55", mediaURL: "https://example.com/video.mp4")
        ]
    }
}

// MARK: - UITableViewDataSource, UITableViewDelegate
extension LNSessionViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return messages.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let item = messages[indexPath.row]
                
        // 时间
        if item is String && (item as! String).hasPrefix("time:") {
            let cell = tableView.dequeueReusableCell(withIdentifier: "LNSessionTimeCell", for: indexPath) as! LNSessionTimeCell
            let timeString = (item as! String).replacingOccurrences(of: "time:", with: "")
            cell.configure(time: timeString)
            return cell
        }
        
        // 消息
        if let message = item as? Message {
            if message.type == .text {
                let cell = tableView.dequeueReusableCell(withIdentifier: "LNSessionTextCell", for: indexPath) as! LNSessionTextCell
                cell.configure(with: message)
                return cell
            } else {
                let cell = tableView.dequeueReusableCell(withIdentifier: "LNSessionVideoCell", for: indexPath) as! LNSessionVideoCell
                cell.configure(with: message)
                return cell
            }
        }
        
        return UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        let item = messages[indexPath.row]
        if item is String && (item as! String).hasPrefix("time:") {
            return 40 // 时间标签高度
        }
        
        if let message = item as? Message {
            if message.type == .text {
                return 60 // 文本消息高度
            } else {
                return 220 // 媒体消息高度
            }
        }
        
        return 60
    }
}

extension LNSessionViewController: LNSessionBottomViewDelegate {
    func sessionBottomViewDidStartRecording(_ bottomView: LNSessionBottomView) {
        print("开始录音")
    }
    
    func sessionBottomViewDidFinishRecording(_ bottomView: LNSessionBottomView, duration: TimeInterval) {
        print("完成录音，时长: \(duration)秒")
    }
    
    func sessionBottomViewDidCancelRecording(_ bottomView: LNSessionBottomView) {
        print("取消录音")
    }
    
    func sessionBottomView(_ bottomView: LNSessionBottomView, didSelectToolWithType type: LNSessionToolbarView.BottonBtnEventType) {
        // 处理工具栏按钮点击事件
    }
    
    func sessionBottomView(_ bottomView: LNSessionBottomView, didToggleVoicePanel expanded: Bool) {
        // 如果展开了录音面板，滚动到最底部
        if expanded && !messages.isEmpty {
            // 使用动画滚动到底部
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                let lastRow = self.tableView.numberOfRows(inSection: 0) - 1
                if lastRow >= 0 {
                    let indexPath = IndexPath(row: lastRow, section: 0)
                    self.tableView.scrollToRow(at: indexPath, at: .bottom, animated: true)
                }
            }
        }
    }
}
