//
//  LNSessionNavView.swift
//  LiveNow
//
//  Created by po on 2025/8/16.
//

import UIKit

class LNSessionNavView: UIView {
    // 定义回调闭包类型
    typealias BackButtonActionBlock = () -> Void
    
    // 声明回调闭包属性
    var backButtonAction: BackButtonActionBlock?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        backgroundColor = .white
        addSubview(backButton)
        addSubview(titleLabel)
        addSubview(callButton)
        addSubview(scrollButton)
        addSubview(moreButton)
        
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalToSuperview().offset(kstatusBarH)
            make.width.height.equalTo(44)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(backButton.snp.right)
            make.centerY.equalTo(backButton)
        }
        
        callButton.snp.makeConstraints { make in
            make.right.equalTo(scrollButton.snp.left).offset(-s(19))
            make.centerY.equalTo(backButton)
            make.width.height.equalTo(s(32))
        }
        
        scrollButton.snp.makeConstraints { make in
            make.right.equalTo(moreButton.snp.left).offset(-s(13))
            make.centerY.equalTo(backButton)
            make.width.height.equalTo(s(24))
        }
        
        moreButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(15))
            make.centerY.equalTo(backButton)
            make.width.height.equalTo(s(24))
        }
    }
    
    // MARK: - 公共方法
    
    /// 设置标题
    func setTitle(_ title: String) {
        titleLabel.text = title
    }
    
    // MARK: - 事件处理
    @objc private func backButtonTapped() {
        // 调用回调闭包通知控制器
        backButtonAction?()
    }
    
    private lazy var backButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(systemName: "chevron.left"), for: .normal)
        button.tintColor = .black
        button.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "NAME"
        label.font = LNFont.bold(18)
        label.textColor = .black
        return label
    }()
    
    private lazy var callButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "ic_session_call"), for: .normal)
        button.tintColor = .white
        button.layer.cornerRadius = 20
        return button
    }()
    
    private lazy var scrollButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "ic_session_upload"), for: .normal)
        return button
    }()
    
    private lazy var moreButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "ic_session_more"), for: .normal)
        return button
    }()
}
