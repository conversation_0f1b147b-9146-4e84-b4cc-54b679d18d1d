//
//  LNSessionHeadUserInfoCardView.swift
//  LiveNow
//
//  Created by po on 2025/8/16.
//

import UIKit
import JKSwiftExtension

class LNSessionHeadUserInfoCardView: UIView {
    private let cardView = UIView()
    private let avatarImageView = UIImageView()
    private let nameLabel = UILabel()
    private let sexAndAge = LNSexAndAgeView()
    private let photoCollectionView = UICollectionView(frame: .zero, collectionViewLayout: UICollectionViewFlowLayout())
    private let followButton = UIButton()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupCell()
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    override func layoutSubviews() {
        super.layoutSubviews()
        cardView.jk.gradientColor(.vertical, [UIColor.hex(hexString: "#FFFFFF").cgColor, UIColor.hex(hexString: "#E8FFF6").cgColor], [0.0, 1.0])
        followButton.jk.gradientColor(.horizontal, [UIColor.hex(hexString: "#04E798").cgColor, UIColor.hex(hexString: "#0ADCE1").cgColor], [0.0, 1.0])
    }
    private func setupCell() {
        backgroundColor = .white
        cardView.layer.cornerRadius = 12
        self.addSubview(cardView)
        // 头像
        avatarImageView.backgroundColor = UIColor.lightGray.withAlphaComponent(0.5)
        avatarImageView.layer.cornerRadius = 25
        avatarImageView.clipsToBounds = true
        addSubview(avatarImageView)
        
        // 名称
        nameLabel.text = "NAME"
        nameLabel.font = LNFont.bold(18)
        sexAndAge.setUIWithSex(1, 23)
        let tempview = UIView()
        addSubview(tempview)
        tempview.addSubview(nameLabel)
        tempview.addSubview(sexAndAge)
        
        // 照片布局
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 8
        layout.itemSize = CGSize(width: 60, height: 60)
        
        photoCollectionView.backgroundColor = .clear
        photoCollectionView.collectionViewLayout = layout
        photoCollectionView.register(PhotoCell.self, forCellWithReuseIdentifier: "PhotoCell")
        photoCollectionView.dataSource = self
        photoCollectionView.delegate = self
        photoCollectionView.isScrollEnabled = false
        addSubview(photoCollectionView)
        
        // 关注按钮
        followButton.setTitle("+ Follow", for: .normal)
        followButton.titleLabel?.font = LNFont.medium(14)
        followButton.backgroundColor = UIColor.hex(hexString: "#00DFAB")
        followButton.setTitleColor(.white, for: .normal)
        followButton.layer.cornerRadius = 18
        addSubview(followButton)
        
        // 布局约束
        cardView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(12)
            make.width.height.equalTo(50)
        }
        tempview.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(10)
            make.centerY.equalTo(avatarImageView)
        }
        nameLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(20)
        }
        sexAndAge.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(6)
            make.left.bottom.equalToSuperview()
        }
        followButton.snp.makeConstraints { make in
            make.centerY.equalTo(nameLabel)
            make.right.equalToSuperview().offset(-12)
            make.width.equalTo(100)
            make.height.equalTo(36)
        }
        
        photoCollectionView.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(10)
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(60)
        }
    }
}

extension LNSessionHeadUserInfoCardView: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return 3 // 显示3张照片
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "PhotoCell", for: indexPath) as! PhotoCell
        return cell
    }
}

class PhotoCell: UICollectionViewCell {
    private let imageView = UIImageView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupCell() {
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8
        imageView.backgroundColor = UIColor.lightGray.withAlphaComponent(0.3)
        contentView.addSubview(imageView)
        
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    func configure(with image: UIImage?) {
        imageView.image = image
    }
}
