//
//  LNSexAndAgeView.swift
//  LiveNow
//
//  Created by po on 2025/8/17.
//

import UIKit

class LNSexAndAgeView: UIView {
    var sexIcon: UIImageView = UIImageView()
    var ageLabel: UILabel = UILabel()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setUI()
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    func setUI() {
        backgroundColor = UIColor.hex(hexString: "#64B1FF")
        addSubview(sexIcon)
        addSubview(ageLabel)
        sexIcon.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(3))
            make.bottom.equalToSuperview().offset(s(-3))
            make.left.equalToSuperview().offset(s(5))
        }
        sexIcon.image = UIImage(named: "ic_session_sex_gril")
        
        ageLabel.snp.makeConstraints { make in
            make.centerY.equalTo(sexIcon)
            make.left.equalTo(sexIcon.snp.right).offset(s(2))
            make.right.equalToSuperview().offset(s(-4.5))
        }
        ageLabel.text = "22"
        ageLabel.font = LNFont.medium(14)
        ageLabel.textColor = .white
        ageLabel.clipsToBounds = true
        
    }
     
    func setUIWithSex(_ gender:Int, _ age:Int) {
        ageLabel.text = "\(age)"

        if gender == 1 {
            // 男性
            backgroundColor = UIColor.hex(hexString: "#77DBFF")
            sexIcon.image = UIImage(named: "ic_male")
        } else {
            // 女性（gender == 2 或其他值）
            backgroundColor = UIColor.hex(hexString: "#FF77C9")
            sexIcon.image = UIImage(named: "ic_female")
        }
    }
    override func layoutSubviews() {
        super.layoutSubviews()
        layer.cornerRadius = bounds.height / 2
    }
    /*
    // Only override draw() if you perform custom drawing.
    // An empty implementation adversely affects performance during animation.
    override func draw(_ rect: CGRect) {
        // Drawing code
    }
    */

}
