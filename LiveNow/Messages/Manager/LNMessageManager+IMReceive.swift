//
//  LNMessageManager+IMReceive.swift
//  LiveNow
//
//  Created by po on 2025/8/24.
//

import Foundation
import RongIMLibCore

extension LNMessageManager: RCIMClientReceiveMessageDelegate {
    //收消息回调
    func onReceived(_ message: RCMessage, left nLeft: Int32, object: Any?, offline: Bool, hasPackage: Bool) {
        print("接收回调的消息id为：\(String(describing: message.messageUId)),object==\(String(describing: message.objectName))")
        let msgName = message.objectName ?? ""
        //处理自定义消息逻辑
        if msgName.isSame(to: "") {
            
        }
        NotificationCenter.default.post(name: LNIMReceiveMessageNotification, object: message)
    }
}
