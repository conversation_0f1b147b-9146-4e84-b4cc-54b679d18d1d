//
//  LNMessageSendManager.swift
//  LiveNow
//
//  Created by po on 2025/8/24.
//  消息的发送

import UIKit
import RongIMLibCore
import JKSwiftExtension

class LNMessageSendManager: NSObject {

    //1v1私聊 发送文本消息
    static func sendTextMessage(targetId:String, text: String = "") {
        //组装内容模型
        let txtMessage = RCTextMessage(content: text)
        //组装消息模型
        let message = RCMessage(type: .ConversationType_PRIVATE, targetId: targetId, direction: .MessageDirection_SEND, content: txtMessage)
        //组装发送人用户信息
        createMessageSenderInfo(message: message)
        RCCoreClient.shared().send(message, pushContent: nil, pushData: nil) { msg in
            
        } successBlock: { msg in
            
        }

    }
    //1v1私聊 发送图片消息
    static func sendPhotoMessage(targetId:String, image: UIImage) {
        let imageMessage = RCImageMessage(image: image)
        imageMessage.isFull = true
        let message = RCMessage(type: RCConversationType.ConversationType_PRIVATE, targetId: targetId, direction: RCMessageDirection.MessageDirection_SEND, content: imageMessage)
        createMessageSenderInfo(message: message)
        RCCoreClient.shared().sendMediaMessage(message, pushContent: nil, pushData: nil, attached: nil) { progress, _ in
            Log("IM 图片上传进度\(progress)")
        } successBlock: { msg in
            
        } errorBlock: { error, msg in
            Log("IM 图片发送失败\(error)")
        }

    }
    //1v1私聊 发送语音消息
    static func sendVoiceMessage(targetId:String, filePath: String, duration: Int) {
        
    }
    //封装发送人用户信息
    static func createMessageSenderInfo(message: RCMessage) {
        if let user = LNUserManager.shared.userModel {
            let userInfo = RCUserInfo(userId: "\(user.id)", name: user.nickName, portrait: user.headFileName)
            userInfo.extra = "\(user.gender)"
            message.content?.senderUserInfo = userInfo
            message.sentTime = Int64(JKPOP.milliStamp)!
        }
    }
}
