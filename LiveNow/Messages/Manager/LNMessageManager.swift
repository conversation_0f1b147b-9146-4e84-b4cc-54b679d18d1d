//
//  LNMessageManager.swift
//  LiveNow
//
//  Created by po on 2025/8/23.
//

import UIKit
import RongIMLibCore
class LNMessageManager: NSObject {
    static let shared = LNMessageManager()
    private override init() {}
    
    func configIMSDK() {
        let options = RCInitOption()
        //数据中心
        options.naviServer = kRongIMUrl
        //初始化IM
        RCCoreClient.shared().initWithAppKey(kRongIMKey, option: options)
        RCCoreClient.shared().logLevel = .log_Level_Verbose
        RCCoreClient.shared().addConnectionStatusChangeDelegate(self)
        RCCoreClient.shared().addReceiveMessageDelegate(self)
        //敏感词拦截
        RCCoreClient.shared().messageBlockDelegate = self
        RCCoreClient.shared().voiceMsgType = RCVoiceMessageType.highQuality
    }
    func loginIMSDK() {
        guard let userData = LNUserManager.shared.userModel else { return }
        if userData.imToken.count > 0 {
            loginIMWithIMToken(imToken: userData.imToken)
        }else {
            getIMToken()
        }
    }
    func logoutIM() {
        //true断开连接后 接收远程推送，false 表示不接受推送
        RCCoreClient.shared().disconnect(true)
    }
    func getIMToken() {
        NetWorkRequest(LNApiProfile.getRongCloudToken(par: [:])) { result in
            guard let imToken = result["data"] as? String else {return}
            guard let code = result["code"] as? Int else {return}
            if code == 200 {
                self.loginIMWithIMToken(imToken: imToken)
            }else{
                print("msg--融云获取Token失败 code--\(code)")
                
            }
        } failure: { error in
            print("msg--融云获取Token失败")
            DispatchQueue.main.asyncAfter(deadline: .now()+3) {
                self.getIMToken()
            }
            
        }
    }
    private func loginIMWithIMToken(imToken:String) {
        guard let userData = LNUserManager.shared.userModel else { return }
        RCCoreClient.shared().connect(withToken: imToken) { code in
            print("msg--融云数据库打开")
        } success: { uid in
            print("msg--融云登陆成功-uid-\(String(describing: uid))")
            
            if let user = LNUserManager.shared.userModel {
                //更新本地IMToken
                user.imToken = imToken
                LNUserManager.shared.saveUserInfoToDisk(user: user)
                //登录成功后将用户信息传给融云
                let tempUserInfo = RCUserInfo(userId: uid, name: user.nickName, portrait: user.headFileName)
                RCCoreClient.shared().currentUserInfo = tempUserInfo
            }
        } error: { errorCode in
            print("msg--融云登陆失败-erroeCode-\(errorCode.rawValue)-uid-\(userData.id)")
            if errorCode == RCConnectErrorCode.RC_CONN_TOKEN_INCORRECT || errorCode == RCConnectErrorCode.RC_CONN_TOKEN_EXPIRE {
                DispatchQueue.main.asyncAfter(deadline: .now()+10) {
                    self.getIMToken()
                }
            }
        }

    }
    
}

extension LNMessageManager: RCConnectionStatusChangeDelegate {
    func onConnectionStatusChanged(_ status: RCConnectionStatus) {
        print("msg--IM连接状态-\(status.rawValue)")
        switch status {
        case .ConnectionStatus_Connected:
            //连接成功
            print("msg--IM连接成功")
        case .ConnectionStatus_Connecting:
            //
            print("msg--IM连接中")
        case .ConnectionStatus_Unconnected:
            print("msg--IM连接失败")
            if let user = LNUserManager.shared.userModel {
                ///重新登录
                DispatchQueue.main.asyncAfter(deadline: .now()+3) {
                    self.loginIMWithIMToken(imToken: user.imToken)
                }
            }
        case .ConnectionStatus_Suspend:
            print("msg--IM连接挂起，自动重连")
        case .ConnectionStatus_Timeout:
            print("msg--IM连接超时，不在重新连接")
            if let user = LNUserManager.shared.userModel {
                ///重新登录
                DispatchQueue.main.asyncAfter(deadline: .now()+3) {
                    self.loginIMWithIMToken(imToken: user.imToken)
                }
            }
        case .ConnectionStatus_SignOut:
            print("msg--IM退出登录")
        case .ConnectionStatus_TOKEN_INCORRECT:
            print("msg--IMToken无效")
            if let user = LNUserManager.shared.userModel {
                ///重新登录
                DispatchQueue.main.asyncAfter(deadline: .now()+3) {
                    self.getIMToken()
                }
            }
        case .ConnectionStatus_DISCONN_EXCEPTION:
            print("msg--IM用户被封禁")
        case .ConnectionStatus_KICKED_OFFLINE_BY_OTHER_CLIENT:
            print("msg--IM当前用户在其他设备登录，此设备被踢下线")
        case .ConnectionStatus_NETWORK_UNAVAILABLE:
            print("msg--IM连接中网络不可用")
        default:
            break
        }
    }

}

extension LNMessageManager: RCMessageBlockDelegate {
    func messageDidBlock(_ blockedMessageInfo: RCBlockedMessageInfo) {
        //消息拦截
    }
}
