//
//  LNMessageHandle.swift
//  LiveNow
//
//  Created by po on 2025/8/24.
//

import UIKit
import Foundation
import RongIMLibCore

class LNMessageHandle: NSObject {
    
    static func messageHandle(message: RCMessage, userType: LNMessageUserType, avator: String, nickname: String, hiddenTime: Bool, sendStatus: Int, progress: Double = 1.0) -> LNBaseMessageModel? {
        
        var baseMessage: LNBaseMessage = LNBaseMessage(time: "", avator: avator, type: .LNMessageContentTypeText, userType: userType, sendStatus: 0, hideTime: hiddenTime)
        let timeString = Date.timeString(timeInterval: Double(message.sentTime))
        let messageName = message.objectName ?? ""
        if messageName.isSame(to: "RC:TxtMsg") { //文本消息
            
        }else if messageName.isSame(to: "RC:ImgMsg") { //图片消息
            
        }else if messageName.isSame(to: "RC:HQVCMsg") { //语音消息
            
        }else if messageName.isSame(to: "RC:SightMsg") { //视频消息
            
        }else if messageName.isSame(to: "giftMsg") { //礼物消息
            
        }else {
            return nil
        }
        
        let messageM = LNBaseMessageModel()
        messageM.messageModel = baseMessage
        messageM.originalMessage = message
        return messageM
    }
}
