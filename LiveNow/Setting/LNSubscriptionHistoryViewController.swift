//
//  LNSubscriptionHistoryViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/17.
//

import UIKit
import SnapKit
import MJRefresh

/// 订阅历史记录（Subscription History）
/// - 卡片式列表：左侧VIP类型，右侧价格；下方时间与状态（红色 Unfinished/绿色 Finished）
/// - 底部加载状态由 MJRefresh 处理
class LNSubscriptionHistoryViewController: LNBaseController, UITableViewDataSource, UITableViewDelegate {

    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }

    // 使用顶部渐变背景
    override var useTopGradientBackground: Bool { return true }

    // MARK: - Data
    private var subscriptionList: [LNSubscriptionHistory] = []
    private var data: [LNSubscriptionRecord] = []

    // 分页与加载状态
    private var page: Int = 1
    private let pageSize: Int = 10
    private var hasMore: Bool = true

    // MARK: - UI
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.dataSource = self
        tv.delegate = self
        tv.separatorStyle = .none
        tv.backgroundColor = UIColor.systemGroupedBackground
        tv.register(LNSubscriptionHistoryCell.self, forCellReuseIdentifier: LNSubscriptionHistoryCell.reuseId)
        tv.rowHeight = s(68)

        // 配置空状态
        tv.em.emptyView = LNEmptyView.empty(
            firstReloadHidden: true,
            canTouch: false,
            isUserInteractionEnabled: false,
            offsetY: -s(80),
            space: s(15),
            backColor: .clear
        ) { config in
            config.image = UIImage(named: "ic_empty")
            config.imageSize = CGSize(width: s(120), height: s(120))
            config.titleTopSpace = s(20)
            config.title = "No subscription history available."
            config.titleFont = LNFont.regular(16)
            config.titleColor = UIColor.systemGray
        } closure: { tag in
            print("Empty view tapped with tag: \(tag)")
        }

        return tv
    }()
    

    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Subscription History"
        edgesForExtendedLayout = .all

        view.addSubview(tableView)
        tableView.snp.makeConstraints { $0.edges.equalTo(view.safeAreaLayoutGuide) }
        setupRefresh()
        loadData(reset: true)
    }

    private func setupRefresh() {
        tableView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(onRefresh))
        tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(onLoadMore))
    }

    @objc private func onRefresh() {
        loadData(reset: true)
    }

    @objc private func onLoadMore() {
        loadData(reset: false)
    }

    private func loadData(reset: Bool) {
        if reset {
            page = 1
            hasMore = true
            tableView.mj_footer?.resetNoMoreData()
        }

        // 调用真实API获取订阅历史
        LNSubscriptionHistoryManager.shared.fetchSubscriptionHistoryList(
            page: page,
            size: pageSize,
            completion: { [weak self] subscriptionList in
                guard let self = self else { return }

                // 更新数据
                if reset {
                    self.subscriptionList = subscriptionList
                    self.data = subscriptionList.map { LNSubscriptionRecord(from: $0) }
                } else {
                    self.subscriptionList.append(contentsOf: subscriptionList)
                    let newRecords = subscriptionList.map { LNSubscriptionRecord(from: $0) }
                    self.data.append(contentsOf: newRecords)
                }

                // 更新分页状态
                self.page += 1
                self.hasMore = subscriptionList.count >= self.pageSize

                // 更新UI
                self.tableView.reloadData()
                self.tableView.mj_header?.endRefreshing()

                // 处理footer状态
                if self.data.isEmpty {
                    self.tableView.mj_footer?.endRefreshing()
                    self.tableView.mj_footer?.isHidden = true
                } else {
                    if self.hasMore {
                        self.tableView.mj_footer?.endRefreshing()
                        self.tableView.mj_footer?.isHidden = false
                    } else {
                        self.tableView.mj_footer?.endRefreshingWithNoMoreData()
                        self.tableView.mj_footer?.isHidden = false
                    }
                }
            },
            failure: { [weak self] error in
                guard let self = self else { return }

                // 处理错误
                self.tableView.mj_header?.endRefreshing()
                self.tableView.mj_footer?.endRefreshing()

                // 如果数据为空，隐藏footer
                if self.data.isEmpty {
                    self.tableView.mj_footer?.isHidden = true
                }

                // 显示错误提示
                print("订阅历史加载失败: \(error.localizedDescription)")
                // 可以添加Toast提示
            }
        )
    }

    // MARK: - Table Datasource
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { data.count }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: LNSubscriptionHistoryCell.reuseId, for: indexPath) as! LNSubscriptionHistoryCell
        let record = data[indexPath.row]
        cell.configure(vipType: record.vipType, priceUSD: record.priceUSD, time: record.time, finished: record.status == .finished)
        return cell
    }

    // Cell 已抽出为独立文件 LNSubscriptionHistoryCell.swift
}
