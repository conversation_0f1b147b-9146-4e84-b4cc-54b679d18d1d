//
//  LNSubscriptionHistoryViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/17.
//

import UIKit
import SnapKit
import MJRefresh

/// 订阅历史记录（Subscription History）
/// - 卡片式列表：左侧VIP类型，右侧价格；下方时间与状态（红色 Unfinished/绿色 Finished）
/// - 底部加载状态由 MJRefresh 处理
class LNSubscriptionHistoryViewController: LNBaseController, UITableViewDataSource, UITableViewDelegate {

    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }

    // 使用顶部渐变背景
    override var useTopGradientBackground: Bool { return true }

    // MARK: - Model
    struct Record { let vipType: String; let priceUSD: String; let time: String; let status: Status }
    enum Status { case finished, unfinished }

    private var data: [Record] = [
        .init(vipType: "Bronze VIP", priceUSD: "US$4.99", time: "2025-01-02 16:25:45", status: .unfinished),
        .init(vipType: "Bronze VIP", priceUSD: "US$4.99", time: "2025-01-02 16:25:45", status: .unfinished),
        .init(vipType: "Bronze VIP", priceUSD: "US$4.99", time: "2025-01-02 16:25:45", status: .unfinished),
        .init(vipType: "Bronze VIP", priceUSD: "US$4.99", time: "2025-01-02 16:25:45", status: .finished),
        .init(vipType: "Bronze VIP", priceUSD: "US$4.99", time: "2025-01-02 16:25:45", status: .finished),
        .init(vipType: "Bronze VIP", priceUSD: "US$4.99", time: "2025-01-02 16:25:45", status: .finished)
    ]

    // 分页与加载状态
    private var page: Int = 1
    private let pageSize: Int = 10
    private var hasMore: Bool = true

    // MARK: - UI
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.dataSource = self
        tv.delegate = self
        tv.separatorStyle = .none
        tv.backgroundColor = UIColor.systemGroupedBackground
        tv.register(LNSubscriptionHistoryCell.self, forCellReuseIdentifier: LNSubscriptionHistoryCell.reuseId)
        tv.rowHeight = s(68)
        return tv
    }()
    

    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Subscription History"
        edgesForExtendedLayout = .all

        view.addSubview(tableView)
        tableView.snp.makeConstraints { $0.edges.equalTo(view.safeAreaLayoutGuide) }
        setupRefresh()
    }

    private func setupRefresh() {
        tableView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(onRefresh))
        tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(onLoadMore))
    }

    @objc private func onRefresh() {
        loadData(reset: true)
    }

    @objc private func onLoadMore() {
        loadData(reset: false)
    }

    private func loadData(reset: Bool) {
        if reset {
            page = 1
            hasMore = true
            tableView.mj_footer?.resetNoMoreData()
        }

        // 模拟网络请求与分页数据，这里可替换为真实接口
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) { [weak self] in
            guard let self = self else { return }

            let newItems: [Record] = (0..<self.pageSize).map { _ in
                let finished = Bool.random()
                return .init(vipType: ["Bronze VIP", "Silver VIP", "Gold VIP"].randomElement()!,
                             priceUSD: ["US$4.99","US$9.99","US$19.99"].randomElement()!,
                             time: "2025-01-02 16:25:45",
                             status: finished ? .finished : .unfinished)
            }

            if reset {
                self.data = newItems
            } else {
                self.data.append(contentsOf: newItems)
            }

            self.page += 1
            // 假设三页数据
            self.hasMore = self.page <= 3

            self.tableView.reloadData()
            self.tableView.mj_header?.endRefreshing()
            if self.hasMore {
                self.tableView.mj_footer?.endRefreshing()
            } else {
                self.tableView.mj_footer?.endRefreshingWithNoMoreData()
            }
        }
    }

    // MARK: - Table Datasource
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { data.count }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: LNSubscriptionHistoryCell.reuseId, for: indexPath) as! LNSubscriptionHistoryCell
        let r = data[indexPath.row]
        cell.configure(vipType: r.vipType, priceUSD: r.priceUSD, time: r.time, finished: r.status == .finished)
        return cell
    }

    // Cell 已抽出为独立文件 LNSubscriptionHistoryCell.swift
}
