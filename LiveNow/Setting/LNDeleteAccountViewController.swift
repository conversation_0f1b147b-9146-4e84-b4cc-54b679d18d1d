//
//  LNDeleteAccountViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/09.
//

import UIKit
import SnapKit

/// 删除账号页面
/// 显示说明文字卡片，底部灰色禁用按钮“Submit a cancellation request”。
/// 该版本仅做静态展示与占位交互，后续可接入真实提交流程与风控校验。
class LNDeleteAccountViewController: LNBaseController {

    // 使用白色导航栏
    override var navigationSolidColor: UIColor { return .white }
    override var navigationTitleColor: UIColor { return .label }

    private lazy var scrollView: UIScrollView = {
        let v = UIScrollView()
        v.alwaysBounceVertical = true
        v.keyboardDismissMode = .onDrag
        return v
    }()
    private lazy var contentView = UIView()

    private lazy var infoCard: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.white
        v.layer.cornerRadius = 12
        v.layer.masksToBounds = true
        return v
    }()

    private lazy var infoLabel: UILabel = {
        let l = UILabel()
        l.text = "Fill Out And Submit A Cancellation Request. Our Staff Will Assist You With The Cancellation Process. After You Voluntarily Cancel, We Will Stop Providing Services And Delete Your Personal Information Or Anonymize It In Accordance With Applicable Laws."
        l.font = LNFont.forTextStyle(.body)
        l.textColor = UIColor.label
        l.numberOfLines = 0
        l.adjustsFontForContentSizeCategory = true
        l.setContentHuggingPriority(.required, for: .vertical)
        return l
    }()

    private lazy var submitButton: UIButton = {
        let b = UIButton(type: .custom)
        b.setTitle("Submit a cancellation request", for: .normal)
        b.setTitleColor(.white, for: .normal)
        b.setTitleColor(UIColor.white.withAlphaComponent(0.6), for: .disabled)
        b.backgroundColor = UIColor.systemRed
        b.layer.cornerRadius = s(24)
        b.layer.masksToBounds = true
        b.titleLabel?.font = LNFont.forTextStyle(.headline)
        b.accessibilityLabel = "submit_cancel_request"
        b.addTarget(self, action: #selector(submitTapped), for: .touchUpInside)
        return b
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Delete account"
        setupUI()
        setupConstraints()
    }

    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        view.addSubview(submitButton)

        contentView.addSubview(infoCard)
        infoCard.addSubview(infoLabel)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalTo(view.safeAreaLayoutGuide)
            make.bottom.equalTo(submitButton.snp.top).offset(-16)
        }
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        infoCard.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(16))
            make.left.right.equalToSuperview().inset(s(16))
            make.bottom.equalToSuperview().offset(-s(16))
        }
        infoLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(12)
        }

        submitButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(24)
            make.height.equalTo(48)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-24)
        }
    }

    @objc private func submitTapped() {
        // 显示确认对话框
        let alert = UIAlertController(
            title: "确认注销账号",
            message: "注销后将删除您的个人信息，此操作不可恢复。确定要继续吗？",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "确认注销", style: .destructive) { [weak self] _ in
            self?.performAccountCancellation()
        })

        present(alert, animated: true)
    }

    /// 执行账号注销
    private func performAccountCancellation() {
        // 禁用按钮，防止重复提交
        submitButton.isEnabled = false
        submitButton.setTitle("正在注销...", for: .normal)


        // 调用注销接口
        NetWorkRequest(LNApiProfile.accountCancellation, completion: { [weak self] result in
            DispatchQueue.main.async {
                self?.handleCancellationSuccess(result: result)
            }
        }, failure: { [weak self] error in
            DispatchQueue.main.async {
                self?.handleCancellationFailure(error: error)
            }
        })
    }

    /// 处理注销成功
    private func handleCancellationSuccess(result: [String: Any]) {
        Log("账号注销成功: \(result)")

        // 重置按钮状态
        submitButton.isEnabled = true
        submitButton.setTitle("Submit a cancellation request", for: .normal)

        // 显示成功提示
        let alert = UIAlertController(
            title: "注销成功",
            message: "您的账号注销申请已提交成功，我们将在24小时内处理您的申请。",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            // 注销成功后，清除用户数据并返回登录页面
            self?.logoutAndReturnToLogin()
        })

        present(alert, animated: true)
    }

    /// 处理注销失败
    private func handleCancellationFailure(error: NSError) {
        Log("账号注销失败: \(error.localizedDescription)")

        // 重置按钮状态
        submitButton.isEnabled = true
        submitButton.setTitle("Submit a cancellation request", for: .normal)

        // 显示错误提示
        let errorMessage = error.localizedDescription.isEmpty ? "注销申请提交失败，请稍后重试" : error.localizedDescription
        let alert = UIAlertController(
            title: "注销失败",
            message: errorMessage,
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    /// 注销并返回登录页面
    private func logoutAndReturnToLogin() {
        // 清除用户数据
        LNUserManager.shared.clearUserInfo()

        // 退出IM登录
        LNMessageManager.shared.logoutIM()

        // 返回到根视图控制器或登录页面
        DispatchQueue.main.async {
            // 发送用户注销通知
            NotificationCenter.default.post(name: NSNotification.Name("UserDidLogout"), object: nil)

            // 返回到根视图控制器
            self.navigationController?.popToRootViewController(animated: true)
        }
    }
}

