//
//  LNRechargeViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/17.
//

import UIKit
import SnapKit

/// 充值页面（Recharge）
/// - 顶部显示当前钻石数量
/// - 充值选项列表，支持折扣标签
/// - 使用渐变背景和卡片式设计
class LNRechargeViewController: LNBaseController {

    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }
    // 使用圆弧形渐变背景
    override var useArcGradientBackground: Bool { return true }

    // MARK: - Data
    private var currentDiamonds: Int = 0
    private var rechargeList: [LNRecharge] = []
    private var rechargeOptions: [LNRechargeOption] = []
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let sv = UIScrollView()
        sv.backgroundColor = .clear
        sv.showsVerticalScrollIndicator = false
        return sv
    }()
    
    private lazy var contentView: UIView = {
        let v = UIView()
        v.backgroundColor = .clear
        return v
    }()
    
    private lazy var diamondHeaderView: UIView = {
        let v = UIView()
        v.backgroundColor = .clear
        v.layer.cornerRadius = s(16)
        v.clipsToBounds = true

        // 使用图片背景
        let backgroundImageView = UIImageView()
        backgroundImageView.image = UIImage(named: "ic_diamond_bg")
        backgroundImageView.contentMode = .scaleAspectFit
        v.addSubview(backgroundImageView)

        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        return v
    }()
    
    private lazy var diamondTitleLabel: UILabel = {
        let l = UILabel()
        l.text = "Available Diamonds"
        l.font = LNFont.medium(16)
        l.textColor = UIColor.hex(hexString: "#333333")
        return l
    }()
    
    private lazy var diamondCountLabel: UILabel = {
        let l = UILabel()
        l.text = "\(currentDiamonds)"
        l.font = LNFont.bold(48)
        l.textColor = UIColor.hex(hexString: "#333333")
        return l
    }()
    
    private lazy var diamondIconView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "ic_diamond")
        iv.contentMode = .scaleAspectFit
        return iv
    }()
    
    private lazy var selectTitleLabel: UILabel = {
        let l = UILabel()
        l.text = "Please Select The Recharge Amount"
        l.font = LNFont.medium(16)
        l.textColor = UIColor.hex(hexString: "#333333")
        return l
    }()
    
    private lazy var optionsStackView: UIStackView = {
        let sv = UIStackView()
        sv.axis = .vertical
        sv.spacing = s(12)
        sv.distribution = .fill
        return sv
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Recharge"
        edgesForExtendedLayout = .all

        setupNavigationBar()
        setupUI()
        loadCurrentDiamonds()
        fetchRechargeList()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
    }

    // MARK: - Navigation Setup
    private func setupNavigationBar() {
        // 创建历史记录按钮
        let recordButton = UIButton(type: .custom)
        recordButton.setImage(UIImage(named: "ic_record"), for: .normal)
        recordButton.frame = CGRect(x: 0, y: 0, width: 24, height: 24)
        recordButton.addTarget(self, action: #selector(recordButtonTapped), for: .touchUpInside)

        // 设置导航栏右侧按钮
        let recordBarButtonItem = UIBarButtonItem(customView: recordButton)
        navigationItem.rightBarButtonItem = recordBarButtonItem
    }

    @objc private func recordButtonTapped() {
        // 处理历史记录按钮点击事件
        showRechargeHistory()
    }

    private func showRechargeHistory() {
        // 跳转到钻石历史记录页面
        let historyVC = LNDiamondHistoryViewController()
        navigationController?.pushViewController(historyVC, animated: true)
    }
    
    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(diamondHeaderView)
        diamondHeaderView.addSubview(diamondTitleLabel)
        diamondHeaderView.addSubview(diamondCountLabel)
        diamondHeaderView.addSubview(diamondIconView)
        
        contentView.addSubview(selectTitleLabel)
        contentView.addSubview(optionsStackView)
        
        setupConstraints()
        setupRechargeOptions()
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        diamondHeaderView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(40))
            make.left.right.equalToSuperview().inset(s(16))
            make.height.equalTo(s(120))
        }
        
        diamondTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.equalToSuperview().offset(s(20))
        }
        
        diamondCountLabel.snp.makeConstraints { make in
            make.top.equalTo(diamondTitleLabel.snp.bottom).offset(s(8))
            make.left.equalTo(diamondTitleLabel)
            make.bottom.equalToSuperview().offset(-s(20))
        }
        
        diamondIconView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(20))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(60))
        }
        
        selectTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(diamondHeaderView.snp.bottom).offset(s(32))
            make.left.equalToSuperview().offset(s(16))
        }
        
        optionsStackView.snp.makeConstraints { make in
            make.top.equalTo(selectTitleLabel.snp.bottom).offset(s(16))
            make.left.right.equalToSuperview().inset(s(16))
            make.bottom.equalToSuperview().offset(-s(20))
        }
    }

    private func setupRechargeOptions() {
        // 清除现有的选项视图
        optionsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        for (index, option) in rechargeOptions.enumerated() {
            let optionView = LNRechargeOptionView()
            let rechargeOption = LNRechargeOptionView.Option(
                diamonds: option.diamonds,
                bonusDiamonds: option.bonusDiamonds,
                priceUSD: option.priceUSD,
                discountLabel: option.discountLabel
            )
            optionView.configure(with: rechargeOption)
            optionView.onTapped = { [weak self] _ in
                self?.handleRecharge(optionIndex: index)
            }
            optionsStackView.addArrangedSubview(optionView)
        }
    }

    private func handleRecharge(optionIndex: Int) {
        guard optionIndex < rechargeOptions.count else { return }
        let option = rechargeOptions[optionIndex]

        // 显示确认弹窗
        let alert = UIAlertController(
            title: "Confirm Purchase",
            message: "Purchase \(option.diamonds) + \(option.bonusDiamonds) diamonds for \(option.priceUSD)?",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Purchase", style: .default) { [weak self] _ in
            self?.processPurchase(optionIndex: optionIndex)
        })

        present(alert, animated: true)
    }

    open func processPurchase(optionIndex: Int) {
        guard optionIndex < rechargeList.count else {
            showErrorAlert(message: "Invalid recharge option")
            return
        }

        let recharge = rechargeList[optionIndex]

        // 显示加载状态
        let loadingAlert = UIAlertController(title: "Processing...", message: nil, preferredStyle: .alert)
        present(loadingAlert, animated: true)

        // 创建订单
        LNRechargeManager.shared.createOrder(
            recharge: recharge,
            completion: { [weak self] result in
                loadingAlert.dismiss(animated: true) {
                    // 处理订单创建成功
                    self?.handleOrderCreated(result: result, recharge: recharge)
                }
            },
            failure: { [weak self] error in
                loadingAlert.dismiss(animated: true) {
                    self?.showErrorAlert(message: "Order creation failed: \(error.localizedDescription)")
                }
            }
        )
    }

    /// 处理订单创建成功
    private func handleOrderCreated(result: [String: Any], recharge: LNRecharge) {
        // 这里应该调用支付SDK进行实际支付
        // 目前模拟支付成功
        currentDiamonds += recharge.totalDiamonds
        diamondCountLabel.text = "\(currentDiamonds)"

        // 显示成功提示
        let successAlert = UIAlertController(
            title: "Purchase Successful!",
            message: "You have received \(recharge.totalDiamonds) diamonds.",
            preferredStyle: .alert
        )
        successAlert.addAction(UIAlertAction(title: "OK", style: .default))
        present(successAlert, animated: true)
    }



    private func loadCurrentDiamonds() {
        // 这里应该从服务器或本地存储加载当前钻石数量
        // 目前使用模拟数据
        currentDiamonds = 0
        diamondCountLabel.text = "\(currentDiamonds)"
    }

    // MARK: - API Methods
    /// 获取充值列表
    private func fetchRechargeList() {
        // 显示加载状态
        showLoadingIndicator()

        LNRechargeManager.shared.fetchRechargeList(
            priceType: "1", // 1-充值，2-订阅，3-充值+订阅
            completion: { [weak self] rechargeList in
                self?.hideLoadingIndicator()
                self?.rechargeList = rechargeList
                self?.updateRechargeOptions()
                self?.setupRechargeOptions()
            },
            failure: { [weak self] error in
                self?.hideLoadingIndicator()
                self?.showErrorAlert(message: error.localizedDescription)
            }
        )
    }

    /// 更新充值选项数据
    private func updateRechargeOptions() {
        rechargeOptions = rechargeList.map { LNRechargeOption(from: $0) }
    }



    /// 显示加载指示器
    private func showLoadingIndicator() {
        // 可以使用项目中的加载组件
        // 这里简单显示在导航栏
        let activityIndicator = UIActivityIndicatorView(style: .medium)
        activityIndicator.startAnimating()
        navigationItem.rightBarButtonItem = UIBarButtonItem(customView: activityIndicator)
    }

    /// 隐藏加载指示器
    private func hideLoadingIndicator() {
        // 恢复历史记录按钮
        setupNavigationBar()
    }

    /// 显示错误提示
    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "Error", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}
