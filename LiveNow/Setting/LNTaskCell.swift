//
//  LNTaskCell.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/14.
//

import UIKit
import SnapKit

// MARK: - Models
struct LNTaskItem { 
    let title: String
    let subtitle: String
    let reward: Int
    var status: LNTaskStatus
}

enum LNTaskStatus { 
    case go, finish 
}

// MARK: - TaskCell
final class LNTaskCell: UITableViewCell {
    static let reuseId = "LNRewardTaskCell"

    var onActionTapped: (() -> Void)?

    private lazy var card: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.white
        return v
    }()
    
    private lazy var diamondIcon: UIImageView = {
        let iv = UIImageView(image: UIImage(named: "ic_diamond"))
        iv.contentMode = .scaleAspectFit
        return iv
    }()
    
    private lazy var rewardLabel: UILabel = {
        let l = UILabel()
        l.textColor = UIColor(hexString: "#00DFAB")
        l.font = LNFont.regular(14)
        l.adjustsFontForContentSizeCategory = true
        return l
    }()

    private lazy var titleLabel: UILabel = {
        let l = UILabel()
        l.font = LNFont.medium(16)
        l.textColor = UIColor.label
        l.adjustsFontForContentSizeCategory = true
        return l
    }()

    private lazy var subtitleLabel: UILabel = {
        let l = UILabel()
        l.font = LNFont.regular(12)
        l.textColor = UIColor(hexString: "#7D7D7D")
        l.adjustsFontForContentSizeCategory = true
        return l
    }()
    
    private lazy var actionButton: UIButton = {
        let b = UIButton(type: .system)
        b.titleLabel?.font = LNFont.regular(12)
        b.setTitleColor(.white, for: .normal)
        b.layer.cornerRadius = s(16)
        b.layer.masksToBounds = true
        b.addTarget(self, action: #selector(actionButtonTapped), for: .touchUpInside)
        return b
    }()
    
    private let actionGradient = CAGradientLayer()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        backgroundColor = .clear
        contentView.backgroundColor = .clear

        contentView.addSubview(card)
        card.addSubview(diamondIcon)
        card.addSubview(rewardLabel)
        card.addSubview(titleLabel)
        card.addSubview(subtitleLabel)
        card.addSubview(actionButton)

        card.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(s(16))
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
        }

        // 钻石图标在左上角
        diamondIcon.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(16))
            make.top.equalToSuperview().offset(s(10))
            make.width.height.equalTo(s(40))
        }

        // 奖励数字在钻石图标下方
        rewardLabel.snp.makeConstraints { make in
            make.centerX.equalTo(diamondIcon)
            make.top.equalTo(diamondIcon.snp.bottom)
        }

        // GO! 按钮在右侧居中
        actionButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(16))
            make.centerY.equalToSuperview()
            make.width.equalTo(s(72))
            make.height.equalTo(s(32))
        }

        // 标题在钻石图标右侧，与钻石图标顶部对齐
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(diamondIcon.snp.right).offset(s(12))
            make.top.equalTo(diamondIcon).offset(s(10))
            make.right.lessThanOrEqualTo(actionButton.snp.left).offset(-s(12))
        }

        // 副标题在标题下方
        subtitleLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(s(10))
            make.right.lessThanOrEqualTo(actionButton.snp.left).offset(-s(12))
            make.bottom.lessThanOrEqualToSuperview().offset(-s(16))
        }

        actionGradient.colors = LNGradient.primaryColors
        actionGradient.startPoint = LNGradient.primaryStartPoint
        actionGradient.endPoint = LNGradient.primaryEndPoint
        actionButton.layer.insertSublayer(actionGradient, at: 0)
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        actionGradient.frame = actionButton.bounds
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    @objc private func actionButtonTapped() {
        onActionTapped?()
    }

    func configure(with item: LNTaskItem) {
        print("LNTaskCell configure with: \(item.title), reward: \(item.reward), status: \(item.status)")
        rewardLabel.text = "+\(item.reward)"
        titleLabel.text = item.title
        subtitleLabel.text = item.subtitle

        switch item.status {
        case .go:
            actionButton.setTitle("GO!", for: .normal)
            actionButton.isEnabled = true
            actionButton.alpha = 1.0
        case .finish:
            actionButton.setTitle("Finish", for: .normal)
            actionButton.isEnabled = false
            actionButton.alpha = 0.6
        }

        // 强制刷新布局
        setNeedsLayout()
        layoutIfNeeded()
    }

    /// 配置卡片圆角
    /// - Parameters:
    ///   - isFirst: 是否为 section 中的第一个 cell
    ///   - isLast: 是否为 section 中的最后一个 cell
    func configureCornerRadius(isFirst: Bool, isLast: Bool) {
        let cornerRadius: CGFloat = s(12)

        if isFirst && isLast {
            // 单独一个 cell，四个角都有圆角
            card.layer.cornerRadius = cornerRadius
            card.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner, .layerMinXMaxYCorner, .layerMaxXMaxYCorner]
        } else if isFirst {
            // 第一个 cell，上面两个角有圆角
            card.layer.cornerRadius = cornerRadius
            card.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        } else if isLast {
            // 最后一个 cell，下面两个角有圆角
            card.layer.cornerRadius = cornerRadius
            card.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
        } else {
            // 中间的 cell，没有圆角
            card.layer.cornerRadius = 0
            card.layer.maskedCorners = []
        }

        card.layer.masksToBounds = true
    }
}
