//
//  LNWhoLikedMeModel.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/23.
//

import Foundation
import UIKit
import HandyJSON

// MARK: - 谁喜欢我用户数据模型
/// 谁喜欢我用户数据模型
class LNWhoLikedMeUser: BaseModel {
    var auditFlag: String = ""
    var auditRejectReason: String = ""
    var auditVideo: String = ""
    var birthday: String = ""
    var checkTime: String = ""
    var connectSucRate: String = ""
    var connectTimes: Int = 0
    var connectUsers: Int = 0
    var country: String = ""
    var createTime: String = ""
    var deviceId: String = ""
    var email: String = ""
    var expireDay: String = ""
    var gender: String = ""
    var googleId: String = ""
    var headFileName: String = ""
    var id: Int = 0
    var incomeDiamond: Int = 0
    var isDefaultHead: String = ""
    var isRecord: String = ""
    var isSafe: String = ""
    var labelIds: [String] = []
    var language: String = ""
    var lastIp: String = ""
    var lastSettleTime: String = ""
    var level: String = ""
    var matchOpen: String = ""
    var matchingTimes: Int = 0
    var newSource: Int = 0
    var nickName: String = ""
    var normalHeadFileName: String = ""
    var onlineStatus: String = ""
    var operatingAreaCode: String = ""
    var packageVersion: String = ""
    var parentId: String = ""
    var password: String = ""
    var payInfo: String = ""
    var phone: String = ""
    var phoneModel: String = ""
    var popularSource: Int = 0
    var power: String = ""
    var quality: String = ""
    var realCallDisplay: String = ""
    var recommendLimit: String = ""
    var remark: String = ""
    var signature: String = ""
    var status: String = ""
    var testVideoUrl: String = ""
    var todayMatchingTimes: Int = 0
    var totalIncome: Int = 0
    var totalVideoTime: Int = 0
    var unionId: Int = 0
    var unionName: String = ""
    var updateTime: String = ""
    var userCode: String = ""
    var username: String = ""
    var videoPrice: Int = 0
    var vipFlag: String = ""
    var virtualType: String = ""
    var warnTimes: Int = 0
    
    /// 计算年龄（从生日字符串解析）
    var age: Int {
        guard !birthday.isEmpty else { return 22 }
        
        // 尝试解析生日字符串，格式可能是 "YYYY-MM-DD" 或其他格式
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        if let birthDate = dateFormatter.date(from: birthday) {
            let calendar = Calendar.current
            let now = Date()
            let ageComponents = calendar.dateComponents([.year], from: birthDate, to: now)
            return ageComponents.year ?? 22
        }
        
        return 22 // 默认年龄
    }
    
    /// 计算年龄性别标签（仅返回年龄，性别通过图片显示）
    var ageGenderTag: String {
        return "\(age)"
    }

    /// 性别图片名称
    var genderImageName: String {
        return gender.lowercased() == "male" ? "ic_male" : "ic_female"
    }

    /// 是否为男性
    var isMale: Bool {
        return gender.lowercased() == "male"
    }
    
    /// 显示名称
    var displayName: String {
        return nickName.isEmpty ? "User" : nickName
    }
    
    /// 头像URL
    var avatarURL: String {
        return headFileName.isEmpty ? normalHeadFileName : headFileName
    }
    
    /// 国家显示名称
    var countryDisplayName: String {
        return country.isEmpty ? "Unknown" : country
    }

    /// 是否在线
    var isOnline: Bool {
        // 根据onlineStatus字段判断用户是否在线
        // 通常 "1" 或 "online" 表示在线，"0" 或 "offline" 表示离线
        return onlineStatus == "1" || onlineStatus.lowercased() == "online"
    }
}

// MARK: - API响应模型
/// 谁喜欢我API响应模型
class LNWhoLikedMeResponse: BaseModel {
    var code: Int = 0
    var data: [LNWhoLikedMeUser] = []
    var msg: String = ""
    var success: Bool = false
}


