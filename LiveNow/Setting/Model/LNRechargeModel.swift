//
//  LNRechargeModel.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/31.
//

import Foundation
import HandyJSON

// MARK: - 充值数据模型
/// 充值数据模型 - 对应Flutter的RechargeBean
class LNRecharge: BaseModel {
    var id: String = ""
    var num: Int = 0
    var giveNum: Int = 0
    var currency: String = ""
    var price: String = ""
    var priceId: String = ""
    var localizedPrice: String = ""
    var productId: String = ""
    var googleProductId: String = ""
    var itemName: String = ""
    var amount: String = ""
    var diamondNum: Int = 0
    var message: String = ""
    var priceType: String = ""
    
    /// 1-钻石消耗,2-会员订阅,3-全部
    var discountType: String = ""
    var discountMessage: String = ""
    
    /// 0-无优惠,2-包含优惠
    var imageUrl: String = ""
    var payTypeMessage: String = ""
    var level: String = ""
    var payMethodId: Int = 0
    
    var times: Int = 0
    var countdown: String = ""
    
    /// 计算总钻石数量
    var totalDiamonds: Int {
        return num + giveNum
    }
    
    /// 显示价格（优先使用本地化价格）
    var displayPrice: String {
        return localizedPrice.isEmpty ? price : localizedPrice
    }
    
    /// 是否有折扣
    var hasDiscount: Bool {
        return discountType == "2" && !discountMessage.isEmpty
    }
    
    /// 折扣标签显示
    var discountLabel: String? {
        return hasDiscount ? discountMessage : nil
    }
    
    /// 显示名称
    var displayName: String {
        return itemName.isEmpty ? "\(num)" : itemName
    }
    
    /// 奖励钻石数量
    var bonusDiamonds: Int {
        return giveNum
    }
    
    /// 基础钻石数量
    var baseDiamonds: Int {
        return num
    }
}

// MARK: - 充值列表API响应模型
/// 充值列表API响应模型
class LNRechargeListResponse: BaseModel {
    var code: Int = 0
    var data: [LNRecharge] = []
    var msg: String = ""
    var success: Bool = false
}

// MARK: - 充值请求参数
/// 充值请求参数
struct LNRechargeRequest {
    /// 价格类型：1-充值，2-订阅，3-充值+订阅
    let priceType: String
    
    init(priceType: String = "1") {
        self.priceType = priceType
    }
    
    /// 转换为请求参数字典
    func toParameters() -> [String: Any] {
        return ["priceType": priceType]
    }
}

// MARK: - 兼容性模型
/// 为了兼容现有的UI组件，保留RechargeOption结构
struct LNRechargeOption {
    let diamonds: Int
    let bonusDiamonds: Int
    let priceUSD: String
    let discountLabel: String?
    
    var totalDiamonds: Int { return diamonds + bonusDiamonds }
    
    /// 从LNRecharge模型创建
    init(from recharge: LNRecharge) {
        self.diamonds = recharge.baseDiamonds
        self.bonusDiamonds = recharge.bonusDiamonds
        self.priceUSD = recharge.displayPrice
        self.discountLabel = recharge.discountLabel
    }
    
    /// 直接创建
    init(diamonds: Int, bonusDiamonds: Int, priceUSD: String, discountLabel: String?) {
        self.diamonds = diamonds
        self.bonusDiamonds = bonusDiamonds
        self.priceUSD = priceUSD
        self.discountLabel = discountLabel
    }
}

// MARK: - 充值管理器
/// 充值管理器 - 处理充值相关的网络请求
class LNRechargeManager {
    static let shared = LNRechargeManager()
    private init() {}
    
    /// 获取充值列表
    /// - Parameters:
    ///   - priceType: 价格类型，默认为"1"（充值）
    ///   - completion: 成功回调
    ///   - failure: 失败回调
    func fetchRechargeList(
        priceType: String = "1",
        completion: @escaping ([LNRecharge]) -> Void,
        failure: @escaping (Error) -> Void
    ) {
        let request = LNRechargeRequest(priceType: priceType)
        
        NetWorkRequest(
            LNApiProfile.rechargePriceList(par: request.toParameters()),
            completion: { result in
                DispatchQueue.main.async {
                    // 解析返回数据
                    if let dataArray = result["data"] as? [[String: Any]] {
                        var rechargeList: [LNRecharge] = []
                        for item in dataArray {
                            if let recharge = LNRecharge.deserialize(from: item) {
                                rechargeList.append(recharge)
                            }
                        }
                        completion(rechargeList)
                    } else {
                        let error = NSError(domain: "LNRechargeManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"])
                        failure(error)
                    }
                }
            },
            failure: { error in
                DispatchQueue.main.async {
                    failure(error)
                }
            }
        )
    }
    
    /// 创建订单
    /// - Parameters:
    ///   - recharge: 充值项目
    ///   - completion: 成功回调
    ///   - failure: 失败回调
    func createOrder(
        recharge: LNRecharge,
        completion: @escaping ([String: Any]) -> Void,
        failure: @escaping (Error) -> Void
    ) {
        let parameters: [String: Any] = [
            "priceId": recharge.priceId,
            "productId": recharge.productId,
            "amount": recharge.amount
        ]
        
        NetWorkRequest(
            LNApiProfile.createOrder(par: parameters),
            completion: { result in
                DispatchQueue.main.async {
                    completion(result)
                }
            },
            failure: { error in
                DispatchQueue.main.async {
                    failure(error)
                }
            }
        )
    }
}
