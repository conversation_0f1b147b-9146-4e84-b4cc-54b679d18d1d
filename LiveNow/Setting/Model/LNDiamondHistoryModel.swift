//
//  LNDiamondHistoryModel.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/21.
//

import Foundation
import UIKit
import HandyJSON

// MARK: - 钻石记录数据模型
/// 钻石记录数据模型
class LNDiamondRecord: BaseModel {
    var afterNum: Int = 0
    var anchorId: Int = 0
    var anchorNickName: String = ""
    var beforeNum: Int = 0
    var changeNum: Int = 0
    var changeType: String = ""
    var createTime: String = ""
    var headFileName: String = ""
    var id: Int = 0
    var isIncreasing: Int = 0
    var message: String = ""
    var nickName: String = ""
    var relationId: Int = 0
    var updateTime: String = ""
    var userCode: String = ""
    var userId: Int = 0
    
    /// 计算钻石数量（显示变化数量）
    var displayDiamonds: Int {
        return abs(changeNum)
    }
    
    /// 计算价格显示（根据钻石数量估算，实际项目中可能需要其他字段）
    var displayPrice: String {
        let diamonds = displayDiamonds
        if diamonds >= 300 {
            return "US$9.99"
        } else if diamonds >= 160 {
            return "US$4.99"
        } else {
            return "US$1.99"
        }
    }
    
    /// 格式化时间显示
    var displayTime: String {
        return createTime.isEmpty ? "2025-01-02 16:25:45" : createTime
    }
    
    /// 计算状态（根据isIncreasing判断）
    var isFinished: Bool {
        return isIncreasing == 1 // 1表示增加（完成），0表示减少（未完成）
    }
    
    /// 显示消息
    var displayMessage: String {
        return message.isEmpty ? "Diamond transaction" : message
    }
}

// MARK: - 钻石记录API响应模型
/// 钻石记录API响应模型
class LNDiamondHistoryResponse: BaseModel {
    var code: Int = 0
    var data: LNPageResponse<LNDiamondRecord>?
    var msg: String = ""
    var success: Bool = false
}

// MARK: - 兼容性模型
/// 为了兼容现有的Cell，保留Record结构
struct LNDiamondHistoryRecord {
    let diamonds: Int
    let priceUSD: String
    let time: String
    let status: LNDiamondHistoryStatus
}

enum LNDiamondHistoryStatus {
    case finished
    case unfinished
}
