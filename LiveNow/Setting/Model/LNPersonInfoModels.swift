//
//  LNPersonInfoModels.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/28.
//

import UIKit
import HandyJSON

// MARK: - 文件类型常量
let fileTypeImage = "1"
let fileTypeVideo = "2"

// MARK: - 主播文件数据模型
/// 主播文件数据模型（图片/视频）
/// 对应Flutter中的AnchorFileBean
class LNAnchorFileModel: BaseModel {
    /// 文件ID
    var id: Int = 0
    /// 主播ID
    var anchorId: String = ""
    /// 文件类型 (1:图片, 2:视频)
    var fileType: String = ""
    /// 文件URL
    var fileUrl: String = ""
    /// 文件名称
    var fileName: String = ""
    /// 缩略图URL
    var thumbnail: String = ""
    /// 状态
    var status: String = ""
    /// 是否锁定
    var isLock: String = ""

    /// 是否为图片
    var isImage: Bool {
        return fileType == fileTypeImage
    }

    /// 是否为视频
    var isVideo: Bool {
        return fileType == fileTypeVideo
    }

    /// 显示用的文件URL
    var displayUrl: String {
        return fileUrl.isEmpty ? fileName : fileUrl
    }

    /// 显示用的缩略图URL
    var displayThumbnail: String {
        return thumbnail.isEmpty ? displayUrl : thumbnail
    }

    /// 是否被锁定
    var isLocked: Bool {
        return isLock == "1"
    }
}

// MARK: - 主播标签数据模型
/// 主播标签数据模型
/// 对应Flutter中的AnchorLabelBean
class LNAnchorLabelModel: BaseModel {
    /// 标签ID
    var id: Int = 0
    /// 标签名称
    var labelName: String = ""
    /// 点赞数量
    var likeCount: Int = 0

    /// 显示用的标签名称
    var displayName: String {
        return labelName.isEmpty ? "标签" : labelName
    }

    /// 显示用的点赞数量
    var displayLikeCount: String {
        if likeCount <= 0 {
            return "0"
        } else if likeCount < 1000 {
            return "\(likeCount)"
        } else if likeCount < 10000 {
            return String(format: "%.1fK", Double(likeCount) / 1000.0)
        } else {
            return String(format: "%.1fW", Double(likeCount) / 10000.0)
        }
    }

    /// 是否有点赞
    var hasLikes: Bool {
        return likeCount > 0
    }
}



// MARK: - API响应模型
/// 主播详情API响应模型
class LNAnchorDetailResponse: BaseModel {
    var code: Int = 0
    var msg: String = ""
    var success: Bool = false
    var data: LNUserModel?
}

/// 关注状态API响应模型
class LNFollowFlagResponse: BaseModel {
    var code: Int = 0
    var msg: String = ""
    var success: Bool = false
    var data: String = ""
}

/// 用户礼物API响应模型
class LNUserGiftResponse: BaseModel {
    var code: Int = 0
    var msg: String = ""
    var success: Bool = false
    var data: [LNGiftModel] = []
}
