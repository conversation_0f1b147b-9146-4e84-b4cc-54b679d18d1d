//
//  LNSubscriptionHistoryModel.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/31.
//

import Foundation
import HandyJSON

// MARK: - 订阅历史数据模型
/// 订阅历史数据模型 - 对应Flutter的RechargeBean（订阅类型）
class LNSubscriptionHistory: BaseModel {
    var id: String = ""
    var num: Int = 0
    var giveNum: Int = 0
    var currency: String = ""
    var price: String = ""
    var priceId: String = ""
    var localizedPrice: String = ""
    var productId: String = ""
    var googleProductId: String = ""
    var itemName: String = ""
    var amount: String = ""
    var diamondNum: Int = 0
    var message: String = ""
    var priceType: String = ""
    
    /// 1-钻石消耗,2-会员订阅,3-全部
    var discountType: String = ""
    var discountMessage: String = ""
    
    /// 0-无优惠,2-包含优惠
    var imageUrl: String = ""
    var payTypeMessage: String = ""
    var level: String = ""
    var payMethodId: Int = 0
    
    var times: Int = 0
    var countdown: String = ""
    
    // 订阅历史特有字段
    var createTime: String = ""
    var updateTime: String = ""
    var status: String = "" // 订阅状态
    var expireTime: String = "" // 过期时间
    
    /// VIP类型显示
    var vipType: String {
        if !itemName.isEmpty {
            return itemName
        }
        
        // 根据level或其他字段判断VIP类型
        switch level {
        case "1":
            return "Bronze VIP"
        case "2":
            return "Silver VIP"
        case "3":
            return "Gold VIP"
        default:
            return "VIP"
        }
    }
    
    /// 显示价格（优先使用本地化价格）
    var displayPrice: String {
        return localizedPrice.isEmpty ? price : localizedPrice
    }
    
    /// 格式化显示价格
    var priceDisplay: String {
        let price = displayPrice
        return price.hasPrefix("US$") ? price : "US$\(price)"
    }
    
    /// 格式化时间显示
    var timeDisplay: String {
        return createTime.isEmpty ? "2025-01-02 16:25:45" : createTime
    }
    
    /// 订阅状态
    var subscriptionStatus: LNSubscriptionStatus {
        switch status {
        case "1", "active", "finished":
            return .finished
        case "0", "inactive", "unfinished":
            return .unfinished
        default:
            return .unfinished
        }
    }
    
    /// 是否已完成
    var isFinished: Bool {
        return subscriptionStatus == .finished
    }
}

// MARK: - 订阅状态枚举
enum LNSubscriptionStatus {
    case finished
    case unfinished
    
    var displayText: String {
        switch self {
        case .finished:
            return "Finished"
        case .unfinished:
            return "Unfinished"
        }
    }
    
    var textColor: UIColor {
        switch self {
        case .finished:
            return UIColor.hex(hexString: "#00FF91") // 绿色
        case .unfinished:
            return UIColor.hex(hexString: "#FF1500") // 红色
        }
    }
}

// MARK: - 订阅历史列表API响应模型
/// 订阅历史列表API响应模型
class LNSubscriptionHistoryListResponse: BaseModel {
    var code: Int = 0
    var data: [LNSubscriptionHistory] = []
    var msg: String = ""
    var success: Bool = false
}

// MARK: - 订阅历史请求参数
/// 订阅历史请求参数
struct LNSubscriptionHistoryRequest {
    /// 价格类型：1-充值，2-订阅，3-充值+订阅
    let priceType: String
    let page: Int
    let size: Int
    
    init(priceType: String = "2", page: Int = 1, size: Int = 10) {
        self.priceType = priceType
        self.page = page
        self.size = size
    }
    
    /// 转换为请求参数字典
    func toParameters() -> [String: Any] {
        return [
            "priceType": priceType,
            "page": page,
            "size": size
        ]
    }
}

// MARK: - 兼容性模型
/// 为了兼容现有的UI组件，保留Record结构
struct LNSubscriptionRecord {
    let vipType: String
    let priceUSD: String
    let time: String
    let status: LNSubscriptionStatus
    
    /// 从LNSubscriptionHistory模型创建
    init(from subscription: LNSubscriptionHistory) {
        self.vipType = subscription.vipType
        self.priceUSD = subscription.priceDisplay
        self.time = subscription.timeDisplay
        self.status = subscription.subscriptionStatus
    }
    
    /// 直接创建
    init(vipType: String, priceUSD: String, time: String, status: LNSubscriptionStatus) {
        self.vipType = vipType
        self.priceUSD = priceUSD
        self.time = time
        self.status = status
    }
}

// MARK: - 订阅历史管理器
/// 订阅历史管理器 - 处理订阅历史相关的网络请求
class LNSubscriptionHistoryManager {
    static let shared = LNSubscriptionHistoryManager()
    private init() {}
    
    /// 获取订阅历史列表
    /// - Parameters:
    ///   - page: 页码
    ///   - size: 每页数量
    ///   - completion: 成功回调
    ///   - failure: 失败回调
    func fetchSubscriptionHistoryList(
        page: Int = 1,
        size: Int = 10,
        completion: @escaping ([LNSubscriptionHistory]) -> Void,
        failure: @escaping (Error) -> Void
    ) {
        let request = LNSubscriptionHistoryRequest(priceType: "2", page: page, size: size)
        
        NetWorkRequest(
            LNApiProfile.rechargePriceList(par: request.toParameters()),
            completion: { result in
                DispatchQueue.main.async {
                    // 解析返回数据
                    if let dataArray = result["data"] as? [[String: Any]] {
                        var subscriptionList: [LNSubscriptionHistory] = []
                        for item in dataArray {
                            if let subscription = LNSubscriptionHistory.deserialize(from: item) {
                                subscriptionList.append(subscription)
                            }
                        }
                        completion(subscriptionList)
                    } else {
                        let error = NSError(domain: "LNSubscriptionHistoryManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"])
                        failure(error)
                    }
                }
            },
            failure: { error in
                DispatchQueue.main.async {
                    failure(error)
                }
            }
        )
    }
}
