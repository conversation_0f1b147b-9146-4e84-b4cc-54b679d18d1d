# LNCountryModal - 国家选择模态视图

## 概述

`LNCountryModal` 是一个功能完整的国家选择模态视图，通过接口动态获取国家列表，支持搜索功能和优雅的动画效果。

## 功能特性

- ✅ 通过接口动态获取国家列表
- ✅ 实时搜索功能
- ✅ 优雅的弹出和消失动画
- ✅ 支持深色模式
- ✅ 响应式布局，适配各种屏幕尺寸
- ✅ 键盘自动处理
- ✅ 国家代码和名称双重显示
- ✅ 支持中英文国家名称

## 数据模型

### LNCountry
```swift
struct LNCountry {
    let id: Int?                // 国家ID
    let countryName: String?    // 中文名称
    let countryNameEn: String   // 英文名称
    let countryCode: String     // 国家代码（ISO 3166-1 alpha-2）

    var displayName: String     // 显示名称（优先显示英文名称）
}
```

### LNCountryList
```swift
class LNCountryList {
    static let shared = LNCountryList()

    // 异步获取国家列表
    func fetchCountryList(completion: @escaping ([LNCountry]) -> Void, failure: @escaping (Error) -> Void)

    var sortedByEnglishName: [LNCountry]  // 按英文名称排序的列表

    // 工具方法
    func getEnglishNameByCode(_ code: String) -> String?
    func getCodeByEnglishName(_ name: String) -> String?
    func getLoadedCountries() -> [LNCountry]
}
```

## 使用方法

### 基本使用

```swift
let countryModal = LNCountryModal()

// 设置选择回调
countryModal.onCountrySelected = { country in
    print("选择了国家: \(country.displayName) (\(country.countryCode))")
}

// 设置取消回调
countryModal.onCancel = {
    print("用户取消了选择")
}

// 显示模态视图
countryModal.show(in: self.view)
```

### 获取国家信息

```swift
let countryList = LNCountryList.shared

// 异步获取国家列表
countryList.fetchCountryList(completion: { countries in
    print("获取到 \(countries.count) 个国家")

    // 通过代码获取名称
    let countryName = countryList.getEnglishNameByCode("CN") // "China"

    // 通过名称获取代码
    let countryCode = countryList.getCodeByEnglishName("China") // "CN"

    // 获取排序后的国家列表
    let sortedCountries = countryList.sortedByEnglishName

}, failure: { error in
    print("获取国家列表失败: \(error.localizedDescription)")
})

// 获取已加载的国家列表（同步）
let loadedCountries = countryList.getLoadedCountries()
```

## 回调说明

### onCountrySelected
- **类型**: `((LNCountry) -> Void)?`
- **说明**: 用户选择国家时触发
- **参数**: 选择的国家对象

### onCancel
- **类型**: `(() -> Void)?`
- **说明**: 用户取消选择时触发（点击背景或取消按钮）

## 界面特性

- **标题**: "Select Country"
- **搜索框**: 支持实时搜索国家名称
- **列表显示**: 显示格式为 "国家名称 (国家代码)"
- **取消按钮**: 红色取消按钮
- **动画效果**: 从底部滑入/滑出的动画

## 技术实现

- 使用 SnapKit 进行自动布局
- 支持 iOS 13+ 系统特性
- 遵循 Apple Human Interface Guidelines
- 使用 `s()` 函数进行屏幕适配
- 支持深色模式自动切换

## 示例代码

参考 `LNCountryModalExample.swift` 文件查看完整的使用示例。

## 注意事项

1. 确保在主线程中调用 `show(in:)` 方法
2. 模态视图会自动处理键盘的显示和隐藏
3. 搜索功能不区分大小写
4. 国家列表已按英文名称排序
5. 支持所有标准的国家代码（ISO 3166-1 alpha-2）
