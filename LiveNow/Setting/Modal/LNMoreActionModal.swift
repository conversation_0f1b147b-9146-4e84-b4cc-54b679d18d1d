//
//  LNMoreActionModal.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/18.
//

import UIKit
import SnapKit

/// 更多操作弹框
/// 功能：
/// - 显示Delete、Report、Cancel三个选项
/// - 支持弹出和消失动画
/// - 覆盖导航栏显示
class LNMoreActionModal: UIView {
    
    // MARK: - Callbacks
    var onDelete: (() -> Void)?
    var onReport: (() -> Void)?
    
    // MARK: - UI Elements
    
    // 背景遮罩
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        return view
    }()
    
    // 主容器
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = s(20)
        view.layer.masksToBounds = true
        return view
    }()
    
    // Delete按钮
    private lazy var deleteButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Delete", for: .normal)
        button.titleLabel?.font = LNFont.medium(18)
        button.setTitleColor(.black, for: .normal)
        button.backgroundColor = .clear
        button.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // Report按钮
    private lazy var reportButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Report", for: .normal)
        button.titleLabel?.font = LNFont.medium(18)
        button.setTitleColor(.black, for: .normal)
        button.backgroundColor = .clear
        button.addTarget(self, action: #selector(reportButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // Cancel按钮
    private lazy var cancelButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Cancel", for: .normal)
        button.titleLabel?.font = LNFont.medium(18)
        button.setTitleColor(UIColor.hex(hexString: "#FF6B6B"), for: .normal)
        button.backgroundColor = .clear
        button.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 分割线1
    private lazy var separatorLine1: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.lightGray.withAlphaComponent(0.3)
        return view
    }()
    
    // 分割线2
    private lazy var separatorLine2: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.lightGray.withAlphaComponent(0.3)
        return view
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupActions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        addSubview(backgroundView)
        addSubview(containerView)
        
        containerView.addSubview(deleteButton)
        containerView.addSubview(separatorLine1)
        containerView.addSubview(reportButton)
        containerView.addSubview(separatorLine2)
        containerView.addSubview(cancelButton)
    }
    
    private func setupConstraints() {
        // 背景遮罩
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 主容器 - 底部弹出
        containerView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(s(220))
        }
        
        // Delete按钮
        deleteButton.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(s(60))
        }
        
        // 分割线1
        separatorLine1.snp.makeConstraints { make in
            make.top.equalTo(deleteButton.snp.bottom)
            make.left.right.equalToSuperview().inset(s(20))
            make.height.equalTo(0.5)
        }
        
        // Report按钮
        reportButton.snp.makeConstraints { make in
            make.top.equalTo(separatorLine1.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(s(60))
        }
        
        // 分割线2
        separatorLine2.snp.makeConstraints { make in
            make.top.equalTo(reportButton.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(10.5)
        }

        // Cancel按钮
        cancelButton.snp.makeConstraints { make in
            make.top.equalTo(separatorLine2.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview()
            make.height.equalTo(s(60))
            make.bottom.lessThanOrEqualToSuperview().offset(-s(40))
        }
    }
    
    private func setupActions() {
        // 点击背景遮罩关闭弹框
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundView.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Event Handlers
    @objc private func deleteButtonTapped() {
        dismiss()
        onDelete?()
    }
    
    @objc private func reportButtonTapped() {
        dismiss()
        onReport?()
    }
    
    @objc private func cancelButtonTapped() {
        dismiss()
    }
    
    @objc private func backgroundTapped() {
        dismiss()
    }
    
    // MARK: - Animation Methods
    private func dismiss() {
        UIView.animate(withDuration: 0.25, animations: {
            self.backgroundView.alpha = 0
            self.containerView.transform = CGAffineTransform(translationX: 0, y: s(220))
        }) { _ in
            self.removeFromSuperview()
        }
    }
}

// MARK: - Public Methods
extension LNMoreActionModal {
    /// 显示更多操作弹框
    /// - Parameter parentView: 父视图
    func show(in parentView: UIView) {
        parentView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 确保弹框在最顶层
        parentView.bringSubviewToFront(self)
        
        // 强制布局以获取正确的frame
        self.layoutIfNeeded()

        // 设置初始状态
        backgroundView.alpha = 0
        containerView.transform = CGAffineTransform(translationX: 0, y: s(220))

        // 显示动画
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut) {
            self.backgroundView.alpha = 1
            self.containerView.transform = .identity
        }
    }
}
