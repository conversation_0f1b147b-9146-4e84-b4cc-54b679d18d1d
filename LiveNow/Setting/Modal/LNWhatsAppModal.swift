//
//  LNWhatsAppModal.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/18.
//

import UIKit
import SnapKit

/// WhatsApp获取弹框
/// 功能：
/// - 显示获取WhatsApp联系方式的弹框
/// - 包含爱心装饰、标题、描述和VIP按钮
/// - 支持弹出和消失动画
class LNWhatsAppModal: UIView {
    
    // MARK: - UI Elements
    
    // 背景遮罩
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        return view
    }()
    
    // 主容器
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = s(20)
        view.layer.masksToBounds = false // 允许心形图标超出边界显示
        // 添加阴影效果
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowOpacity = 0.1
        view.layer.shadowRadius = 8
        return view
    }()
    
    // 中心爱心
    private lazy var heartView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_heart")
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = UIColor.hex(hexString: "#FF69B4")
        return imageView
    }()
    
    // 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "want to get her\nwhatsapp ?"
        label.font = LNFont.medium(20)
        label.textColor = UIColor.hex(hexString: "#04E798")
        label.textAlignment = .center
        label.numberOfLines = 2
        return label
    }()
    
    // 描述标签
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "you can get her whatsapp\nnumber by paying 80 diamonds"
        label.font = LNFont.regular(16)
        label.textColor = .black
        label.textAlignment = .center
        label.numberOfLines = 2
        return label
    }()
    
    // VIP按钮
    private lazy var vipButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("ONLY VIP can see new girls", for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = s(25)
        button.layer.masksToBounds = true
        button.addTarget(self, action: #selector(vipButtonTapped), for: .touchUpInside)
        return button
    }()

    // VIP按钮的渐变层
    private lazy var vipGradientLayer: CAGradientLayer = {
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.hex(hexString: "#04E798").cgColor,
            UIColor.hex(hexString: "#0ADCE1").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1.0, y: 0.5)
        gradientLayer.cornerRadius = s(25)
        return gradientLayer
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupActions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        addSubview(backgroundView)
        addSubview(containerView)

        // 直接将心形图标添加到主视图，使其显示在容器上方
        addSubview(heartView)

        containerView.addSubview(titleLabel)
        containerView.addSubview(descriptionLabel)
        containerView.addSubview(vipButton)

        // 添加 VIP 按钮的渐变层
        vipButton.layer.insertSublayer(vipGradientLayer, at: 0)
    }
    
    private func setupConstraints() {
        // 背景遮罩
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 主容器
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(s(300))
            make.height.equalTo(s(280))
        }
        
        // 中心爱心 - 一半在容器外面，一半在里面
        heartView.snp.makeConstraints { make in
            make.centerY.equalTo(containerView.snp.top) // 中心对齐容器顶部，实现一半在外一半在内
            make.centerX.equalTo(containerView)
            make.width.height.equalTo(s(100))
        }
        
        // 标题
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(40))
            make.left.right.equalToSuperview().inset(s(20))
        }
        
        // 描述
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview().inset(s(20))
        }
        
        // VIP按钮
        vipButton.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(s(30))
            make.left.right.equalToSuperview().inset(s(20))
            make.height.equalTo(s(50))
            make.bottom.equalToSuperview().offset(-s(20))
        }
    }
    
    private func setupActions() {
        // 点击背景遮罩关闭弹框
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundView.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Event Handlers
    @objc private func vipButtonTapped() {
        print("VIP button tapped")
        // TODO: 实现VIP功能
        dismiss()
    }
    
    @objc private func backgroundTapped() {
        dismiss()
    }
    
    // MARK: - Animation Methods
    private func dismiss() {
        UIView.animate(withDuration: 0.2, delay: 0, options: [.curveEaseInOut, .allowUserInteraction]) {
            self.backgroundView.alpha = 0
            self.containerView.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
            self.containerView.alpha = 0.8
            // 同时处理心形图标的消失动画
            self.heartView.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
            self.heartView.alpha = 0.8
        } completion: { _ in
            self.removeFromSuperview()
        }
    }

    /// 更新渐变层的 frame
    private func updateGradientLayerFrame() {
        // 直接设置渐变层尺寸，避免动画效果
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        vipGradientLayer.frame = vipButton.bounds
        CATransaction.commit()
    }
}

// MARK: - Public Methods
extension LNWhatsAppModal {
    /// 显示WhatsApp弹框
    /// - Parameter parentView: 父视图
    func show(in parentView: UIView) {
        parentView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 确保弹框在最顶层
        parentView.bringSubviewToFront(self)
        
        // 强制布局以获取正确的frame
        self.layoutIfNeeded()

        // 更新渐变层尺寸
        self.updateGradientLayerFrame()

        // 设置初始状态
        backgroundView.alpha = 0
        containerView.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        containerView.alpha = 0.8
        // 同时设置心形图标的初始状态
        heartView.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        heartView.alpha = 0.8

        // 显示动画
        UIView.animate(withDuration: 0.25, delay: 0, options: [.curveEaseOut, .allowUserInteraction]) {
            self.backgroundView.alpha = 1
            self.containerView.transform = .identity
            self.containerView.alpha = 1.0
            // 同时处理心形图标的显示动画
            self.heartView.transform = .identity
            self.heartView.alpha = 1.0
        }
    }
}
