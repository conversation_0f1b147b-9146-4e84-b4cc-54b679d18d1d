//
//  LNDateOfBirthModal.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/15.
//

import UIKit
import SnapKit
import JKSwiftExtension

/// 出生日期选择弹框模态视图
class LNDateOfBirthModal: UIView {
    
    // MARK: - Properties
    var onConfirm: ((Date) -> Void)?
    var onCancel: (() -> Void)?
    
    private var selectedDate: Date = Date()
    private var years: [Int] = []
    private var months: [Int] = Array(1...12)
    private var days: [Int] = Array(1...31)
    
    // MARK: - UI Elements
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        let tap = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tap)
        return view
    }()
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white
        view.layer.cornerRadius = s(20)
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.layer.masksToBounds = true
        return view
    }()
    
    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(systemName: "xmark"), for: .normal)
        button.tintColor = UIColor.systemGray
        button.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Date Of Birth"
        label.font = LNFont.medium(18)
        label.textColor = UIColor.label
        label.textAlignment = .center
        return label
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Please Choose Your Real Birthday, Which Cannot\nBe Modified After Submission"
        label.font = LNFont.regular(14)
        label.textColor = UIColor.systemGray
        label.textAlignment = .center
        label.numberOfLines = 2
        return label
    }()
    
    private lazy var pickerView: UIPickerView = {
        let picker = UIPickerView()
        picker.delegate = self
        picker.dataSource = self
        return picker
    }()
    
    private lazy var confirmButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Confirm", for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor(hexString: "#00D4AA")
        button.layer.cornerRadius = s(25)
        button.layer.masksToBounds = true
        button.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupData()
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private Methods
    private func setupData() {
        // 生成年份数组（从1950到当前年份）
        let currentYear = Calendar.current.component(.year, from: Date())
        years = Array(1950...currentYear).reversed()
        
        // 设置默认选中日期为1998-02-03
        let calendar = Calendar.current
        var components = DateComponents()
        components.year = 1998
        components.month = 2
        components.day = 3
        selectedDate = calendar.date(from: components) ?? Date()
    }
    
    private func setupUI() {
        addSubview(backgroundView)
        addSubview(containerView)
        
        containerView.addSubview(closeButton)
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(pickerView)
        containerView.addSubview(confirmButton)
    }
    
    private func setupConstraints() {
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        containerView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(s(400))
        }
        
        closeButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.equalToSuperview().offset(s(20))
            make.size.equalTo(s(24))
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.centerX.equalToSuperview()
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(12))
            make.left.equalToSuperview().offset(s(20))
            make.right.equalToSuperview().offset(-s(20))
        }
        
        pickerView.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview()
            make.height.equalTo(s(200))
        }
        
        confirmButton.snp.makeConstraints { make in
            make.top.equalTo(pickerView.snp.bottom).offset(s(20))
            make.left.equalToSuperview().offset(s(75))
            make.right.equalToSuperview().offset(-s(75))
            make.height.equalTo(s(50))
            make.bottom.lessThanOrEqualToSuperview().offset(-s(40))
        }
    }
    
    private func setDefaultPickerValues() {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day], from: selectedDate)

        if let year = components.year,
           let yearIndex = years.firstIndex(of: year) {
            pickerView.selectRow(yearIndex, inComponent: 0, animated: false)
        }

        if let month = components.month {
            pickerView.selectRow(month - 1, inComponent: 1, animated: false)
        }

        if let day = components.day {
            pickerView.selectRow(day - 1, inComponent: 2, animated: false)
        }

        updateDaysForSelectedMonth()
    }
    
    private func updateDaysForSelectedMonth() {
        let yearIndex = pickerView.selectedRow(inComponent: 0)
        let monthIndex = pickerView.selectedRow(inComponent: 1)
        
        let year = years[yearIndex]
        let month = monthIndex + 1
        
        let calendar = Calendar.current
        let range = calendar.range(of: .day, in: .month, for: calendar.date(from: DateComponents(year: year, month: month))!)
        let daysInMonth = range?.count ?? 31
        
        days = Array(1...daysInMonth)
        pickerView.reloadComponent(2)
        
        // 如果当前选中的日期超过了该月的天数，调整到最后一天
        let currentDayIndex = pickerView.selectedRow(inComponent: 2)
        if currentDayIndex >= days.count {
            pickerView.selectRow(days.count - 1, inComponent: 2, animated: true)
        }
    }
    
    private func updateSelectedDate() {
        let yearIndex = pickerView.selectedRow(inComponent: 0)
        let monthIndex = pickerView.selectedRow(inComponent: 1)
        let dayIndex = pickerView.selectedRow(inComponent: 2)
        
        let year = years[yearIndex]
        let month = monthIndex + 1
        let day = days[dayIndex]
        
        let calendar = Calendar.current
        var components = DateComponents()
        components.year = year
        components.month = month
        components.day = day
        
        selectedDate = calendar.date(from: components) ?? Date()
    }
    
    // MARK: - Actions
    @objc private func backgroundTapped() {
        dismiss()
        onCancel?()
    }
    
    @objc private func closeTapped() {
        dismiss()
        onCancel?()
    }
    
    @objc private func confirmTapped() {
        updateSelectedDate()
        dismiss()
        onConfirm?(selectedDate)
    }
}

// MARK: - UIPickerViewDataSource
extension LNDateOfBirthModal: UIPickerViewDataSource {
    func numberOfComponents(in pickerView: UIPickerView) -> Int {
        return 3 // 年、月、日
    }

    func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
        switch component {
        case 0: return years.count
        case 1: return months.count
        case 2: return days.count
        default: return 0
        }
    }
}

// MARK: - UIPickerViewDelegate
extension LNDateOfBirthModal: UIPickerViewDelegate {
    func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int) -> String? {
        switch component {
        case 0: return "\(years[row])"
        case 1: return String(format: "%02d", months[row])
        case 2: return String(format: "%02d", days[row])
        default: return nil
        }
    }

    func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
        if component == 0 || component == 1 {
            // 年份或月份改变时，更新日期数组
            updateDaysForSelectedMonth()
        }
    }

    func pickerView(_ pickerView: UIPickerView, widthForComponent component: Int) -> CGFloat {
        return kscreenW / 3.0
    }

    func pickerView(_ pickerView: UIPickerView, rowHeightForComponent component: Int) -> CGFloat {
        return s(40)
    }
}

// MARK: - Public Methods
extension LNDateOfBirthModal {
    /// 显示日期选择器
    /// - Parameters:
    ///   - parentView: 父视图
    ///   - initialDate: 初始日期，默认为1998-02-03
    func show(in parentView: UIView, initialDate: Date? = nil) {
        if let date = initialDate {
            selectedDate = date
        }

        parentView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 强制布局以获取正确的frame
        self.layoutIfNeeded()

        // 设置初始状态
        backgroundView.alpha = 0
        containerView.transform = CGAffineTransform(translationX: 0, y: s(400))

        // 显示动画
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut) {
            self.backgroundView.alpha = 1
            self.containerView.transform = .identity
        } completion: { _ in
            // 在动画完成后设置默认选中值，确保 pickerView 已经完全准备好
            self.setDefaultPickerValues()
        }
    }

    /// 隐藏日期选择器
    private func dismiss() {
        UIView.animate(withDuration: 0.25, animations: {
            self.backgroundView.alpha = 0
            self.containerView.transform = CGAffineTransform(translationX: 0, y: s(400))
        }) { _ in
            self.removeFromSuperview()
        }
    }
}
