//
//  LNVipLevelPagerCell.swift
//  LiveNow
//
//  Created by AI on 2025/8/24.
//

import UIKit
import SnapKit
import FSPagerView

/// VIP等级轮播Cell - 专门用于FSPagerView的VIP等级展示
class LNVipLevelPagerCell: FSPagerViewCell {
    
    // MARK: - Properties
    
    /// VIP等级数据模型
    struct VipLevelData {
        let level: Int
        let title: String
        let progressText: String
        let progress: Float
        let isCurrentLevel: Bool
        let isUnlocked: Bool
        
        init(level: Int, currentUserLevel: Int) {
            self.level = level
            self.title = "VIP \(level)"
            self.isCurrentLevel = (level == currentUserLevel)
            self.isUnlocked = (level <= currentUserLevel)
            
            if level == 6 {
                self.progressText = "Max level reached"
                self.progress = 1.0
            } else if level == currentUserLevel {
                let nextLevelPoints = (level + 1) * 100
                self.progressText = "\(nextLevelPoints) to the next level"
                self.progress = 0.7 // 当前等级70%进度
            } else if level < currentUserLevel {
                self.progressText = "Level completed"
                self.progress = 1.0 // 已完成等级
            } else {
                let nextLevelPoints = (level + 1) * 100
                self.progressText = "\(nextLevelPoints) to unlock"
                self.progress = 0.0 // 未达到等级
            }
        }
    }
    
    // MARK: - UI Elements
    /// 背景图片视图
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.clipsToBounds = true
        return imageView
    }()
    
    /// 等级标题标签
    private lazy var levelLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.bold(18)
        label.textAlignment = .left
        label.isHidden = true
        return label
    }()
    
    /// 进度文本标签
    private lazy var progressLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.regular(12)
        label.textAlignment = .left
        return label
    }()
    
    /// 进度条
    private lazy var progressView: UIProgressView = {
        let progressView = UIProgressView(progressViewStyle: .default)
        progressView.layer.cornerRadius = s(3)
        progressView.clipsToBounds = true
        return progressView
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        // 设置Cell基本样式 - 移除可能产生阴影的设置
        contentView.backgroundColor = .clear
        contentView.layer.cornerRadius = s(14)
        contentView.clipsToBounds = true

        // 移除所有阴影相关设置
        contentView.layer.shadowColor = UIColor.clear.cgColor
        contentView.layer.shadowOffset = CGSize.zero
        contentView.layer.shadowRadius = 0
        contentView.layer.shadowOpacity = 0

        // 确保背景图片视图也没有阴影
        backgroundImageView.layer.shadowColor = UIColor.clear.cgColor
        backgroundImageView.layer.shadowOffset = CGSize.zero
        backgroundImageView.layer.shadowRadius = 0
        backgroundImageView.layer.shadowOpacity = 0

        // 添加子视图
        contentView.addSubview(backgroundImageView)
        contentView.addSubview(levelLabel)
        contentView.addSubview(progressLabel)
        contentView.addSubview(progressView)
    }
    
    private func setupConstraints() {
        // 背景图片约束
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 等级标题约束
        levelLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(16))
            make.top.equalToSuperview().offset(s(16))
            make.right.equalToSuperview().offset(-s(16))
        }
        
        // 进度条约束
        progressView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(16))
            make.right.equalToSuperview().offset(-s(16))
            make.height.equalTo(s(6))
            make.bottom.equalToSuperview().offset(-s(30))
        }
        
        // 进度文本约束
        progressLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(16))
            make.bottom.equalTo(progressView.snp.top).offset(-s(8))
            make.right.lessThanOrEqualToSuperview().offset(-s(16))
        }
    }
    
    // MARK: - Configuration
    
    /// 配置Cell数据
    /// - Parameters:
    ///   - data: VIP等级数据
    func configure(with data: VipLevelData) {
        // 设置背景图片
        backgroundImageView.image = UIImage(named: "ic_vip_v\(data.level)") ?? UIImage(named: "ic_vip_v0")
        
        // 设置等级标题
        levelLabel.text = data.title
        
        // 设置进度信息
        progressLabel.text = data.progressText
        progressView.progress = data.progress
        
        // 根据等级设置颜色主题
        updateColorTheme(for: data.level, isCurrentLevel: data.isCurrentLevel)
    }
    
    /// 根据VIP等级更新颜色主题
    private func updateColorTheme(for level: Int, isCurrentLevel: Bool) {
        if level == 0 {
            // 免费等级使用较暗的文本
            levelLabel.textColor = UIColor(hexString: "#6D75AD")
            progressLabel.textColor = UIColor(hexString: "#6D75AD")
            progressView.progressTintColor = UIColor(hexString: "#6D75AD")
            progressView.trackTintColor = UIColor(hexString: "#6D75AD").withAlphaComponent(0.3)
        } else {
            // 付费等级使用白色文本
            levelLabel.textColor = .white
            progressLabel.textColor = UIColor.white.withAlphaComponent(0.9)
            progressView.progressTintColor = .white
            progressView.trackTintColor = UIColor.white.withAlphaComponent(0.3)
        }
        
        // 当前等级的特殊处理
        if isCurrentLevel {
            levelLabel.font = LNFont.bold(20) // 当前等级字体更大
        } else {
            levelLabel.font = LNFont.bold(18)
        }
    }
    
    // MARK: - Animation
    
    /// 添加选中动画效果
    func animateSelection() {
        UIView.animate(withDuration: 0.1, animations: {
            self.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.transform = CGAffineTransform.identity
            }
        }
    }
    
    // MARK: - Reuse
    
    override func prepareForReuse() {
        super.prepareForReuse()
        backgroundImageView.image = nil
        levelLabel.text = nil
        progressLabel.text = nil
        progressView.progress = 0.0
        contentView.alpha = 1.0
        transform = CGAffineTransform.identity
    }
}
