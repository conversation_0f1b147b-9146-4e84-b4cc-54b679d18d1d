//
//  LNHelpFeedbackViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/09.
//

import UIKit
import SnapKit
import JKSwiftExtension
import Photos
import AVFoundation

/// 帮助与反馈页面
/// - 文本输入 + 图片选择（相册）+ 提交按钮
/// - iOS 13+ 支持深色模式与动态字体
class LNHelpFeedbackViewController: LNBaseController, UITextViewDelegate, UIImagePickerControllerDelegate, UINavigationControllerDelegate {

    // 使用白色导航栏
    override var navigationSolidColor: UIColor { return .white }
    override var navigationTitleColor: UIColor { return .label }

    // MARK: - UI
    private lazy var scrollView: UIScrollView = {
        let v = UIScrollView()
        v.alwaysBounceVertical = true
        v.keyboardDismissMode = .interactive
        return v
    }()
    private lazy var contentView = UIView()

    private lazy var textCard: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = s(12)
        v.layer.masksToBounds = true
        return v
    }()

    private lazy var textView: JKPlaceHolderTextView = {
        let tv = JKPlaceHolderTextView()
        tv.font = LNFont.regular(14)
        tv.textColor = UIColor.label
        tv.placeHolder = "Please Enter Feedback Content"
        tv.delegate = self
        tv.isScrollEnabled = false
        return tv
    }()

    private lazy var photoSlot: UIControl = {
        let c = UIControl()
        c.layer.cornerRadius = s(12)
        c.layer.masksToBounds = true
        c.backgroundColor = UIColor.white
        c.addTarget(self, action: #selector(photoTapped), for: .touchUpInside)
        return c
    }()

    private lazy var cameraIcon: UIImageView = {
        let iv = UIImageView(image: UIImage(systemName: "camera"))
        iv.tintColor = UIColor.systemGray
        iv.contentMode = .scaleAspectFit
        return iv
    }()

    private lazy var selectedImageView: UIImageView = {
        let iv = UIImageView()
        iv.isHidden = true
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        return iv
    }()

    private lazy var submitButton: UIButton = {
        let b = UIButton(type: .system)
        b.setTitle("Submit", for: .normal)
        b.setTitleColor(.white, for: .normal)
        b.titleLabel?.font = LNFont.regular(16)
        b.layer.cornerRadius = 24
        b.layer.masksToBounds = true
        b.addTarget(self, action: #selector(submitTapped), for: .touchUpInside)
        return b
    }()
    private let submitGradient = CAGradientLayer()

    // MARK: - State
    private var selectedImage: UIImage?
    private var isSubmitting = false // 防止重复提交

    // MARK: - Life Cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Help and Feedback"
        view.backgroundColor = UIColor.systemGroupedBackground
        navigationItem.largeTitleDisplayMode = .never
        setupUI()
        setupConstraints()
        setupKeyboardHandling()
        applyGradient()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        submitGradient.frame = submitButton.bounds
    }

    // MARK: - Setup
    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        view.addSubview(submitButton)

        contentView.addSubview(textCard)
        textCard.addSubview(textView)

        contentView.addSubview(photoSlot)
        photoSlot.addSubview(selectedImageView)
        photoSlot.addSubview(cameraIcon)

        submitGradient.colors = LNGradient.primaryColors
        submitGradient.startPoint = LNGradient.primaryStartPoint
        submitGradient.endPoint = LNGradient.primaryEndPoint
        submitButton.layer.insertSublayer(submitGradient, at: 0)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalTo(view.safeAreaLayoutGuide)
            make.bottom.equalTo(submitButton.snp.top).offset(-s(16))
        }
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        textCard.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(180)
        }
        textView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(12)
        }

        photoSlot.snp.makeConstraints { make in
            make.top.equalTo(textCard.snp.bottom).offset(s(22))
            make.left.equalTo(textCard)
            make.width.height.equalTo(s(112))
            make.bottom.equalToSuperview().offset(-s(24))
        }
        cameraIcon.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(s(40))
        }
        selectedImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        submitButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(s(24))
            make.height.equalTo(s(48))
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-s(24))
        }
    }

    private func applyGradient() { /* frame 在 viewDidLayoutSubviews 设置 */ }

    // MARK: - Actions
    @objc private func photoTapped() {
        showImagePicker()
    }

    private func showImagePicker() {
        let modal = LNAvatarModal()
        modal.showRemoveOption = selectedImage != nil
        modal.onSelectFromAlbum = { [weak self] in
            self?.checkPhotoLibraryPermission()
        }
        modal.onTakePhoto = { [weak self] in
            self?.checkCameraPermission()
        }
        modal.onRemovePhoto = { [weak self] in
            self?.removeSelectedImage()
        }
        modal.onCancel = {
            // 用户取消选择
        }
        // 使用 keyWindow 来覆盖整个屏幕包括导航栏
        if let keyWindow = JKPOP.keyWindow {
            modal.show(in: keyWindow)
        } else {
            modal.show(in: self.view)
        }
    }

    private func removeSelectedImage() {
        selectedImage = nil
        selectedImageView.image = nil
        selectedImageView.isHidden = true
        cameraIcon.isHidden = false
    }

    private func checkPhotoLibraryPermission() {
        let status = PHPhotoLibrary.authorizationStatus()
        switch status {
        case .authorized:
            presentImagePicker(sourceType: .photoLibrary)
        case .denied, .restricted:
            showPermissionAlert(for: "相册")
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization { [weak self] newStatus in
                DispatchQueue.main.async {
                    self?.handlePhotoLibraryAuthorizationStatus(newStatus)
                }
            }
        case .limited:
            presentImagePicker(sourceType: .photoLibrary)
        @unknown default:
            // iOS 14+ 的 .limited 状态也允许访问
            presentImagePicker(sourceType: .photoLibrary)
        }
    }

    private func handlePhotoLibraryAuthorizationStatus(_ status: PHAuthorizationStatus) {
        if status == .authorized {
            presentImagePicker(sourceType: .photoLibrary)
        } else if #available(iOS 14, *), status == .limited {
            presentImagePicker(sourceType: .photoLibrary)
        } else {
            showPermissionAlert(for: "相册")
        }
    }

    private func checkCameraPermission() {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        switch status {
        case .authorized:
            presentImagePicker(sourceType: .camera)
        case .denied, .restricted:
            showPermissionAlert(for: "相机")
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                DispatchQueue.main.async {
                    if granted {
                        self?.presentImagePicker(sourceType: .camera)
                    } else {
                        self?.showPermissionAlert(for: "相机")
                    }
                }
            }
        @unknown default:
            showPermissionAlert(for: "相机")
        }
    }

    private func presentImagePicker(sourceType: UIImagePickerController.SourceType) {
        guard UIImagePickerController.isSourceTypeAvailable(sourceType) else {
            let message = sourceType == .camera ? "设备不支持相机功能" : "设备不支持相册功能"
            showAlert(title: "提示", message: message)
            return
        }

        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = self
        picker.allowsEditing = true
        present(picker, animated: true)
    }

    private func showPermissionAlert(for feature: String) {
        let alert = UIAlertController(
            title: "需要\(feature)权限",
            message: "请在设置中允许访问\(feature)以选择图片",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "去设置", style: .default) { _ in
            if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsURL)
            }
        })
        present(alert, animated: true)
    }

    @objc private func submitTapped() {
        // 防止重复提交
        guard !isSubmitting else { return }

        let text = textView.text.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !text.isEmpty else {
            showAlert(title: "提示", message: "请输入反馈内容。")
            return
        }

        // 检查用户是否登录
        guard let user = LNUserManager.shared.userModel else {
            showAlert(title: "提示", message: "请先登录后再提交反馈。")
            return
        }

        isSubmitting = true
        showLoadingIndicator()

        // 如果有选择图片，先上传图片
        if let image = selectedImage {
            uploadImageAndSubmitFeedback(image: image, content: text, user: user)
        } else {
            // 没有图片，直接提交反馈
            submitFeedback(imageUrls: [], content: text, user: user)
        }
    }

    // MARK: - UIImagePickerControllerDelegate
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)

        // 优先使用编辑后的图片，如果没有则使用原图
        let image = info[.editedImage] as? UIImage ?? info[.originalImage] as? UIImage

        if let selectedImg = image {
            selectedImage = selectedImg
            selectedImageView.image = selectedImg
            selectedImageView.isHidden = false
            cameraIcon.isHidden = true
        }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }

    // MARK: - Feedback Submission

    /// 上传图片并提交反馈
    private func uploadImageAndSubmitFeedback(image: UIImage, content: String, user: LNUserModel) {
        LNImageUploadManager.shared.uploadImage(
            image,
            compressionQuality: 0.7,
            progress: nil,
            success: { [weak self] imageUrl in
                guard let self = self else { return }
                Log("图片上传成功，URL: \(imageUrl)")
                self.submitFeedback(imageUrls: [imageUrl], content: content, user: user)
            },
            failure: { [weak self] error in
                guard let self = self else { return }
                Log("图片上传失败: \(error.localizedDescription)")
                self.hideLoadingIndicator()
                self.isSubmitting = false
                self.showAlert(title: "上传失败", message: "图片上传失败，请重试。\n错误信息：\(error.localizedDescription)")
            }
        )
    }

    /// 提交反馈到服务器
    private func submitFeedback(imageUrls: [String], content: String, user: LNUserModel) {
        // 构建反馈接口参数
        let parameters: [String: Any] = [
            "fileUrlList": imageUrls,
            "reportContent": content,
            "reportUserHeadFileName": user.headFileName,
            "reportUserId": user.id,
            "reportUserNickName": user.nickName,
            "reportUserRole": "\(user.userRole)",
            "userId": user.id
        ]

        Log("提交反馈参数: \(parameters)")

        NetWorkRequest(
            LNApiProfile.userFeedback(par: parameters),
            completion: { [weak self] result in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    self.hideLoadingIndicator()
                    self.isSubmitting = false

                    Log("反馈提交成功: \(result)")

                    // 显示成功提示
                    self.showSuccessAlert()
                }
            },
            failure: { [weak self] error in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    self.hideLoadingIndicator()
                    self.isSubmitting = false

                    Log("反馈提交失败: \(error.localizedDescription)")
                    self.showAlert(title: "提交失败", message: "反馈提交失败，请重试。\n错误信息：\(error.localizedDescription)")
                }
            }
        )
    }

    // MARK: - UI Helper Methods

    /// 显示加载指示器
    private func showLoadingIndicator() {
        submitButton.isEnabled = false
        submitButton.setTitle("提交中...", for: .normal)
        submitButton.alpha = 0.6
    }

    /// 隐藏加载指示器
    private func hideLoadingIndicator() {
        submitButton.isEnabled = true
        submitButton.setTitle("Submit", for: .normal)
        submitButton.alpha = 1.0
    }

    /// 显示通用提示框
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    /// 显示成功提示并清空表单
    private func showSuccessAlert() {
        let alert = UIAlertController(title: "提交成功", message: "感谢您的反馈！我们会认真处理您的建议。", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "好的", style: .default) { [weak self] _ in
            self?.clearForm()
            self?.navigationController?.popViewController(animated: true)
        })
        present(alert, animated: true)
    }

    /// 清空表单
    private func clearForm() {
        textView.text = ""
        selectedImage = nil
        selectedImageView.image = nil
        selectedImageView.isHidden = true
        cameraIcon.isHidden = false
        view.endEditing(true)
    }

    // MARK: - Keyboard handling
    private func setupKeyboardHandling() {
        NotificationCenter.default.addObserver(self, selector: #selector(kbChange(_:)), name: UIResponder.keyboardWillChangeFrameNotification, object: nil)
        let tap = UITapGestureRecognizer(target: self, action: #selector(endEditingTap))
        tap.cancelsTouchesInView = false
        view.addGestureRecognizer(tap)
    }
    @objc private func endEditingTap() { view.endEditing(true) }
    @objc private func kbChange(_ note: Notification) {
        guard let userInfo = note.userInfo,
              let endFrame = (userInfo[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue,
              let duration = userInfo[UIResponder.keyboardAnimationDurationUserInfoKey] as? TimeInterval else { return }
        let insets = max(0, view.bounds.maxY - view.convert(endFrame, from: nil).minY)
        UIView.animate(withDuration: duration) {
            self.scrollView.contentInset.bottom = insets + 12
            self.scrollView.verticalScrollIndicatorInsets.bottom = insets
        }
    }
}

