//
//  LNPersonInfoUsageExample.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/28.
//

import UIKit

/// 个人信息页面使用示例
/// 展示如何调用三个接口获取数据和使用优化后的数据模型
class LNPersonInfoUsageExample {
    
    /// 示例：如何使用LNPersonInfoController
    static func showPersonInfo(userBean: LNUserModel, from viewController: UIViewController) {
        let personInfoVC = LNPersonInfoController()
        personInfoVC.setUserData(userBean)

        // 推送到个人信息页面
        viewController.navigationController?.pushViewController(personInfoVC, animated: true)
    }
    
    /// 示例：直接调用三个接口的方法
    static func loadPersonData(anchorId: String) {
        // 1. 获取主播详情信息
        getPersonInfo(anchorId: anchorId)
        
        // 2. 获取关注状态
        getFollowFlag(anchorId: anchorId)
        
        // 3. 获取用户礼物列表
        getUserGift(anchorId: anchorId)
    }
    
    /// 获取主播详情信息
    /// 对应Flutter中的 getPersonInfo 方法
    /// - Parameter anchorId: 主播ID
    private static func getPersonInfo(anchorId: String) {
        let params = ["id": anchorId]
        
        NetWorkRequest(LNApiAnchor.anchorDetail(par: params), completion: { result in
            // 解析用户信息
            if let data = result["data"] as? [String: Any],
               let userBean = LNUserModel.deserialize(from: data) {
                
                print("=== 获取主播详情成功 ===")
                print("主播ID: \(userBean.id)")
                print("昵称: \(userBean.nickName)")
                print("国家: \(userBean.country)")
                print("年龄: \(userBean.age)")
                print("性别: \(userBean.gender)")
                
                // 处理文件列表 - 使用优化后的模型
                if !userBean.anchorFileList.isEmpty {
                    var imageList: [LNAnchorFileModel] = []
                    var videoList: [LNAnchorFileModel] = []
                    
                    for file in userBean.anchorFileList {
                        print("文件详情:")
                        print("  - ID: \(file.id)")
                        print("  - 主播ID: \(file.anchorId)")
                        print("  - 类型: \(file.fileType)")
                        print("  - URL: \(file.displayUrl)")
                        print("  - 缩略图: \(file.displayThumbnail)")
                        print("  - 状态: \(file.status)")
                        print("  - 是否锁定: \(file.isLocked)")
                        
                        if file.isImage {
                            imageList.append(file)
                        } else if file.isVideo {
                            videoList.append(file)
                        }
                    }
                    
                    print("图片数量: \(imageList.count)")
                    print("视频数量: \(videoList.count)")
                }
                
                // 处理标签列表 - 使用优化后的模型
                print("标签数量: \(userBean.anchorLabelList.count)")
                for label in userBean.anchorLabelList {
                    print("标签详情:")
                    print("  - ID: \(label.id)")
                    print("  - 名称: \(label.displayName)")
                    print("  - 点赞数: \(label.displayLikeCount)")
                    print("  - 有点赞: \(label.hasLikes)")
                }
            }
            
        }, failure: { error in
            print("获取主播详情失败: \(error.localizedDescription)")
        })
    }
    
    /// 获取关注状态
    /// 对应Flutter中的 getFollowFlag 方法
    /// - Parameter anchorId: 主播ID
    private static func getFollowFlag(anchorId: String) {
        let params = ["id": anchorId]
        
        NetWorkRequest(LNApiProfile.followFlag(par: params), completion: { result in
            // 解析关注状态
            if let data = result["data"] as? String {
                let isFollowed = (data == "1")
                print("=== 获取关注状态成功 ===")
                print("是否已关注: \(isFollowed ? "是" : "否")")
            }
            
        }, failure: { error in
            print("获取关注状态失败: \(error.localizedDescription)")
        })
    }
    
    /// 获取用户礼物列表
    /// 对应Flutter中的 getUserGift 方法
    /// - Parameter anchorId: 主播ID
    private static func getUserGift(anchorId: String) {
        let params = ["anchorId": anchorId]
        
        NetWorkRequest(LNApiProfile.userGiftList(par: params), completion: { result in
            // 解析礼物列表
            if let data = result["data"] as? [[String: Any]] {
                let giftList = data.compactMap { LNGiftModel.deserialize(from: $0) }
                
                print("=== 获取礼物列表成功 ===")
                print("礼物数量: \(giftList.count)")
                
                for gift in giftList {
                    print("礼物详情:")
                    print("  - ID: \(gift.giftId)")
                    print("  - 名称: \(gift.name)")
                    print("  - 价格: \(gift.price)")
                    print("  - 图片: \(gift.imageUrl)")
                    print("  - 特殊: \(gift.isSpecial)")
                    print("  - 类型: \(gift.type)")
                }
            }
            
        }, failure: { error in
            print("获取礼物列表失败: \(error.localizedDescription)")
        })
    }
}

/// 扩展：为LNPersonInfoController添加便捷初始化方法
extension LNPersonInfoController {

    /// 便捷初始化方法
    /// - Parameter userBean: 用户数据
    convenience init(userBean: LNUserModel) {
        self.init()
        self.setUserData(userBean)
    }

    /// 静态方法：快速创建并推送个人信息页面
    /// - Parameters:
    ///   - userBean: 用户数据
    ///   - fromViewController: 来源视图控制器
    static func show(userBean: LNUserModel, from viewController: UIViewController) {
        let personInfoVC = LNPersonInfoController(userBean: userBean)
        viewController.navigationController?.pushViewController(personInfoVC, animated: true)
    }
}

/// 数据模型对比说明
/// 
/// Flutter AnchorFileBean 对应 Swift LNAnchorFileModel:
/// - id: Int -> id: Int
/// - anchorId: String -> anchorId: String  
/// - fileType: String -> fileType: String
/// - fileUrl: String -> fileUrl: String
/// - fileName: String -> fileName: String
/// - thumbnail: String -> thumbnail: String (新增)
/// - status: String -> status: String (新增)
/// - isLock: String -> isLock: String (新增)
///
/// Flutter AnchorLabelBean 对应 Swift LNAnchorLabelModel:
/// - id: Int -> id: Int
/// - labelName: String -> labelName: String
/// - likeCount: Int -> likeCount: Int (新增)
