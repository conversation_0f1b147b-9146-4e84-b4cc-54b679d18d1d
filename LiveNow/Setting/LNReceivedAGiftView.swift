//
//  LNReceivedAGiftView.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/18.
//

import UIKit
import SnapKit

/// Received a Gift 横向滑动视图
/// 展示用户收到的礼物集合，支持横向滑动浏览
class LNReceivedAGiftView: UIView {
    
    // MARK: - Properties
    private let gifts = [
        GiftItem(name: "<PERSON>", count: 10, emoji: "🌹"),
        GiftItem(name: "Heart", count: 10, emoji: "💖"),
        GiftItem(name: "Diamond", count: 10, emoji: "💎"),
        GiftItem(name: "Crown", count: 10, emoji: "👑"),
        GiftItem(name: "Star", count: 10, emoji: "⭐")
    ]
    
    // MARK: - UI Elements
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "🎁 Received a gift"
        label.font = LNFont.bold(16)
        label.textColor = UIColor.hex(hexString: "#04E798")
        return label
    }()
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = s(10)
        layout.minimumInteritemSpacing = 0
        layout.sectionInset = UIEdgeInsets(top: 0, left: s(15), bottom: 0, right: s(15))
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(LNGiftCell.self, forCellWithReuseIdentifier: "LNGiftCell")
        return collectionView
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        
        addSubview(titleLabel)
        addSubview(collectionView)
    }
    
    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.left.equalToSuperview().offset(s(15))
            make.right.equalToSuperview().offset(-s(15))
        }
        
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview()
            make.height.equalTo(s(100))
            make.bottom.equalToSuperview().offset(-s(15))
        }
    }
}

// MARK: - UICollectionViewDataSource
extension LNReceivedAGiftView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return gifts.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "LNGiftCell", for: indexPath) as! LNGiftCell
        cell.configure(with: gifts[indexPath.item])
        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension LNReceivedAGiftView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let size = s(80)
        return CGSize(width: size, height: size)
    }
}

// MARK: - UICollectionViewDelegate
extension LNReceivedAGiftView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let gift = gifts[indexPath.item]
        print("Gift selected: \(gift.name)")
        // TODO: 实现礼物详情功能
    }
}

// MARK: - GiftItem Model
struct GiftItem {
    let name: String
    let count: Int
    let emoji: String
}

// MARK: - LNGiftCell
class LNGiftCell: UICollectionViewCell {
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = s(8)
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowRadius = 4
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        return view
    }()
    
    private lazy var emojiLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: s(30))
        label.textAlignment = .center
        return label
    }()
    
    private lazy var countLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.bold(14)
        label.textColor = .black
        label.textAlignment = .center
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(containerView)
        containerView.addSubview(emojiLabel)
        containerView.addSubview(countLabel)
        
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        emojiLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(10))
            make.centerX.equalToSuperview()
        }
        
        countLabel.snp.makeConstraints { make in
            make.top.equalTo(emojiLabel.snp.bottom).offset(s(5))
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-s(10))
        }
    }
    
    func configure(with gift: GiftItem) {
        emojiLabel.text = gift.emoji
        countLabel.text = "\(gift.count)"
    }
}
