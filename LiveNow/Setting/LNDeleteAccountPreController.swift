//
//  LNDeleteAccountPreController.swift
//  LiveNow
//
//  根据设计图实现的“删除账号-前置确认”页面
//  iOS 13+，使用 SnapKit 布局与 s(xx) 适配；继承 LNBaseController
//

import UIKit
import SnapKit
import JKSwiftExtension

// 不需要这个页面，直接展示LNDeleteAccountViewController页面即可
// 点击提交，直接调用注销功能接口
final class LNDeleteAccountPreController: LNBaseController, UITextViewDelegate {

    // 使用白色导航栏
    override var navigationSolidColor: UIColor { return .white }
    override var navigationTitleColor: UIColor { return .label }

    // MARK: - UI
    private lazy var scrollView: UIScrollView = {
        let v = UIScrollView()
        v.alwaysBounceVertical = true
        v.keyboardDismissMode = .onDrag
        return v
    }()

    private lazy var contentView = {
      let v = UIView()
      v.backgroundColor = UIColor.white
      v.layer.cornerRadius = s(8)
      v.layer.masksToBounds = true
      return v
    }()

    // 顶部信息卡片
    private lazy var infoCard: UIView = {
        let v = UIView()
        return v
    }()

    private lazy var titleLabel: UILabel = {
        let l = UILabel()
        l.text = "Delete account"
        l.font = LNFont.bold(20)
        l.textColor = .label
        l.numberOfLines = 1
        return l
    }()

    private lazy var subtitleLabel: UILabel = {
        let l = UILabel()
        l.text = "Permanently Cancelled And Cannot Be Restored Please Proceed With Caution"
        l.font = LNFont.forTextStyle(.subheadline)
        l.textColor = UIColor.secondaryLabel
        l.numberOfLines = 0
        l.adjustsFontForContentSizeCategory = true
        return l
    }()

    // 选择原因标题
    private lazy var reasonHeaderLabel: UILabel = {
        let l = UILabel()
        l.text = "Choose cancellation reason"
        l.font = LNFont.bold(18)
        l.textColor = .label
        return l
    }()

    // 单选项（使用按钮 + 标签）
    private lazy var reasonOptionViews: [UIControl] = {
        let titles = ["注销原因一", "注销原因二", "注销原因三"]
        return titles.enumerated().map { (idx, title) in
            let c = UIControl()
            c.accessibilityLabel = "reason_option_\(idx)"

            let icon = UIImageView()
            icon.tag = 1001 // 圆圈图标
            icon.tintColor = UIColor.hex(hexString: "#00DFAB")
            icon.image = UIImage(systemName: "circle")

            let l = UILabel()
            l.text = title
            l.font = LNFont.forTextStyle(.body)
            l.textColor = .label

            c.addSubview(icon)
            c.addSubview(l)
            icon.snp.makeConstraints { make in
                make.left.equalToSuperview()
                make.centerY.equalToSuperview()
                make.width.height.equalTo(s(18))
            }
            l.snp.makeConstraints { make in
                make.left.equalTo(icon.snp.right).offset(s(10))
                make.right.equalToSuperview()
                make.top.bottom.equalToSuperview().inset(s(10))
            }
            c.addTarget(self, action: #selector(reasonTapped(_:)), for: .touchUpInside)
            return c
        }
    }()

    // 填写原因标题
    private lazy var inputHeaderLabel: UILabel = {
        let l = UILabel()
        l.text = "Please fill in the reason for cancellation"
        l.font = LNFont.bold(18)
        l.textColor = .label
        return l
    }()

    private lazy var inputContainer: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor(hexString: "#F5F5F5")
        v.layer.cornerRadius = s(10)
        v.layer.masksToBounds = true
        return v
    }()

    private lazy var inputTextView: JKPlaceHolderTextView = {
        let tv = JKPlaceHolderTextView()
        tv.font = LNFont.forTextStyle(.body)
        tv.placeHolder = "Please Enter..."
        tv.textColor = .label
        tv.backgroundColor = .clear
        tv.delegate = self
        tv.isScrollEnabled = false
        return tv
    }()

    // 下一步按钮
    private lazy var nextButton: UIButton = {
        let b = UIButton(type: .system)
        b.setTitle("Next step", for: .normal)
        b.setTitleColor(.white, for: .normal)
        b.titleLabel?.font = LNFont.forTextStyle(.headline)
        b.layer.cornerRadius = s(24)
        b.layer.masksToBounds = true
        b.isEnabled = false
        b.backgroundColor = UIColor.systemGray3
        b.addTarget(self, action: #selector(nextStepTapped), for: .touchUpInside)
        return b
    }()
    private let nextGradient = CAGradientLayer()

    // MARK: - State
    private var selectedReasonIndex: Int? { didSet { updateReasonSelectionUI() } }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Delete account"
        setupUI()
        setupConstraints()
        updateNextButtonState()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        nextGradient.frame = nextButton.bounds
    }

    deinit {
    }

    // MARK: - Setup
    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        view.addSubview(nextButton)

        // info card
        contentView.addSubview(infoCard)
        infoCard.addSubview(titleLabel)
        infoCard.addSubview(subtitleLabel)

        // reason header
        contentView.addSubview(reasonHeaderLabel)
        // options
        reasonOptionViews.forEach { contentView.addSubview($0) }

        // input
        contentView.addSubview(inputHeaderLabel)
        contentView.addSubview(inputContainer)
        inputContainer.addSubview(inputTextView)

        // next button gradient setup
        nextGradient.colors = LNGradient.primaryColors
        nextGradient.startPoint = LNGradient.primaryStartPoint
        nextGradient.endPoint = LNGradient.primaryEndPoint
        nextButton.layer.insertSublayer(nextGradient, at: 0)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(nextButton.snp.top).offset(-s(16))
        }

        contentView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(16))
            make.left.right.equalToSuperview().inset(s(16))
            make.width.equalTo(kscreenW - s(32))
        }

        infoCard.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(16))
            make.left.right.equalToSuperview().inset(s(16))
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview()
            make.right.equalToSuperview()
        }
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(8))
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }

        reasonHeaderLabel.snp.makeConstraints { make in
            make.top.equalTo(infoCard.snp.bottom).offset(s(18))
            make.left.right.equalTo(infoCard)
        }

        // 三个选项垂直排布
        var previous: UIView = reasonHeaderLabel
        for v in reasonOptionViews {
            v.snp.makeConstraints { make in
                make.top.equalTo(previous.snp.bottom).offset(s(12))
                make.left.right.equalTo(infoCard)
            }
            previous = v
        }

        inputHeaderLabel.snp.makeConstraints { make in
            make.top.equalTo(previous.snp.bottom).offset(s(18))
            make.left.right.equalTo(infoCard)
        }

        inputContainer.snp.makeConstraints { make in
            make.top.equalTo(inputHeaderLabel.snp.bottom).offset(s(12))
            make.left.right.equalTo(infoCard)
        }

        inputTextView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(s(8))
            make.height.greaterThanOrEqualTo(s(120))
        }

        // 为 contentView 添加底部约束，确保滚动内容高度正确
        inputContainer.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-s(24))
        }

        nextButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(s(24))
            make.height.equalTo(s(48))
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-s(24))
        }
    }

    // MARK: - Actions
    @objc private func reasonTapped(_ sender: UIControl) {
        guard let index = reasonOptionViews.firstIndex(of: sender) else { return }
        selectedReasonIndex = index
        updateNextButtonState()
    }

    @objc private func nextStepTapped() {
        let vc = LNDeleteAccountViewController()
        navigationController?.pushViewController(vc, animated: true)
    }

    // MARK: - Helpers
    private func updateReasonSelectionUI() {
        for (idx, v) in reasonOptionViews.enumerated() {
            let icon = v.viewWithTag(1001) as? UIImageView
            if selectedReasonIndex == idx {
                icon?.image = UIImage(systemName: "checkmark.circle.fill")
            } else {
                icon?.image = UIImage(systemName: "circle")
            }
        }
    }

    private func canProceed() -> Bool {
        let hasReason = (selectedReasonIndex != nil)
        let hasCustomText = !(inputTextView.text?.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ?? true)
        return hasReason || hasCustomText
    }

    private func updateNextButtonState() {
        let enabled = canProceed()
        nextButton.isEnabled = enabled
        if enabled {
            nextButton.backgroundColor = .clear
            nextGradient.isHidden = false
        } else {
            nextGradient.isHidden = true
            nextButton.backgroundColor = UIColor.systemGray3
        }
    }


    // MARK: - UITextViewDelegate
    func textViewDidChange(_ textView: UITextView) {
        updateNextButtonState()
    }
}


