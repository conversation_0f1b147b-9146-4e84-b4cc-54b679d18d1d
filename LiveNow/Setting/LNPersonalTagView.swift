//
//  LNPersonalTagView.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/18.
//

import UIKit
import SnapKit

/// Personal Profile 标签视图
/// 展示用户的个人兴趣标签，如 Cooking、Tourism、Listen to music 等
class LNPersonalTagView: UIView {
    
    // MARK: - Properties
    private let tags = ["Cooking", "Tourism", "Listen to music", "Listen to music", "Listen to music", "Tourism"]
    
    // MARK: - UI Elements
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "🌟 Personal Profile"
        label.font = LNFont.bold(16)
        label.textColor = UIColor.hex(hexString: "#04E798")
        return label
    }()
    
    private lazy var tagsContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupTags()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func layoutSubviews() {
        super.layoutSubviews()

        // 更新渐变层的尺寸
        if let gradientLayer = layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = bounds
        }
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        setupGradientBackground()
        layer.cornerRadius = s(12)

        addSubview(titleLabel)
        addSubview(tagsContainerView)
    }

    private func setupGradientBackground() {
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.hex(hexString: "#F0F9FF").cgColor,
            UIColor.hex(hexString: "#F0F9FF").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)
        gradientLayer.cornerRadius = s(12)

        layer.insertSublayer(gradientLayer, at: 0)

        // 确保渐变层在布局时更新尺寸
        DispatchQueue.main.async {
            gradientLayer.frame = self.bounds
        }
    }
    
    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.left.equalToSuperview().offset(s(15))
            make.right.equalToSuperview().offset(-s(15))
        }
        
        tagsContainerView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview().inset(s(15))
            make.bottom.equalToSuperview().offset(-s(15))
        }
    }
    
    private func setupTags() {
        var currentX: CGFloat = 0
        var currentY: CGFloat = 0
        let tagSpacing: CGFloat = s(8)
        let lineSpacing: CGFloat = s(10)
        let containerWidth = kscreenW - s(70) // 减去左右边距和容器边距
        
        for tag in tags {
            let tagButton = createTagButton(with: tag)
            tagsContainerView.addSubview(tagButton)
            
            // 计算标签尺寸
            let tagSize = tagButton.intrinsicContentSize
            let tagWidth = tagSize.width + s(20) // 增加内边距
            let tagHeight = s(30)
            
            // 检查是否需要换行
            if currentX + tagWidth > containerWidth && currentX > 0 {
                currentX = 0
                currentY += tagHeight + lineSpacing
            }
            
            // 设置标签位置
            tagButton.snp.makeConstraints { make in
                make.left.equalToSuperview().offset(currentX)
                make.top.equalToSuperview().offset(currentY)
                make.width.equalTo(tagWidth)
                make.height.equalTo(tagHeight)
            }
            
            currentX += tagWidth + tagSpacing
        }
        
        // 更新容器高度
        let totalHeight = currentY + s(30)
        tagsContainerView.snp.makeConstraints { make in
            make.height.equalTo(totalHeight)
        }
    }
    
    private func createTagButton(with text: String) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(text, for: .normal)
        button.titleLabel?.font = LNFont.regular(14)
        button.setTitleColor(UIColor.hex(hexString: "#04E798"), for: .normal)
        button.backgroundColor = UIColor.hex(hexString: "#04E798").withAlphaComponent(0.1)
        button.layer.cornerRadius = s(15)
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.hex(hexString: "#04E798").withAlphaComponent(0.3).cgColor
        
        // 添加点击事件
        button.addTarget(self, action: #selector(tagButtonTapped(_:)), for: .touchUpInside)
        
        return button
    }
    
    // MARK: - Event Handlers
    @objc private func tagButtonTapped(_ sender: UIButton) {
        guard let title = sender.title(for: .normal) else { return }
        print("Tag tapped: \(title)")
        
        // 添加点击动画
        UIView.animate(withDuration: 0.1, animations: {
            sender.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                sender.transform = .identity
            }
        }
        
        // TODO: 处理标签点击事件
    }
}
