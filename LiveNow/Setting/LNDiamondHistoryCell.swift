//
//  LNDiamondHistoryCell.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/08/13.
//

import UIKit
import SnapKit

final class LNDiamondHistoryCell: UITableViewCell {
    static let reuseId = "LNDiamondHistoryCell"

    private lazy var card: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.systemBackground
        v.layer.cornerRadius = 12
        v.layer.masksToBounds = true
        return v
    }()
    private lazy var diamondLabel: UILabel = {
        let l = UILabel(); l.font = LNFont.regular(16); l.textColor = UIColor.label; return l
    }()
    private lazy var diamondIcon: UIImageView = {
        let iv = UIImageView(image: UIImage(named: "ic_diamond"))
        iv.contentMode = .scaleAspectFit
        return iv
    }()
    private lazy var priceLabel: UILabel = {
        let l = UILabel(); l.font = LNFont.regular(16); l.textAlignment = .right; l.textColor = UIColor.label; return l
    }()
    private lazy var timeLabel: UILabel = {
        let l = UILabel(); l.font = LNFont.regular(12); l.textColor = UIColor.secondaryLabel; return l
    }()
    private lazy var statusLabel: UILabel = {
        let l = UILabel(); l.font = LNFont.regular(12); l.textAlignment = .right; return l
    }()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        backgroundColor = .clear
        contentView.backgroundColor = .clear

        contentView.addSubview(card)
        card.addSubview(diamondLabel)
        card.addSubview(diamondIcon)
        card.addSubview(priceLabel)
        card.addSubview(timeLabel)
        card.addSubview(statusLabel)

        card.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(s(16))
            make.top.equalToSuperview().offset(s(8))
            make.bottom.equalToSuperview()
        }
        diamondLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(10))
            make.left.equalToSuperview().offset(s(16))
        }
        diamondIcon.snp.makeConstraints { make in
            make.centerY.equalTo(diamondLabel).offset(s(-2))
            make.left.equalTo(diamondLabel.snp.right).offset(s(8))
            make.width.height.equalTo(s(26))
            make.right.lessThanOrEqualTo(priceLabel.snp.left).offset(-s(8))
        }
        priceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(diamondLabel)
            make.right.equalToSuperview().offset(-s(16))
        }
        timeLabel.snp.makeConstraints { make in
            make.left.equalTo(diamondLabel)
            make.top.equalTo(diamondLabel.snp.bottom).offset(s(8))
            make.bottom.equalToSuperview().offset(-s(12))
            make.right.lessThanOrEqualTo(statusLabel.snp.left).offset(-s(8))
        }
        statusLabel.snp.makeConstraints { make in
            make.centerY.equalTo(timeLabel)
            make.right.equalTo(priceLabel)
        }

        priceLabel.setContentHuggingPriority(.required, for: .horizontal)
        priceLabel.setContentCompressionResistancePriority(.required, for: .horizontal)
        statusLabel.setContentHuggingPriority(.required, for: .horizontal)
        statusLabel.setContentCompressionResistancePriority(.required, for: .horizontal)
        diamondLabel.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)
        diamondLabel.setContentHuggingPriority(.defaultLow, for: .horizontal)
    }

    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }

    func configure(diamonds: Int, priceUSD: String, time: String, finished: Bool) {
        diamondLabel.text = "\(diamonds)"
        priceLabel.text = priceUSD
        timeLabel.text = time
        if finished {
            statusLabel.text = "Finished"
            statusLabel.textColor = UIColor.systemGreen
        } else {
            statusLabel.text = "Unfinished"
            statusLabel.textColor = UIColor.systemRed
        }
    }
}


