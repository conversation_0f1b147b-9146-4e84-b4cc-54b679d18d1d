//
//  LNDiamondHistoryViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/09.
//

import UIKit
import SnapKit
import MJRefresh
import HandyJSON

/// 充值记录（Diamond History）
/// - 卡片式列表：左侧钻石数量 + 图标，右侧价格；下方时间与状态（红色 Unfinished/绿色 Finished）
/// - 底部加载状态由 MJRefresh 处理
class LNDiamondHistoryViewController: LNBaseController, UITableViewDataSource, UITableViewDelegate {

    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }

    // 使用顶部渐变背景
    override var useTopGradientBackground: Bool { return true }

    // MARK: - Data
    private var data: [LNDiamondRecord] = []

    // 分页与加载状态
    private var current: Int = 1
    private let size: Int = 20
    private var hasMore: Bool = true

    // MARK: - UI
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.dataSource = self
        tv.delegate = self
        tv.separatorStyle = .none
        tv.backgroundColor = UIColor.systemGroupedBackground
        tv.register(LNDiamondHistoryCell.self, forCellReuseIdentifier: LNDiamondHistoryCell.reuseId)
        tv.rowHeight = s(68)

        // 配置空状态
        tv.em.emptyView = LNEmptyView.empty(
            firstReloadHidden: true,
            canTouch: false,
            isUserInteractionEnabled: false,
            offsetY: -s(80),
            space: s(15),
            backColor: .clear
        ) { config in
            config.image = UIImage(named: "ic_empty")
            config.imageSize = CGSize(width: s(120), height: s(120))
            config.titleTopSpace = s(20)
            config.title = "No data available for the time being."
            config.titleFont = LNFont.regular(16)
            config.titleColor = UIColor.systemGray
        } closure: { tag in
            print("Empty view tapped with tag: \(tag)")
        }

        return tv
    }()
    

    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Diamond History"
        edgesForExtendedLayout = .all

        view.addSubview(tableView)
        tableView.snp.makeConstraints {
            $0.top.left.right.equalTo(view.safeAreaLayoutGuide)
            $0.bottom.equalToSuperview()
        }
        setupRefresh()
        loadData(reset: true)
    }

    private func setupRefresh() {
        tableView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(onRefresh))
        tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(onLoadMore))
        // 初始时隐藏footer，直到有数据时才显示
        tableView.mj_footer?.isHidden = true
    }

    @objc private func onRefresh() {
        loadData(reset: true)
    }

    @objc private func onLoadMore() {
        loadData(reset: false)
    }

    private func loadData(reset: Bool) {
        if reset {
            current = 1
            hasMore = true
            tableView.mj_footer?.resetNoMoreData()
        }

        // 调用钻石记录接口
        let params = [
            "current": current,
            "size": size
        ]

        NetWorkRequest(LNApiProfile.diamondRecords(par: params), completion: { [weak self] result in
            guard let self = self else { return }

            // 解析响应数据
            if let dataDict = result["data"] as? [String: Any],
               let jsonData = try? JSONSerialization.data(withJSONObject: dataDict),
               let pageResponse = LNPageResponse<LNDiamondRecord>.deserialize(from: String(data: jsonData, encoding: .utf8)) {

                let newItems = pageResponse.records

                if reset {
                    self.data = newItems
                } else {
                    self.data.append(contentsOf: newItems)
                }

                // 更新分页信息
                self.current = pageResponse.current + 1
                self.hasMore = pageResponse.current < pageResponse.pages

                DispatchQueue.main.async {
                    self.tableView.reloadData()
                    self.tableView.mj_header?.endRefreshing()

                    // 只有当有数据时才处理footer状态
                    if self.data.isEmpty {
                        // 数据为空时，隐藏footer
                        self.tableView.mj_footer?.endRefreshing()
                        self.tableView.mj_footer?.isHidden = true
                    } else {
                        // 有数据时，显示footer并根据是否还有更多数据设置状态
                        self.tableView.mj_footer?.isHidden = false
                        if self.hasMore {
                            self.tableView.mj_footer?.endRefreshing()
                        } else {
                            self.tableView.mj_footer?.endRefreshingWithNoMoreData()
                        }
                    }
                }
            } else {
                // 解析失败，结束刷新
                DispatchQueue.main.async {
                    self.tableView.mj_header?.endRefreshing()
                    self.tableView.mj_footer?.endRefreshing()
                    // 如果数据为空，隐藏footer
                    if self.data.isEmpty {
                        self.tableView.mj_footer?.isHidden = true
                    }
                }
            }
        }, failure: { [weak self] error in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.tableView.mj_header?.endRefreshing()
                self.tableView.mj_footer?.endRefreshing()

                // 如果数据为空，隐藏footer
                if self.data.isEmpty {
                    self.tableView.mj_footer?.isHidden = true
                }

                // 显示错误提示
                print("钻石记录加载失败: \(error.localizedDescription)")
            }
        })
    }

    // MARK: - Table Datasource
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { data.count }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: LNDiamondHistoryCell.reuseId, for: indexPath) as! LNDiamondHistoryCell
        let record = data[indexPath.row]
        cell.configure(diamonds: record.displayDiamonds, priceUSD: record.displayPrice, time: record.displayTime, finished: record.isFinished)
        return cell
    }

    // Cell 已抽出为独立文件 LNDiamondHistoryCell.swift
}

