//
//  LNCustomerServiceViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/09.
//

import UIKit
import SnapKit

/// 客服中心页面（Customer Service）
/// - 设计要求：
///   - iOS 13+，深色模式与动态字体
///   - 使用 SnapKit 布局
///   - 卡片“Hot Questions”包含 3 个问题项（前1个为跳转样式，后2个支持展开/收起）
///   - 底部两个链接按钮与一个渐变色保存按钮
class LNCustomerServiceViewController: LNBaseController {

    // MARK: - 导航栏样式配置
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }

    // 使用圆弧形渐变背景
    override var useArcGradientBackground: Bool { return true }

    // MARK: - UI
    private lazy var scrollView: UIScrollView = {
        let v = UIScrollView()
        v.alwaysBounceVertical = true
        v.showsVerticalScrollIndicator = true
        v.keyboardDismissMode = .onDrag
        return v
    }()

    private lazy var contentView = UIView()

    private lazy var cardView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.systemBackground
        v.layer.cornerRadius = 16
        v.layer.masksToBounds = true
        // 卡片阴影外层由容器提供，这里仅圆角
        return v
    }()

    private lazy var cardShadowContainer: UIView = {
        let v = UIView()
        v.backgroundColor = .clear
        v.layer.shadowColor = UIColor.black.withAlphaComponent(0.12).cgColor
        v.layer.shadowOpacity = 1
        v.layer.shadowRadius = 12
        v.layer.shadowOffset = CGSize(width: 0, height: 4)
        return v
    }()

    private lazy var hotTitleLabel: UILabel = {
        let l = UILabel()
        l.text = "Hot Questions"
        l.font = LNFont.forTextStyle(.title2)
        l.textColor = UIColor.label
        l.adjustsFontForContentSizeCategory = true
        return l
    }()

    // 三个问题行
    private lazy var row1 = makeRow(title: "How to recharge?", style: .disclosure)
    private lazy var row2 = makeRow(title: "How to solve the recharge problem?", style: .expandable(tag: 2))
    private lazy var row3 = makeRow(title: "No verification code received?", style: .expandable(tag: 3))

    private lazy var linkHowToWorkButton: UIButton = makeLinkButton(title: "How to work for the APP?")
    private lazy var linkSuggestionsButton: UIButton = makeLinkButton(title: "I Have suggestions for the APP")

    private lazy var saveButton: UIButton = {
        let b = UIButton(type: .system)
        b.setTitle("save", for: .normal)
        b.setTitleColor(.white, for: .normal)
        b.titleLabel?.font = LNFont.forTextStyle(.headline)
        b.layer.cornerRadius = s(24)
        b.layer.masksToBounds = true
        b.accessibilityLabel = "save"
        b.addTarget(self, action: #selector(saveTapped), for: .touchUpInside)
        return b
    }()

    private let saveGradient = CAGradientLayer()

    // 可展开行状态表：tag -> (label, chevron, expanded)
    private var expandableMap: [Int: (label: UILabel, chevron: UIImageView, expanded: Bool)] = [:]

    // MARK: - Life Cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Customer Service"
        edgesForExtendedLayout = .all
        setupUI()
        setupConstraints()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        saveGradient.frame = saveButton.bounds
    }

    // MARK: - Setup
    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        // 卡片阴影容器 -> 卡片
        contentView.addSubview(cardShadowContainer)
        cardShadowContainer.addSubview(cardView)

        // 卡片内容
        cardView.addSubview(hotTitleLabel)
        cardView.addSubview(row1.container)
        cardView.addSubview(row2.container)
        cardView.addSubview(row3.container)
        cardView.addSubview(linkHowToWorkButton)
        cardView.addSubview(linkSuggestionsButton)

        // 底部按钮 - 固定在页面底部
        view.addSubview(saveButton)

        // 保存按钮渐变 - 使用项目主要渐变色
        saveGradient.colors = LNGradient.primaryColors
        saveGradient.startPoint = LNGradient.primaryStartPoint
        saveGradient.endPoint = LNGradient.primaryEndPoint
        saveButton.layer.insertSublayer(saveGradient, at: 0)
    }

    private func setupConstraints() {
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // 卡片布局
        cardShadowContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(16))
            make.left.right.equalToSuperview().inset(s(16))
        }
        cardView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        hotTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(16))
            make.left.equalToSuperview().offset(s(16))
            make.right.lessThanOrEqualToSuperview().offset(-s(16))
        }

        // 行高
        row1.container.snp.makeConstraints { make in
            make.top.equalTo(hotTitleLabel.snp.bottom).offset(s(12))
            make.left.right.equalToSuperview()
        }
        row2.container.snp.makeConstraints { make in
            make.top.equalTo(row1.container.snp.bottom)
            make.left.right.equalToSuperview()
        }
        row3.container.snp.makeConstraints { make in
            make.top.equalTo(row2.container.snp.bottom)
            make.left.right.equalToSuperview()
        }

        linkHowToWorkButton.snp.makeConstraints { make in
            make.top.equalTo(row3.container.snp.bottom).offset(s(12))
            make.left.equalToSuperview().offset(s(16))
        }
        linkSuggestionsButton.snp.makeConstraints { make in
            make.top.equalTo(linkHowToWorkButton.snp.bottom).offset(s(6))
            make.left.equalTo(linkHowToWorkButton)
            make.bottom.equalToSuperview().offset(-s(24)) // 给底部留出更多空间
        }

        // 保存按钮 - 固定在页面底部
        saveButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(s(24))
            make.height.equalTo(s(48))
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-s(24))
        }

        // 调整 scrollView 底部约束，为固定按钮留出空间
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalTo(view.safeAreaLayoutGuide)
            make.bottom.equalTo(saveButton.snp.top).offset(-s(16))
        }
    }

    // MARK: - Row Factory
    private enum RowStyle { case disclosure, expandable(tag: Int) }

    private func makeRow(title: String, style: RowStyle) -> (container: UIView, chevron: UIImageView) {
        let container = UIView()
        container.backgroundColor = .clear

        let separator = UIView()
        separator.backgroundColor = UIColor.separator
        container.addSubview(separator)
        separator.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(s(16))
            make.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }

        let button = UIControl()
        container.addSubview(button)
        button.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(s(56))
        }

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = LNFont.forTextStyle(.body)
        titleLabel.textColor = UIColor.label
        titleLabel.adjustsFontForContentSizeCategory = true

        let chevron = UIImageView()
        chevron.contentMode = .scaleAspectFit
        chevron.tintColor = UIColor.systemGray3

        button.addSubview(titleLabel)
        button.addSubview(chevron)

        titleLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(s(16))
            make.right.lessThanOrEqualTo(chevron.snp.left).offset(-s(8))
        }
        chevron.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-s(16))
            make.width.equalTo(s(12))
            make.height.equalTo(s(16))
        }

        switch style {
        case .disclosure:
            chevron.image = UIImage(systemName: "chevron.right")
            button.addTarget(self, action: #selector(row1Tapped), for: .touchUpInside)
            // disclosure 样式无额外内容，容器底部贴在按钮底部
            button.snp.makeConstraints { make in
                make.bottom.equalToSuperview()
            }

        case .expandable(let tag):
            chevron.image = UIImage(systemName: "chevron.down")
            button.tag = tag
            button.addTarget(self, action: #selector(expandableTapped(_:)), for: .touchUpInside)

            let answerLabel = UILabel()
            answerLabel.text = "Thanks for your question. Tap to expand for more details..."
            answerLabel.font = LNFont.forTextStyle(.subheadline)
            answerLabel.textColor = UIColor.secondaryLabel
            answerLabel.numberOfLines = 0
            answerLabel.isHidden = true
            container.addSubview(answerLabel)
            answerLabel.snp.makeConstraints { make in
                make.top.equalTo(button.snp.bottom).offset(0)
                make.left.right.equalToSuperview().inset(s(16))
                make.bottom.equalToSuperview().offset(-s(12))
            }

            expandableMap[tag] = (label: answerLabel, chevron: chevron, expanded: false)
        }

        return (container, chevron)
    }

    private func makeLinkButton(title: String) -> UIButton {
        let b = UIButton(type: .system)
        b.setTitle(title, for: .normal)
        b.titleLabel?.font = LNFont.forTextStyle(.body)
        b.setTitleColor(UIColor.systemTeal, for: .normal)
        b.contentHorizontalAlignment = .left
        b.addTarget(self, action: #selector(linkTapped(_:)), for: .touchUpInside)
        return b
    }

    // MARK: - Actions
    @objc private func row1Tapped() {
        let alert = UIAlertController(title: "How to recharge?", message: "This page will guide users to recharge steps.", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    @objc private func expandableTapped(_ sender: UIControl) {
        guard var state = expandableMap[sender.tag] else { return }
        state.expanded.toggle()
        state.label.isHidden = !state.expanded
        state.chevron.image = UIImage(systemName: state.expanded ? "chevron.up" : "chevron.down")
        expandableMap[sender.tag]?.expanded = state.expanded
        UIView.animate(withDuration: 0.25) { self.view.layoutIfNeeded() }
    }

    @objc private func linkTapped(_ sender: UIButton) {
        let title = sender.title(for: .normal) ?? "Link"
        let alert = UIAlertController(title: title, message: "Thanks for your feedback!", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    @objc private func saveTapped() {
        let alert = UIAlertController(title: "Saved", message: "Your settings have been saved.", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}

