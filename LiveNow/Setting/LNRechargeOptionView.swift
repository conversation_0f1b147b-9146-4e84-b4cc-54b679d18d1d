//
//  LNRechargeOptionView.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/17.
//

import UIKit
import SnapKit

/// 充值选项视图
/// - 显示钻石数量、奖励钻石、价格和折扣标签
/// - 支持点击交互和动画效果
class LNRechargeOptionView: UIView {
    
    // MARK: - Model
    struct Option {
        let diamonds: Int
        let bonusDiamonds: Int
        let priceUSD: String
        let discountLabel: String?
        
        var totalDiamonds: Int { return diamonds + bonusDiamonds }
    }
    
    // MARK: - Properties
    private var option: Option?
    var onTapped: ((Option) -> Void)?
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.systemBackground
        v.layer.cornerRadius = s(12)
        v.layer.masksToBounds = true
        
        // 添加轻微阴影效果
        v.layer.shadowColor = UIColor.black.cgColor
        v.layer.shadowOffset = CGSize(width: 0, height: 2)
        v.layer.shadowRadius = 4
        v.layer.shadowOpacity = 0.1
        v.layer.masksToBounds = false
        
        return v
    }()
    
    private lazy var discountView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.hex(hexString: "#FF6B35")
        v.layer.cornerRadius = s(8)
        v.isHidden = true
        return v
    }()
    
    private lazy var discountLabel: UILabel = {
        let l = UILabel()
        l.font = LNFont.medium(12)
        l.textColor = .white
        l.textAlignment = .center
        return l
    }()
    
    private lazy var diamondIcon: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "ic_diamond")
        iv.contentMode = .scaleAspectFit
        return iv
    }()
    
    private lazy var diamondLabel: UILabel = {
        let l = UILabel()
        l.font = LNFont.bold(18)
        l.textColor = UIColor.hex(hexString: "#333333")
        return l
    }()
    
    private lazy var bonusLabel: UILabel = {
        let l = UILabel()
        l.font = LNFont.medium(14)
        l.textColor = UIColor.hex(hexString: "#04E798")
        return l
    }()
    
    private lazy var priceLabel: UILabel = {
        let l = UILabel()
        l.font = LNFont.bold(16)
        l.textColor = .white
        l.textAlignment = .center
        l.backgroundColor = UIColor.hex(hexString: "#04E798")
        l.layer.cornerRadius = s(16)
        l.layer.masksToBounds = true
        return l
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupGesture()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        
        addSubview(containerView)
        containerView.addSubview(discountView)
        discountView.addSubview(discountLabel)
        containerView.addSubview(diamondIcon)
        containerView.addSubview(diamondLabel)
        containerView.addSubview(bonusLabel)
        containerView.addSubview(priceLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(s(68))
        }
        
        discountView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(8))
            make.left.equalToSuperview().offset(s(16))
            make.width.equalTo(s(60))
            make.height.equalTo(s(24))
        }
        
        discountLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(s(4))
        }
        
        diamondIcon.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(16))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(32))
        }
        
        diamondLabel.snp.makeConstraints { make in
            make.left.equalTo(diamondIcon.snp.right).offset(s(12))
            make.centerY.equalToSuperview().offset(s(-8))
        }
        
        bonusLabel.snp.makeConstraints { make in
            make.left.equalTo(diamondLabel.snp.right).offset(s(8))
            make.centerY.equalTo(diamondLabel)
        }
        
        priceLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(16))
            make.centerY.equalToSuperview()
            make.width.equalTo(s(80))
            make.height.equalTo(s(32))
        }
    }
    
    private func setupGesture() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(onTapped(_:)))
        addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Configuration
    func configure(with option: Option) {
        self.option = option
        
        diamondLabel.text = "\(option.diamonds)"
        bonusLabel.text = "+\(option.bonusDiamonds)"
        priceLabel.text = option.priceUSD
        
        if let discount = option.discountLabel {
            discountView.isHidden = false
            discountLabel.text = discount
        } else {
            discountView.isHidden = true
        }
    }
    
    // MARK: - Actions
    @objc private func onTapped(_ gesture: UITapGestureRecognizer) {
        guard let option = option else { return }
        
        // 添加点击动画
        UIView.animate(withDuration: 0.1, animations: {
            self.containerView.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.containerView.transform = .identity
            }
        }
        
        // 触发回调
        onTapped?(option)
    }
    
    // MARK: - Animation
    func setHighlighted(_ highlighted: Bool, animated: Bool = true) {
        let alpha: CGFloat = highlighted ? 0.7 : 1.0
        let transform = highlighted ? CGAffineTransform(scaleX: 0.98, y: 0.98) : .identity
        
        if animated {
            UIView.animate(withDuration: 0.2) {
                self.containerView.alpha = alpha
                self.containerView.transform = transform
            }
        } else {
            containerView.alpha = alpha
            containerView.transform = transform
        }
    }
}
