//
//  LNRewardTasksViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/09.
//

import UIKit
import SnapKit

/// 奖励任务页面：New Task、Daily Task 两组任务卡片，带 GO!/Finish 按钮与底部 save 按钮
class LNRewardTasksViewController: LNBaseController, UITableViewDataSource, UITableViewDelegate {

    // MARK: - Navigation Bar Style
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }

    // 使用圆弧形渐变背景
    override var useArcGradientBackground: Bool { return true }

    // MARK: - Models
    struct Section { let title: String; var items: [LNTaskItem] }

    private var sections: [Section] = [
        .init(title: "New Task", items: [
            .init(title: "Cumulative Recharge", subtitle: "Task progress: 0/3500", reward: 200, status: .go),
            .init(title: "Subscribe VIP", subtitle: "Subscribe to be VIP membership", reward: 200, status: .go)
        ]),
        .init(title: "Daily Task", items: [
            .init(title: "Cumulative Recharge", subtitle: "Task progress: 0/3500", reward: 200, status: .go),
            .init(title: "Subscribe VIP", subtitle: "Subscribe to be VIP membership", reward: 200, status: .go),
            .init(title: "Cumulative Recharge", subtitle: "Task progress: 0/3500", reward: 200, status: .finish),
            .init(title: "Subscribe VIP", subtitle: "Subscribe to be VIP membership", reward: 200, status: .finish)
        ])
    ]

    // MARK: - UI
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .grouped)
        tv.dataSource = self
        tv.delegate = self
        tv.separatorStyle = .none
        tv.backgroundColor = UIColor.clear
        tv.register(LNTaskCell.self, forCellReuseIdentifier: LNTaskCell.reuseId)
        tv.sectionFooterHeight = 0
        tv.rowHeight = s(80)
        return tv
    }()

    private lazy var saveButton: UIButton = {
        let b = UIButton(type: .system)
        b.setTitle("save", for: .normal)
        b.setTitleColor(.white, for: .normal)
        b.titleLabel?.font = LNFont.forTextStyle(.headline)
        b.layer.cornerRadius = s(26)
        b.layer.masksToBounds = true
        b.addTarget(self, action: #selector(saveTapped), for: .touchUpInside)
        return b
    }()
    private let saveGradient = CAGradientLayer()

    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Reward tasks"
        edgesForExtendedLayout = .all
  
        view.addSubview(tableView)
        view.addSubview(saveButton)

        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(saveButton.snp.top).offset(-s(16))
        }
        saveButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(s(24))
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-s(16))
            make.height.equalTo(s(52))
        }

        // 渐变 - 参考退出登录按钮
        saveGradient.colors = LNGradient.primaryColors
        saveGradient.startPoint = LNGradient.primaryStartPoint
        saveGradient.endPoint = LNGradient.primaryEndPoint
        saveButton.layer.insertSublayer(saveGradient, at: 0)
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        saveGradient.frame = saveButton.bounds
    }

    // MARK: - Table
    func numberOfSections(in tableView: UITableView) -> Int {
        print("numberOfSections: \(sections.count)")
        return sections.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        let count = sections[section].items.count
        print("numberOfRowsInSection \(section): \(count)")
        return count
    }

    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? { sections[section].title }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let title = sections[section].title
        let v = UIView()
        let l = UILabel(); l.text = title; l.font = LNFont.forTextStyle(.headline); l.textColor = UIColor.label
        v.addSubview(l)
        l.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(16))
            make.bottom.equalToSuperview().offset(-s(8))
        }
        return v
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat { s(48) }


    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        print("cellForRowAt: section \(indexPath.section), row \(indexPath.row)")
        let cell = tableView.dequeueReusableCell(withIdentifier: LNTaskCell.reuseId, for: indexPath) as! LNTaskCell
        let item = sections[indexPath.section].items[indexPath.row]
        print("Configuring cell with item: \(item.title)")
        cell.configure(with: item)

        // 配置圆角
        let totalRowsInSection = sections[indexPath.section].items.count
        let isFirst = indexPath.row == 0
        let isLast = indexPath.row == totalRowsInSection - 1
        cell.configureCornerRadius(isFirst: isFirst, isLast: isLast)

        cell.onActionTapped = { [weak self] in
            self?.handleTaskAction(at: indexPath)
        }

        return cell
    }

    // MARK: - Actions
    @objc private func saveTapped() {
        let alert = UIAlertController(title: "Saved", message: "Your settings have been saved.", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func handleTaskAction(at indexPath: IndexPath) {
        let item = sections[indexPath.section].items[indexPath.row]

        switch item.status {
        case .go:
            // 处理 GO! 按钮点击
            handleGoAction(for: item, at: indexPath)
        case .finish:
            // Finish 状态的按钮应该是禁用的，不应该触发点击
            break
        }
    }

    private func handleGoAction(for item: LNTaskItem, at indexPath: IndexPath) {
        let alert = UIAlertController(title: item.title, message: "Starting task: \(item.title)", preferredStyle: .alert)

        // 模拟任务完成
        alert.addAction(UIAlertAction(title: "Complete Task", style: .default) { [weak self] _ in
            self?.completeTask(at: indexPath)
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        present(alert, animated: true)
    }

    private func completeTask(at indexPath: IndexPath) {
        // 更新任务状态为完成
        sections[indexPath.section].items[indexPath.row].status = .finish

        // 刷新对应的单元格
        tableView.reloadRows(at: [indexPath], with: .none)

        // 显示完成提示
        let alert = UIAlertController(title: "Task Completed!", message: "You earned +\(sections[indexPath.section].items[indexPath.row].reward) diamonds!", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }


}

