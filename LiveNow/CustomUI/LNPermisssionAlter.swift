//
//  LNPermisssionAlter.swift
//  LiveNow
//
//  Created by edy on 2025/8/20.
//

import UIKit
import AVFAudio
import AVFoundation

class LNPermisssionAlter: UIView {
    
    var sureBlock:(() -> Void)?
    var cancelBlock: (() -> Void)?
    
    var isShowed: Bool = false

    override init(frame: CGRect) {
        super.init(frame: frame)
        self.frame = UIScreen.main.bounds
        addSubviews()
        addConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI & Layout
    private func addSubviews() {
        addSubview(backgroundMask)
        addSubview(bgView)
        bgView.addSubview(cameraItem)
        bgView.addSubview(microItem)
        bgView.addSubview(titleLbl)
        bgView.addSubview(statusDesLbl)
        bgView.addSubview(gotItBtn)
        bgView.addSubview(cancelBtn)
        
    }
    private func addConstraints() {
        bgView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(s(295))
        }
        titleLbl.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.height.equalTo(s(21))
            make.top.equalTo(s(21))
        }
        statusDesLbl.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(s(255))
            make.top.equalTo(titleLbl.snp.bottom).offset(s(8.5))
        }
        cameraItem.snp.makeConstraints { make in
            make.left.equalTo(s(20))
            make.right.equalTo(s(-20))
            make.top.equalTo(statusDesLbl.snp.bottom).offset(s(13.5))
            make.height.equalTo(s(40))
        }
        microItem.snp.makeConstraints { make in
            make.left.equalTo(s(20))
            make.right.equalTo(s(-20))
            make.top.equalTo(cameraItem.snp.bottom).offset(s(15))
            make.height.equalTo(s(40))
        }
        cancelBtn.snp.makeConstraints { make in
            make.left.equalTo(s(22.5))
            make.top.equalTo(microItem.snp.bottom).offset(s(20))
            make.bottom.equalTo(s(-15))
            make.size.equalTo(CGSize(width: s(115), height: s(45)))
        }
        gotItBtn.snp.makeConstraints { make in
            make.bottom.equalTo(s(-15))
            make.size.equalTo(CGSize(width: s(115), height: s(45)))
            make.right.equalTo(s(-22.5))
        }
    }
    
    func showAlert(animate: Bool = true) {
        if isShowed { return }
        isShowed = true
        guard let window = UIApplication.shared.keyWindow else { return }
        window.addSubview(self)
        isShowCheckedIcon()
        if animate {
            alpha = 0
            UIView.animate(withDuration: 0.25) { [weak self] in
                guard let `self` = self else {return}
                self.alpha = 1
            }
        }
    }
    
    func dismissAlert(animate: Bool = true) {
        isShowed = false
        if animate {
            UIView.animate(withDuration: 0.25) { [weak self] in
                self?.alpha = 0
            } completion: { [weak self] (_) in
                self?.removeFromSuperview()
            }
        } else {
            removeFromSuperview()
        }
    }
    
    // 判断是否开启权限
    func isShowCheckedIcon() {
        let videoStatus = AVCaptureDevice.authorizationStatus(for: .video)
        let microStatus = AVAudioSession.sharedInstance().recordPermission
        if videoStatus == .authorized {
            self.cameraItem.rightBtn.isEnabled = false
        } else {
            self.cameraItem.rightBtn.isEnabled = true
        }
        if microStatus == .granted {
            self.microItem.rightBtn.isEnabled = false
        } else {
            self.microItem.rightBtn.isEnabled = true
        }
        
        if videoStatus == .authorized && microStatus == .granted {
            self.sureBlock?()
            self.dismissAlert()
        }
    }
    
    @objc func submitClick() {
        self.authCamera { isOpen in
            if isOpen {
                if let url = URL(string: UIApplication.openSettingsURLString), UIApplication.shared.canOpenURL(url) {
                    UIApplication.shared.open(url, options: [:], completionHandler: nil)
                }
            }
            self.isShowCheckedIcon()
        }
        self.authMicrophone { isOpen in
            if isOpen {
                if let url = URL(string: UIApplication.openSettingsURLString), UIApplication.shared.canOpenURL(url) {
                    UIApplication.shared.open(url, options: [:], completionHandler: nil)
                }
            }
            self.isShowCheckedIcon()
        }
    }
    
    @objc func backgroundMaskClick() {
        dismissAlert()
        cancelBlock?()
    }
    
    // MARK: - Lazy
    lazy var backgroundMask: UIView = {
        let backgroundMask = UIView(frame: self.bounds)
        backgroundMask.backgroundColor = UIColor(hexString: "#000000", alpha: 0.4)
        return backgroundMask
    }()
    
    lazy var bgView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = s(15)
        view.layer.masksToBounds = false
        return view
    }()
    
    lazy var titleLbl: UILabel = {
        let lbl = UILabel()
        lbl.text = "We need your Permission"
        lbl.textColor = UIColor(hexString: "#2A2A2A")
        lbl.font = LNFont.bold(20)
        return lbl
    }()
    
    lazy var statusDesLbl: UILabel = {
        let lbl = UILabel()
        lbl.text = "Open microphone and camera panel so you can match or video chat"
        lbl.textColor = UIColor(hexString: "#818181", alpha: 0.7)
        lbl.font = LNFont.regular(14)
        lbl.textAlignment = .center
        lbl.numberOfLines = 0
        lbl.lineBreakMode = .byWordWrapping
        return lbl
    }()
    
    lazy var cameraItem: LNPermissionItem = {
        let view = LNPermissionItem()
        view.leftIcon.image = UIImage(named: "match_permission_camera")
        view.titleLbl.text = "Camera"
        view.itemActionBlock = {
            self.authCamera { isOpen in
                if isOpen {
                    if let url = URL(string: UIApplication.openSettingsURLString), UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url, options: [:], completionHandler: nil)
                    }
                }
                self.isShowCheckedIcon()
            }
        }
        return view
    }()
    
    lazy var microItem: LNPermissionItem = {
        let view = LNPermissionItem()
        view.leftIcon.image = UIImage(named: "match_permission_micro")
        view.titleLbl.text = "Microphone"
        view.itemActionBlock = {
            self.authMicrophone { isOpen in
                if isOpen {
                    if let url = URL(string: UIApplication.openSettingsURLString), UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url, options: [:], completionHandler: nil)
                    }
                }
                self.isShowCheckedIcon()
            }
        }
        return view
    }()
    
    lazy var cancelBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("Cancel", for: .normal)
        btn.setTitleColor(UIColor(hexString: "#818181"), for: .normal)
        btn.titleLabel?.font = LNFont.bold(16)
        btn.addTarget(self, action: #selector(backgroundMaskClick), for: .touchUpInside)
        btn.layer.cornerRadius = s(22.5)
        btn.layer.masksToBounds = true
        btn.backgroundColor = UIColor(hexString: "#F4F4F4")
        return btn
    }()
    
    lazy var gotItBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("OK", for: .normal)
        btn.setTitleColor(UIColor(hexString: "#FFFFFF"), for: .normal)
        btn.titleLabel?.font = LNFont.bold(16)
        btn.addTarget(self, action: #selector(submitClick), for: .touchUpInside)
        btn.layer.cornerRadius = s(22.5)
        btn.layer.masksToBounds = true
        btn.layer.insertSublayer(bgLayer2, at: 0)
        return btn
    }()
    
    private lazy var bgLayer2: CAGradientLayer = {
        let bgLayer = CAGradientLayer()
        bgLayer.colors = [UIColor(hexString: "#04E798")?.cgColor as Any, UIColor(hexString: "#0ADCE1")?.cgColor as Any]
        bgLayer.locations = [0, 1]
        bgLayer.frame = CGRect(x: 0, y: 0, width: s(115), height: s(45))
        bgLayer.startPoint = CGPoint(x: 0, y: 0)
        bgLayer.endPoint = CGPoint(x: 1, y: 0)
        return bgLayer
    }()
    
    func authCamera(clouser: @escaping ((Bool) -> Void)) {
        let authStatus = AVCaptureDevice.authorizationStatus(for: .video)
        switch authStatus {
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { (result) in
                if result {
                    DispatchQueue.main.async {
                        clouser(false)
                    }
                } else {
                    DispatchQueue.main.async {
                        clouser(false)
                    }
                }
            }
        case .denied:
            clouser(true)
        case .restricted:
            clouser(true)
        case .authorized:
            clouser(true)
        @unknown default:
            clouser(true)
        }
    }

    /**
     麦克风权限
     
     - parameters: action 权限结果闭包
     */
    func authMicrophone(clouser: @escaping ((Bool) -> Void)) {
        let authStatus = AVAudioSession.sharedInstance().recordPermission
        switch authStatus {
        case .undetermined:
            AVAudioSession.sharedInstance().requestRecordPermission { (result) in
                if result {
                    DispatchQueue.main.async {
                        clouser(false)
                    }
                } else {
                    DispatchQueue.main.async {
                        clouser(false)
                    }
                }
            }
        case .denied:
            clouser(true)
        case .granted:
            clouser(true)
        @unknown default:
            clouser(true)
        }
    }
    
}

class LNPermissionItem: UIView {
    
    var itemActionBlock:(() -> Void)?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.layer.borderWidth = s(0.5)
        self.layer.borderColor = UIColor(hexString: "#000000", alpha: 0.13)?.cgColor
        self.layer.cornerRadius = s(20)
        addSubviews()
        addConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI & Layout
    private func addSubviews() {
        addSubview(leftIcon)
        addSubview(titleLbl)
        addSubview(rightBtn)
    }
    private func addConstraints() {
        leftIcon.snp.makeConstraints { make in
            make.left.equalTo(s(15))
            make.centerY.equalToSuperview()
            make.size.equalTo(s(18))
        }
        titleLbl.snp.makeConstraints { make in
            make.left.equalTo(leftIcon.snp.right).offset(s(10))
            make.centerY.equalToSuperview()
            make.height.equalTo(s(16.5))
        }
        rightBtn.snp.makeConstraints { make in
            make.right.equalTo(s(-12.5))
            make.centerY.equalToSuperview()
            make.size.equalTo(s(15))
        }
    }
    
    // MARK: - Action
    @objc func rightBtnAction() {
        itemActionBlock?()
    }
    
    // MARK: - Lazy
    lazy var leftIcon: UIImageView = {
        let icon = UIImageView()
        return icon
    }()
    
    lazy var titleLbl: UILabel = {
        let lbl = UILabel()
        lbl.textColor = UIColor(hexString: "#2A2A2A", alpha: 0.7)
//        lbl.font = .fontMedium(size: 14)
        return lbl
    }()
    
    lazy var rightBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setBackgroundImage(UIImage(named: "match_permission_unCheck"), for: .normal)
        btn.setBackgroundImage(UIImage(named: "match_permission_checked"), for: .disabled)
        btn.addTarget(self, action: #selector(rightBtnAction), for: .touchUpInside)
        btn.isEnabled = true
        return btn
    }()
}

