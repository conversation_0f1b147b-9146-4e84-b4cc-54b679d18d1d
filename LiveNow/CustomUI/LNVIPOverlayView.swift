//
//  LNVIPOverlayView.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/23.
//

import UIKit
import SnapKit
import JKSwiftExtension
import CoreImage

/// VIP蒙层视图 - 通用组件
/// 功能：
/// - 当用户不是VIP时显示蒙层，遮盖下面的内容
/// - 包含钻石图标、提示文字和升级按钮
/// - 支持自定义提示文字和按钮回调
/// - 可在任何需要VIP权限的页面使用
class LNVIPOverlayView: UIView {
    
    // MARK: - 回调闭包
    
    /// 升级VIP按钮点击回调
    var onUpgradeVIP: (() -> Void)?
    
    // MARK: - 配置属性
    
    /// 主标题文字，默认为 "Check who liked me"
    var titleText: String = "Check who liked me" {
        didSet {
            titleLabel.text = titleText
        }
    }
    
    /// 副标题文字，默认为 "some girls liked you and you can chat with them for free~~"
    var subtitleText: String = "some girls liked you and you can chat with them for free~~" {
        didSet {
            subtitleLabel.text = subtitleText
        }
    }
    
    /// 按钮文字，默认为 "Check who liked me"
    var buttonText: String = "Check who liked me" {
        didSet {
            upgradeButton.setTitle(buttonText, for: .normal)
        }
    }
    
    // MARK: - UI Elements

    // 高斯模糊背景视图 - 精确9pt模糊度
    lazy var gaussianBlurView: UIVisualEffectView = {
        let blurEffect = UIBlurEffect(style: .prominent)
        let view = UIVisualEffectView(effect: blurEffect)
        view.alpha(0.9)
        view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        return view
    }()

    // 钻石图标
    private lazy var diamondImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_bg_zs")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 主标题
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = titleText
        label.font = LNFont.bold(20)
        label.textColor = UIColor(hexString: "#7D7D7D")
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    // 副标题
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = subtitleText
        label.font = LNFont.regular(14)
        label.textColor = UIColor(hexString: "#7D7D7D")
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    // 升级VIP按钮
    private lazy var upgradeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle(buttonText, for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.backgroundColor = UIColor.hex(hexString: "#00D4AA")
        button.layer.cornerRadius = s(25)
        button.layer.masksToBounds = true
        button.addTarget(self, action: #selector(upgradeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = UIColor(hexString: "#747474", alpha: 0.2)

        // 添加子视图 - 按层级顺序添加
        addSubview(gaussianBlurView)      // 高斯模糊背景
        addSubview(diamondImageView)      // 钻石图标
        addSubview(titleLabel)            // 标题
        addSubview(subtitleLabel)         // 副标题
        addSubview(upgradeButton)         // 按钮

        setupConstraints()
    }
    
    private func setupConstraints() {
        // 高斯模糊背景铺满整个视图
        gaussianBlurView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }


        // 钻石图标居中偏上
        diamondImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(s(20))
            make.width.equalTo(s(300))
            make.height.equalTo(s(300))
        }

                // 升级按钮在副标题下方
        upgradeButton.snp.makeConstraints { make in
            make.top.equalTo(diamondImageView.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview().inset(s(30))
            make.height.equalTo(s(50))
        }
        
        // 主标题在钻石图标下方
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(upgradeButton.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview().inset(s(30))
        }
        
        // 副标题在主标题下方
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview().inset(s(30))
        }
        
    }


    
    // MARK: - Event Handlers
    
    @objc private func upgradeButtonTapped() {
        onUpgradeVIP?()
    }
    
    // MARK: - Public Methods
    
    /// 显示蒙层（带动画）
    func show(animated: Bool = true) {
        isHidden = false
        
        if animated {
            alpha = 0
            transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
            
            UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseOut], animations: {
                self.alpha = 1
                self.transform = .identity
            })
        } else {
            alpha = 1
            transform = .identity
        }
    }
    
    /// 隐藏蒙层（带动画）
    func hide(animated: Bool = true, completion: (() -> Void)? = nil) {
        if animated {
            UIView.animate(withDuration: 0.25, delay: 0, options: [.curveEaseIn], animations: {
                self.alpha = 0
                self.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
            }) { _ in
                self.isHidden = true
                completion?()
            }
        } else {
            alpha = 0
            isHidden = true
            completion?()
        }
    }
    
    /// 根据VIP状态自动显示或隐藏蒙层
    /// - Parameter isVIP: 用户是否为VIP
    /// - Parameter animated: 是否使用动画
    func updateVisibility(isVIP: Bool, animated: Bool = true) {
        if isVIP {
            hide(animated: animated)
        } else {
            show(animated: animated)
        }
    }
}

// MARK: - 便捷创建方法

extension LNVIPOverlayView {

    /// 创建默认的"谁喜欢我"蒙层
    static func createWhoLikedMeOverlay() -> LNVIPOverlayView {
        let overlay = LNVIPOverlayView()
        overlay.titleText = "Check who liked me"
        overlay.subtitleText = "some girls liked you and you can chat with them for free~~"
        overlay.buttonText = "Check who liked me"
        return overlay
    }

    /// 创建自定义蒙层
    /// - Parameters:
    ///   - title: 主标题
    ///   - subtitle: 副标题
    ///   - buttonTitle: 按钮文字
    /// - Returns: 配置好的蒙层视图
    static func createCustomOverlay(title: String, subtitle: String, buttonTitle: String) -> LNVIPOverlayView {
        let overlay = LNVIPOverlayView()
        overlay.titleText = title
        overlay.subtitleText = subtitle
        overlay.buttonText = buttonTitle
        return overlay
    }
}
