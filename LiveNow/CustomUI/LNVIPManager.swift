//
//  LNVIPManager.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/23.
//

import UIKit

/// VIP管理器 - 统一处理VIP相关逻辑
/// 功能：
/// - 检查用户VIP状态
/// - 提供VIP权限验证
/// - 统一VIP升级入口
class LNVIPManager {
    
    // MARK: - 单例
    
    static let shared = LNVIPManager()
    private init() {}
    
    // MARK: - VIP状态检查
    
    /// 检查当前用户是否为VIP
    /// - Returns: true表示是VIP，false表示不是VIP
    func isCurrentUserVIP() -> Bool {
        guard let user = LNUserManager.shared.userModel else {
            return false
        }
        
        // 根据LNUserModel的VIP字段判断
        // vipExpireFlag == 1 且 vipExpireDay 不为空表示VIP有效
        return user.vipExpireFlag == 1 && !user.vipExpireDay.isEmpty
    }
    
    /// 获取VIP过期时间
    /// - Returns: VIP过期时间字符串，如果不是VIP则返回nil
    func getVIPExpireDate() -> String? {
        guard let user = LNUserManager.shared.userModel,
              isCurrentUserVIP() else {
            return nil
        }
        
        return user.vipExpireDay
    }
    
    /// 获取VIP状态描述
    /// - Returns: VIP状态描述字符串
    func getVIPStatusDescription() -> String {
        if isCurrentUserVIP() {
            if let expireDate = getVIPExpireDate() {
                return "Active (expires: \(expireDate))"
            } else {
                return "Active"
            }
        } else {
            return "Unactivated"
        }
    }
    
    // MARK: - VIP权限验证
    
    /// 验证VIP权限，如果不是VIP则显示升级提示
    /// - Parameters:
    ///   - in: 要显示蒙层的父视图
    ///   - title: 自定义标题，如果为nil则使用默认标题
    ///   - subtitle: 自定义副标题，如果为nil则使用默认副标题
    ///   - buttonTitle: 自定义按钮标题，如果为nil则使用默认标题
    ///   - onUpgrade: 升级按钮点击回调
    /// - Returns: true表示有VIP权限，false表示没有权限且已显示升级提示
    @discardableResult
    func requireVIPPermission(
        in parentView: UIView,
        title: String? = nil,
        subtitle: String? = nil,
        buttonTitle: String? = nil,
        onUpgrade: (() -> Void)? = nil
    ) -> Bool {
        
        if isCurrentUserVIP() {
            return true
        }
        
        // 不是VIP，显示升级蒙层
        showVIPUpgradeOverlay(
            in: parentView,
            title: title,
            subtitle: subtitle,
            buttonTitle: buttonTitle,
            onUpgrade: onUpgrade
        )
        
        return false
    }
    
    // MARK: - VIP升级相关
    
    /// 显示VIP升级蒙层
    /// - Parameters:
    ///   - parentView: 父视图
    ///   - title: 自定义标题
    ///   - subtitle: 自定义副标题
    ///   - buttonTitle: 自定义按钮标题
    ///   - onUpgrade: 升级按钮点击回调
    private func showVIPUpgradeOverlay(
        in parentView: UIView,
        title: String? = nil,
        subtitle: String? = nil,
        buttonTitle: String? = nil,
        onUpgrade: (() -> Void)? = nil
    ) {
        
        // 移除已存在的蒙层
        removeExistingOverlay(from: parentView)
        
        // 创建新的蒙层
        let overlay: LNVIPOverlayView
        
        if let title = title, let subtitle = subtitle, let buttonTitle = buttonTitle {
            overlay = LNVIPOverlayView.createCustomOverlay(
                title: title,
                subtitle: subtitle,
                buttonTitle: buttonTitle
            )
        } else {
            overlay = LNVIPOverlayView.createWhoLikedMeOverlay()
        }
        
        // 设置升级回调
        overlay.onUpgradeVIP = { [weak self] in
            onUpgrade?()
            self?.showMembershipCenter()
        }
        
        // 添加到父视图，从安全区域顶部开始（导航栏下面）
        parentView.addSubview(overlay)
        overlay.snp.makeConstraints { make in
            if let viewController = parentView.findViewController(),
               viewController.navigationController != nil {
                // 如果有导航栏，从安全区域顶部开始
                make.top.equalTo(parentView.safeAreaLayoutGuide.snp.top)
            } else {
                // 如果没有导航栏，从顶部开始
                make.top.equalToSuperview()
            }
            make.left.right.bottom.equalToSuperview()
        }
        
        // 显示蒙层
        overlay.show(animated: true)
        
        // 设置标识，方便后续移除
        overlay.tag = 9999
    }
    
    /// 移除已存在的VIP蒙层
    /// - Parameter parentView: 父视图
    func removeExistingOverlay(from parentView: UIView) {
        parentView.subviews.forEach { subview in
            if subview.tag == 9999, let overlay = subview as? LNVIPOverlayView {
                overlay.hide(animated: true) {
                    overlay.removeFromSuperview()
                }
            }
        }
    }
    
    /// 显示会员中心页面
    private func showMembershipCenter() {
        // 获取当前最顶层的视图控制器
        guard let topViewController = UIApplication.shared.windows.first?.rootViewController?.topMostViewController() else {
            return
        }
        
        // 跳转到会员中心
        let membershipVC = LNMembershipCenterViewController()
        topViewController.navigationController?.pushViewController(membershipVC, animated: true)
    }
}

// MARK: - UIViewController 扩展

extension UIViewController {

    /// 获取最顶层的视图控制器
    func topMostViewController() -> UIViewController {
        if let presentedViewController = presentedViewController {
            return presentedViewController.topMostViewController()
        }

        if let navigationController = self as? UINavigationController {
            return navigationController.visibleViewController?.topMostViewController() ?? self
        }

        if let tabBarController = self as? UITabBarController {
            return tabBarController.selectedViewController?.topMostViewController() ?? self
        }

        return self
    }
}

// MARK: - UIView 扩展

extension UIView {

    /// 找到视图对应的视图控制器
    func findViewController() -> UIViewController? {
        var responder: UIResponder? = self
        while responder != nil {
            if let viewController = responder as? UIViewController {
                return viewController
            }
            responder = responder?.next
        }
        return nil
    }
}

// MARK: - 便捷使用方法

extension LNVIPManager {
    
    /// 为"谁喜欢我"功能验证VIP权限
    /// - Parameters:
    ///   - parentView: 父视图
    ///   - onUpgrade: 升级回调
    /// - Returns: 是否有权限
    @discardableResult
    func requireVIPForWhoLikedMe(in parentView: UIView, onUpgrade: (() -> Void)? = nil) -> Bool {
        return requireVIPPermission(
            in: parentView,
            title: "Check who liked me",
            subtitle: "some girls liked you and you can chat with them for free~~",
            buttonTitle: "Check who liked me",
            onUpgrade: onUpgrade
        )
    }
    
    /// 为聊天功能验证VIP权限
    /// - Parameters:
    ///   - parentView: 父视图
    ///   - onUpgrade: 升级回调
    /// - Returns: 是否有权限
    @discardableResult
    func requireVIPForChat(in parentView: UIView, onUpgrade: (() -> Void)? = nil) -> Bool {
        return requireVIPPermission(
            in: parentView,
            title: "VIP Chat Access",
            subtitle: "Upgrade to VIP to unlock unlimited chat features",
            buttonTitle: "Upgrade to VIP",
            onUpgrade: onUpgrade
        )
    }
    
    /// 为视频通话功能验证VIP权限
    /// - Parameters:
    ///   - parentView: 父视图
    ///   - onUpgrade: 升级回调
    /// - Returns: 是否有权限
    @discardableResult
    func requireVIPForVideoCall(in parentView: UIView, onUpgrade: (() -> Void)? = nil) -> Bool {
        return requireVIPPermission(
            in: parentView,
            title: "VIP Video Call",
            subtitle: "Upgrade to VIP to enjoy unlimited video calls",
            buttonTitle: "Upgrade to VIP",
            onUpgrade: onUpgrade
        )
    }
}
