//
//  LNGiftSelectionDemo.swift
//  LiveNow
//
//  Created by AI on 2025/8/23.
//

import UIKit
import SnapKit

/// 送礼物弹框演示页面
class LNGiftSelectionDemo: LNBaseController {
    
    // MARK: - UI Components
    
    /// 背景渐变视图
    private lazy var backgroundGradientView: UIView = {
        let view = UIView()
        return view
    }()
    
    /// 主播头像
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = s(50)
        imageView.layer.masksToBounds = true
        imageView.backgroundColor = UIColor.hex(hexString: "#E0E0E0")
        imageView.image = UIImage(named: "default_avatar")
        return imageView
    }()
    
    /// 主播名称
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.text = "Beautiful Girl"
        label.font = LNFont.bold(20)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    /// 在线状态
    private lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.text = "Online"
        label.font = LNFont.medium(14)
        label.textColor = UIColor.hex(hexString: "#00E5A0")
        label.textAlignment = .center
        return label
    }()
    
    /// 送礼物按钮
    private lazy var giftButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("🎁 Send Gift", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.backgroundColor = UIColor.hex(hexString: "#FF69B4")
        button.layer.cornerRadius = s(25)
        button.addTarget(self, action: #selector(giftButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 视频通话按钮
    private lazy var videoCallButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("📹 Video Call", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.backgroundColor = UIColor.hex(hexString: "#00E5A0")
        button.layer.cornerRadius = s(25)
        button.addTarget(self, action: #selector(videoCallButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 钻石余额显示
    private lazy var diamondBalanceView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        view.layer.cornerRadius = s(15)
        
        let diamondIcon = UIImageView()
        diamondIcon.image = UIImage(named: "ic_diamond")
        diamondIcon.contentMode = .scaleAspectFit
        view.addSubview(diamondIcon)
        
        let balanceLabel = UILabel()
        balanceLabel.text = "999"
        balanceLabel.font = LNFont.medium(14)
        balanceLabel.textColor = .white
        view.addSubview(balanceLabel)
        
        diamondIcon.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(10))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(16))
        }
        
        balanceLabel.snp.makeConstraints { make in
            make.left.equalTo(diamondIcon.snp.right).offset(s(5))
            make.right.equalToSuperview().offset(-s(10))
            make.centerY.equalToSuperview()
        }
        
        return view
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupGradientBackground()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateGradientBackground()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        title = "Gift Selection Demo"
        
        // 添加子视图
        view.addSubview(backgroundGradientView)
        view.addSubview(diamondBalanceView)
        view.addSubview(avatarImageView)
        view.addSubview(nameLabel)
        view.addSubview(statusLabel)
        view.addSubview(giftButton)
        view.addSubview(videoCallButton)
        
        // 设置约束
        backgroundGradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        diamondBalanceView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(s(20))
            make.right.equalToSuperview().offset(-s(20))
            make.height.equalTo(s(30))
            make.width.greaterThanOrEqualTo(s(60))
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-s(50))
            make.width.height.equalTo(s(100))
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview().inset(s(20))
        }
        
        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview().inset(s(20))
        }
        
        giftButton.snp.makeConstraints { make in
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-s(80))
            make.left.equalToSuperview().offset(s(20))
            make.width.equalTo((kscreenW - s(60)) / 2)
            make.height.equalTo(s(50))
        }

        videoCallButton.snp.makeConstraints { make in
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-s(80))
            make.right.equalToSuperview().offset(-s(20))
            make.width.equalTo((kscreenW - s(60)) / 2)
            make.height.equalTo(s(50))
        }
    }
    
    private func setupGradientBackground() {
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.hex(hexString: "#FF6B6B").cgColor,
            UIColor.hex(hexString: "#4ECDC4").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        backgroundGradientView.layer.sublayers?.removeAll()
        backgroundGradientView.layer.insertSublayer(gradientLayer, at: 0)
    }
    
    private func updateGradientBackground() {
        if let gradientLayer = backgroundGradientView.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = backgroundGradientView.bounds
        }
    }
    
    // MARK: - Actions
    
    @objc private func giftButtonTapped() {
        showGiftSelection()
    }
    
    @objc private func videoCallButtonTapped() {
        showAlert(title: "Video Call", message: "视频通话功能即将上线")
    }
    
    // MARK: - Gift Selection
    
    private func showGiftSelection() {
        let giftSelectionView = LNGiftSelectionView()
        
        // 设置回调
        giftSelectionView.onSendGift = { [weak self] gift, quantity in
            self?.handleGiftSent(gift: gift, quantity: quantity)
        }
        
        giftSelectionView.onDismiss = {
            print("Gift selection dismissed")
        }
        
        // 显示弹框
        giftSelectionView.show(in: view)
    }
    
    private func handleGiftSent(gift: LNGiftModel, quantity: Int) {
        let totalCost = gift.price * quantity
        let message = "已发送 \(quantity) 个 \(gift.name)，消耗 \(totalCost) 钻石"
        showAlert(title: "礼物发送成功", message: message)
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}
