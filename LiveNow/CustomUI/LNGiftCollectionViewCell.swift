//
//  LNGiftCollectionViewCell.swift
//  LiveNow
//
//  Created by AI on 2025/8/23.
//

import UIKit
import SnapKit

/// 礼物选择Cell
class LNGiftCollectionViewCell: UICollectionViewCell {
    
    static let identifier = "LNGiftCollectionViewCell"
    
    // MARK: - UI Components
    
    /// 礼物图片
    private lazy var giftImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .red
        return imageView
    }()
    
    private lazy var diamondView: UIView = {
        let view = UIView()
        return view
    }()
    
    /// 钻石图标
    private lazy var diamondImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_diamond")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    /// 价格标签
    private lazy var priceLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.medium(12)
        label.textColor = UIColor.hex(hexString: "#00D4AA")
        label.textAlignment = .center
        label.backgroundColor = .clear
        return label
    }()
    
    /// 选中状态边框
    private lazy var selectionBorderView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.layer.borderWidth = s(2)
        view.layer.borderColor = UIColor.hex(hexString: "#00D4AA").cgColor
        view.layer.cornerRadius = s(8)
        view.isHidden = true
        return view
    }()
    
    // MARK: - Properties
    
    /// 当前礼物数据
    private var giftModel: LNGiftModel?
    
    /// 是否选中
    override var isSelected: Bool {
        didSet {
            updateSelectionState()
        }
    }
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor(.lightGray)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = .white
        layer.cornerRadius = s(8)
        layer.masksToBounds = true
        
        // 添加子视图
        contentView.addSubview(selectionBorderView)
        contentView.addSubview(giftImageView)
        contentView.addSubview(diamondView)
        diamondView.addSubview(diamondImageView)
        diamondView.addSubview(priceLabel)
        
        // 设置约束
        selectionBorderView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        giftImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(s(72))
        }
        diamondView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(giftImageView.snp.bottom).offset(s(8))
            make.height.equalTo(s(16))
        }
        priceLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.centerY.equalToSuperview()
            make.height.equalTo(s(16))
        }
        diamondImageView.snp.makeConstraints { make in
            make.leading.equalTo(priceLabel.snp.trailing)
            make.centerY.equalToSuperview()
            make.size.equalTo(s(16))
            make.trailing.equalToSuperview()
        }
    }
    
    // MARK: - Configuration
    
    /// 配置Cell数据
    func configure(with gift: LNGiftModel) {
        self.giftModel = gift
        
        // 设置礼物图片
        giftImageView.ln_setGiftImage(url: gift.imageUrl, placeholder: UIImage(named: "placeholder_gift"))
        
        // 设置价格
        priceLabel.text = "\(gift.price)"
        
    }
    
    /// 更新选中状态
    private func updateSelectionState() {
        selectionBorderView.isHidden = !isSelected
        
        if isSelected {
            // 添加选中动画
            UIView.animate(withDuration: 0.2) {
                self.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
            } completion: { _ in
                UIView.animate(withDuration: 0.2) {
                    self.transform = .identity
                }
            }
        }
    }
    
    // MARK: - Reuse
    
    override func prepareForReuse() {
        super.prepareForReuse()
        giftImageView.image = nil
        priceLabel.text = nil
        selectionBorderView.isHidden = true
        backgroundColor = .white
        transform = .identity
    }
}

// MARK: - UIImageView Extension for Gift Loading

extension UIImageView {
    /// 加载礼物图片的便捷方法
    func ln_setGiftImage(url: String, placeholder: UIImage? = nil) {
        // 如果是网络图片，使用LNImageLoader
        if url.hasPrefix("http") {
            LNImageLoader.loadImage(self, url: url, placeholder: placeholder)
        } else {
            // 本地图片直接加载
            if let image = UIImage(named: url) {
                self.image = image
            } else {
                self.image = placeholder
            }
        }
    }
}
