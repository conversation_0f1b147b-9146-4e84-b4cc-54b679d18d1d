//
//  LNGiftPageLayout.swift
//  LiveNow
//
//  Created by AI on 2025/8/23.
//

import UIKit

/// 礼物分页布局类
/// 专门用于处理2行4列的礼物分页显示
class LNGiftPageLayout: UICollectionViewFlowLayout {
    
    // MARK: - Properties
    
    /// 每页礼物数量 (2行 x 4列)
    private let itemsPerPage = 8
    /// 每行礼物数量
    private let itemsPerRow = 4
    /// 行数
    private let numberOfRows = 2
    
    // MARK: - Initialization
    
    override init() {
        super.init()
        setupLayout()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupLayout()
    }
    
    // MARK: - Setup
    
    private func setupLayout() {
        scrollDirection = .horizontal
        minimumLineSpacing = s(10)
        minimumInteritemSpacing = s(10)
        sectionInset = UIEdgeInsets(top: s(0), left: s(10), bottom: s(0), right: s(10))
    }
    
    // MARK: - Layout Calculation
    
    override func prepare() {
        super.prepare()
        
        guard let collectionView = collectionView else { return }
        
        // 计算item大小
        let containerWidth = collectionView.bounds.width
        let horizontalPadding = sectionInset.left + sectionInset.right
        let horizontalSpacing = minimumInteritemSpacing * CGFloat(itemsPerRow - 1)
        let availableWidth = containerWidth - horizontalPadding - horizontalSpacing
        let itemWidth = availableWidth / CGFloat(itemsPerRow)
        
//        let containerHeight = collectionView.bounds.height
//        let verticalPadding = sectionInset.top + sectionInset.bottom
//        let verticalSpacing = minimumLineSpacing * CGFloat(numberOfRows - 1)
//        let availableHeight = containerHeight - verticalPadding - verticalSpacing
//        let itemHeight = availableHeight / CGFloat(numberOfRows)
        
        itemSize = CGSize(width: itemWidth, height: s(96))
    }
    
    override var collectionViewContentSize: CGSize {
        guard let collectionView = collectionView else { return .zero }
        
        let numberOfItems = collectionView.numberOfItems(inSection: 0)
        let numberOfPages = ceil(Double(numberOfItems) / Double(itemsPerPage))
        let contentWidth = CGFloat(numberOfPages) * collectionView.bounds.width
        
        return CGSize(width: contentWidth, height: collectionView.bounds.height)
    }
    
    override func layoutAttributesForElements(in rect: CGRect) -> [UICollectionViewLayoutAttributes]? {
        guard let collectionView = collectionView else { return nil }
        
        var attributes: [UICollectionViewLayoutAttributes] = []
        let numberOfItems = collectionView.numberOfItems(inSection: 0)
        
        for i in 0..<numberOfItems {
            let indexPath = IndexPath(item: i, section: 0)
            if let itemAttributes = layoutAttributesForItem(at: indexPath) {
                if rect.intersects(itemAttributes.frame) {
                    attributes.append(itemAttributes)
                }
            }
        }
        
        return attributes
    }
    
    override func layoutAttributesForItem(at indexPath: IndexPath) -> UICollectionViewLayoutAttributes? {
        guard let collectionView = collectionView else { return nil }
        
        let attributes = UICollectionViewLayoutAttributes(forCellWith: indexPath)
        
        // 计算当前item在第几页
        let pageIndex = indexPath.item / itemsPerPage
        // 计算当前item在页面中的位置
        let itemInPage = indexPath.item % itemsPerPage
        // 计算当前item在页面中的行和列
        let row = itemInPage / itemsPerRow
        let column = itemInPage % itemsPerRow
        
        // 计算item的位置
        let pageWidth = collectionView.bounds.width
        let pageOffsetX = CGFloat(pageIndex) * pageWidth
        
        let itemWidth = itemSize.width
        let itemHeight = itemSize.height
        
        let x = pageOffsetX + sectionInset.left + CGFloat(column) * (itemWidth + minimumInteritemSpacing)
        let y = sectionInset.top + CGFloat(row) * (itemHeight + minimumLineSpacing)
        
        attributes.frame = CGRect(x: x, y: y, width: itemWidth, height: itemHeight)
        
        return attributes
    }
    
    override func shouldInvalidateLayout(forBoundsChange newBounds: CGRect) -> Bool {
        return true
    }
    
    // MARK: - Paging Support
    
    override func targetContentOffset(forProposedContentOffset proposedContentOffset: CGPoint, withScrollingVelocity velocity: CGPoint) -> CGPoint {
        guard let collectionView = collectionView else { return proposedContentOffset }
        
        let pageWidth = collectionView.bounds.width
        let currentPage = round(proposedContentOffset.x / pageWidth)
        let targetX = currentPage * pageWidth
        
        return CGPoint(x: targetX, y: proposedContentOffset.y)
    }
}
