//
//  LNRechargeTest.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/31.
//

import UIKit

/// 充值功能测试类
class LNRechargeTest {
    
    /// 测试充值数据模型
    static func testRechargeModel() {
        print("=== 测试充值数据模型 ===")
        
        // 创建测试数据
        let testData: [String: Any] = [
            "id": "1001",
            "num": 199,
            "giveNum": 100,
            "currency": "USD",
            "price": "4.99",
            "priceId": "price_001",
            "localizedPrice": "US$4.99",
            "productId": "com.livehouse.diamonds.199",
            "itemName": "Diamond Pack",
            "amount": "4.99",
            "diamondNum": 299,
            "message": "Special offer",
            "priceType": "1",
            "discountType": "2",
            "discountMessage": "30%Off",
            "imageUrl": "https://example.com/diamond.png",
            "payMethodId": 1,
            "times": 1,
            "countdown": "24:00:00"
        ]
        
        // 测试数据解析
        if let recharge = LNRecharge.deserialize(from: testData) {
            print("✅ 数据解析成功")
            print("ID: \(recharge.id)")
            print("基础钻石: \(recharge.baseDiamonds)")
            print("奖励钻石: \(recharge.bonusDiamonds)")
            print("总钻石: \(recharge.totalDiamonds)")
            print("显示价格: \(recharge.displayPrice)")
            print("是否有折扣: \(recharge.hasDiscount)")
            print("折扣标签: \(recharge.discountLabel ?? "无")")
        } else {
            print("❌ 数据解析失败")
        }
        
        // 测试兼容性模型转换
        if let recharge = LNRecharge.deserialize(from: testData) {
            let option = LNRechargeOption(from: recharge)
            print("✅ 兼容性模型转换成功")
            print("钻石数量: \(option.diamonds)")
            print("奖励钻石: \(option.bonusDiamonds)")
            print("总钻石: \(option.totalDiamonds)")
            print("价格: \(option.priceUSD)")
            print("折扣: \(option.discountLabel ?? "无")")
        }
    }
    
    /// 测试充值请求参数
    static func testRechargeRequest() {
        print("\n=== 测试充值请求参数 ===")
        
        let request = LNRechargeRequest(priceType: "1")
        let parameters = request.toParameters()
        
        print("✅ 请求参数生成成功")
        print("参数: \(parameters)")
    }
    
    /// 测试充值管理器（模拟）
    static func testRechargeManager() {
        print("\n=== 测试充值管理器 ===")
        
        // 注意：这里只是测试方法调用，实际网络请求需要在真实环境中测试
        print("充值管理器初始化成功")
        print("可以调用 fetchRechargeList 和 createOrder 方法")
    }
    
    /// 运行所有测试
    static func runAllTests() {
        print("🚀 开始运行充值功能测试\n")
        
        testRechargeModel()
        testRechargeRequest()
        testRechargeManager()
        
        print("\n✅ 所有测试完成")
    }
}

/// 充值页面演示类
class LNRechargeDemo {
    
    /// 演示如何使用充值页面
    static func showRechargeViewController(from parentVC: UIViewController) {
        let rechargeVC = LNRechargeViewController()
        parentVC.navigationController?.pushViewController(rechargeVC, animated: true)
    }
    
    /// 演示如何创建测试数据
    static func createTestRechargeData() -> [LNRecharge] {
        let testDataArray: [[String: Any]] = [
            [
                "id": "1001",
                "num": 199,
                "giveNum": 100,
                "price": "4.99",
                "localizedPrice": "US$4.99",
                "productId": "com.livehouse.diamonds.199",
                "itemName": "Starter Pack",
                "discountType": "0",
                "discountMessage": ""
            ],
            [
                "id": "1002",
                "num": 199,
                "giveNum": 100,
                "price": "4.99",
                "localizedPrice": "US$4.99",
                "productId": "com.livehouse.diamonds.199.discount",
                "itemName": "Special Pack",
                "discountType": "2",
                "discountMessage": "30%Off"
            ],
            [
                "id": "1003",
                "num": 499,
                "giveNum": 200,
                "price": "9.99",
                "localizedPrice": "US$9.99",
                "productId": "com.livehouse.diamonds.499",
                "itemName": "Popular Pack",
                "discountType": "2",
                "discountMessage": "50%Off"
            ]
        ]
        
        var rechargeList: [LNRecharge] = []
        for data in testDataArray {
            if let recharge = LNRecharge.deserialize(from: data) {
                rechargeList.append(recharge)
            }
        }
        
        return rechargeList
    }
}
