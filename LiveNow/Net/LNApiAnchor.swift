import UIKit
import Moya


public enum LNApiAnchor {
    // MARK: - 主播列表 Home
    /// 推荐主播列表
    case recommendList(par: [String: Any])
    /// 热门主播列表
    case popularList(par: [String: Any])
    /// 新主播列表
    case newList(par: [String: Any])
    /// 关注主播列表
    case followList(par: [String: Any])

    // MARK: - 主播详情
    /// 主播详情
    case anchorDetail(par: [String: Any])
    /// 主播简要详情
    case anchorLitDetail(par: [String: Any])
    /// 随机主播
    case randomAnchor(par: [String: Any])

    // MARK: - 视频通话
    /// 生成视频频道
    case videoChannel(par: [String: Any])
    /// 获取RTC Token
    case rtcToken(par: [String: Any])
    /// 挂断视频
    case videoHangup(par: [String: Any])
    /// 取消视频推送
    case videoPushCancel(par: [String: Any])
    /// 视频扣费
    case videoDeduct(par: [String: Any])
    /// 视频推送
    case videoPush(par: [String: Any])
    /// 来电主播
    case incomingAnchor(par: [String: Any])
}

extension LNApiAnchor: Moya.TargetType {
    public var baseURL: URL {
        return URL(string: apiURL + "/ks-mikchat/")!
    }

    public var path: String {
        var baseURL = ""
        var p: [String: Any]?
        switch self {
        case .recommendList:
            baseURL = "anchor/recommend"
        case .popularList:
            baseURL = "anchor/popular"
        case .newList:
            baseURL = "anchor/new"
        case .followList:
            baseURL = "anchor/follow"
        case .anchorDetail(let par):
            baseURL = "anchor/detail"
            p = par
        case .anchorLitDetail(let par):
            baseURL = "anchor/litDetail"
            p = par
        case .randomAnchor:
            baseURL = "video/random/anchor"
        case .videoChannel:
            baseURL = "video/channel"
        case .rtcToken:
            baseURL = "video/rtc/token"
        case .videoHangup:
            baseURL = "video/hangup"
        case .videoPushCancel:
            baseURL = "video/push/cancel"
        case .videoDeduct:
            baseURL = "video/deduct"
        case .videoPush:
            baseURL = "video/push"
        case .incomingAnchor:
            baseURL = "video/incoming/anchor"
        }

        if let parDict = p {
            var namedPaird = [String]()
            for(key, value) in parDict {
                if let valueStr = value as? String, let utf8Str = valueStr.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
                    // 处理url上包含中文时，进行编码
                    namedPaird.append("\(key)=\(utf8Str)")
                } else {
                    namedPaird.append("\(key)=\(value)")
                }
            }
            let signedString = namedPaird.joined(separator:"&")
            return baseURL + "?" + signedString
        } else {
            return baseURL
        }
    }

    public var method: Moya.Method {
        switch self {
        case .recommendList,
                .popularList,
                .newList,
                .followList,
                .randomAnchor,
                .videoChannel,
                .videoHangup,
                .rtcToken,
                .videoPushCancel,
                .videoDeduct,
                .incomingAnchor,
                .videoPush:
            return .post
        default:
            return .get
        }
    }

    /// 这个是做单元测试模拟的数据，必须要实现，只在单元测试文件中有作用
    public var sampleData: Data {
        return "".data(using: String.Encoding.utf8)!
    }

    public var task: Task {
        var params: [String: Any]? = nil

        switch self {
        case  .recommendList(let par), 
                .popularList(let par), 
                .newList(let par), 
                .followList(let par),
                .randomAnchor(let par),
                .videoChannel(let par),
                .videoHangup(let par),
                .rtcToken(let par),
                .videoPushCancel(let par),
                .videoDeduct(let par),
                .incomingAnchor(let par),
                .videoPush(let par):
            params = par
        default:
            return .requestPlain
        }
        
        if let params = params {
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        }
        return .requestPlain
    }

    public var headers: [String: String]? {
        return LNNetApiTool.networkHeaders()
    }
}
