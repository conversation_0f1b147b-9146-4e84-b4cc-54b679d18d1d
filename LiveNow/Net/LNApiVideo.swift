//
//  LNApiVideo.swift
//  LiveNow
//
//  Created by edy on 2025/8/22.
//

import Moya

public enum LNApiVideo {
    /// 随机主播
    case randomAnchor(par: [String: Any])
    /// 获取渠道
    case genVideoChannel(par: [String: Any])
    /// 获取toekn
    case getVideoRtcToken(par: [String: Any])
    /// 挂断
    case postVideoHangUp(par: [String: Any])
    /// 取消
    case postVideoCancel(par: [String: Any])
    /// 扣费
    case postVideoDeduct(par: [String: Any])
    /// 加入频道推送消息给主播
    case postVideoPush(par: [String: Any])
    /// 获取主动来电主播
    case incomingAnchor(par: [String: Any])
}

extension LNApiVideo: Moya.TargetType {
    public var baseURL: URL {
        return URL(string: apiURL + "/ks-mikchat/video/")!
    }
    
    public var path: String {
        var baseURL = ""
        var p: [String: Any]?
        switch self {
        case .randomAnchor:
            baseURL = "random/anchor"
        case .genVideoChannel:
            baseURL = "channel"
        case .getVideoRtcToken:
            baseURL = "rtc/token"
        case .postVideoHangUp:
            baseURL = "hangup"
        case .postVideoCancel:
            baseURL = "push/cancel"
        case .postVideoDeduct:
            baseURL = "deduct"
        case .postVideoPush:
            baseURL = "push"
        case .incomingAnchor:
            baseURL = "incoming/anchor"
            
        }
        
        if let parDict = p {
            var namedPaird = [String]()
            for(key, value) in parDict {
                if let valueStr = value as? String, let utf8Str = valueStr.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
                    // 处理url上包含中文时，进行编码
                    namedPaird.append("\(key)=\(utf8Str)")
                } else {
                    namedPaird.append("\(key)=\(value)")
                }
            }
            let signedString = namedPaird.joined(separator:"&")
            return baseURL + "?" + signedString
        } else {
            return baseURL
        }
    }
        
    public var method: Moya.Method {
        return .post
    }
    
    /// 这个是做单元测试模拟的数据，必须要实现，只在单元测试文件中有作用
    public var sampleData: Data {
        return "".data(using: String.Encoding.utf8)!
    }
    
    public var task: Task {
        var params: [String: Any]? = nil
        
        switch self {
        case .postVideoHangUp(let par),
             .randomAnchor(let par):
            params = par
        default:
            return .requestPlain
        }
        if let params = params {
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        }
        return .requestPlain
    }
    
    public var headers: [String: String]? {
        
        
        return LNNetApiTool.networkHeaders()
    }
}

