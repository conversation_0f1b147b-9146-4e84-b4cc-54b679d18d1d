//
//  NetApiTool.swift
//  LiveNow
//
//  Created by edy on 2025/8/12.
//

import UIKit
import JKSwiftExtension

class LNNetApiTool: NSObject {
    // 请求头
    class func networkHeaders() -> [String: String] {
        let token = LNUserManager.shared.getToken()
        
        let header: [String: String] = [
            "Content-Type": "application/json; charset=UTF-8;",
            "Connection": "keep-alive",
            "X-pkg": "com.livehouse.video.ios",
            "X-model": "\(UIDevice.jk.deviceType)",
            "X-deviceType": "iOS",
            "Blade-Auth": token
        ]
        return header
    }
}
