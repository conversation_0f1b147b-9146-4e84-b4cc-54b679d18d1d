//
//  LNNetManager.swift
//  LiveNow
//
//  Created by edy on 2025/8/12.
//

import UIKit
import Moya
import Alamofire


let apiURL = "http://8.219.212.130:8000"

let RESULT_CODE = "code"
let RESULT_MESSAGE = "msg"
let ERROR_RESULT = "errorResult"

/// endpointClosure
private let myEndpointClosure = { (target: Moya.TargetType) -> Endpoint in
    /// 这里的endpointClosure和网上其他实现有些不太一样。
    /// 主要是为了解决URL带有？无法请求正确的链接地址的bug
    let url = target.baseURL.absoluteString + target.path
    var endpoint = Endpoint(
        url: url,
        sampleResponseClosure: { .networkResponse(200, target.sampleData) },
        method: target.method,
        task: target.task,
        httpHeaderFields: target.headers
    )
    
    switch target {
    default:
        return endpoint
    }
}

private let requestClosure = { (endpoint: Endpoint, done: MoyaProvider.RequestResultClosure) in
    do {
        var request = try endpoint.urlRequest()
        // 设置请求时长
        request.timeoutInterval = 30
        // 打印请求参数
        if let requestData = request.httpBody {
            let bodyStr = String(data: requestData, encoding: .utf8) ?? ""
            Log("接口:\(request.url!)--\(String(describing: request.httpMethod))--发送参数:\(bodyStr)")
        } else {
            Log("接口:\(request.url!)--\(String(describing: request.httpMethod))")
        }
        done(.success(request))
    } catch {
        done(.failure(MoyaError.underlying(error, nil)))
    }
}

private let uploadRequestClosure = { (endpoint: Endpoint, done: MoyaProvider.RequestResultClosure) in
    do {
        var request = try endpoint.urlRequest()
        // 设置请求时长
        request.timeoutInterval = 180
        done(.success(request))
    } catch {
        done(.failure(MoyaError.underlying(error, nil)))
    }
}

private let translateClosure = { (endpoint: Endpoint, done: MoyaProvider.RequestResultClosure) in
        do {
            var request = try endpoint.urlRequest()
            // 设置请求时长
            request.timeoutInterval = 3
            // 打印请求参数
            if let requestData = request.httpBody {
                let bodyStr = String(data: requestData, encoding: .utf8) ?? ""
                Log("接口:\(request.url!)--\(String(describing: request.httpMethod))--发送参数:\(bodyStr)")
            } else {
                Log("接口:\(request.url!)--\(String(describing: request.httpMethod))")
            }
            done(.success(request))
        } catch {
            done(.failure(MoyaError.underlying(error, nil)))
        }
}

/// NetworkActivityPlugin插件用来监听网络请求
private let networkPlugin = NetworkActivityPlugin.init { (changeType, target) in
    
    // targetType 是当前请求的基本信息
    switch changeType {
    case .began:
        Log("接口请求开始: \(target.baseURL)\(target.path)")
        break
    case .ended:
        Log("接口请求结束: \(target.baseURL)\(target.path)")
        break
    }
}

fileprivate let Provider = MoyaProvider<MultiTarget>(endpointClosure: myEndpointClosure, requestClosure: requestClosure, plugins: [networkPlugin], trackInflights: false)

/// 先添加一个闭包用于成功时后台返回数据的回调
typealias successCallback = (([String: Any]) -> Void)
typealias failureCallback = ((NSError) -> Void)
typealias progressCallback = ((Double) -> Void)?

/// 再次用一个方法封装provider.request()
func NetWorkRequest(_ target: Moya.TargetType, completion: @escaping successCallback, failure: @escaping failureCallback, progressCallback: progressCallback = nil) {
    
    Provider.request(MultiTarget(target)) { result in
        switch result {
        case let .success(response):
            if response.statusCode == 200 {
                do {
                    let responseDic = try JSONSerialization.jsonObject(with: response.data, options: JSONSerialization.ReadingOptions.mutableContainers)
                    
                    guard let result = responseDic as? [String: Any] else { return }
                    print("接口:\(target.path)--返回值为:\(result)")
                    guard let code = result[RESULT_CODE] as? Int else { return }
                    if code == 200 {
                        completion(result)
                    } else {
                        if code == -3 {
                            // access_token无效,需要刷新token或者重新登录
                            Log("接口请求失败-- 路径：\(target.path),响应码：\(code)--错误信息：access_token无效,需要刷新token或者重新登录")
//                            AppDelegate.shared.window?.makeToast("登录失效，请重新登录".localized, duration: 3)
//                            UCUserManager.shared.logOut {
//                                DispatchQueue.main.asyncAfter(deadline: .now()+1, execute: {
//                                    AppDelegate.shared.resetRootViewController()
//                                })
//                            }
                        } else if code == -4 {
                            // refresh_token无效,需要重新登录
//                            printLog("接口请求失败-- 路径：\(path),响应码：\(code)--错误信息：refresh_token无效,需要重新登录")
//                            AppDelegate.shared.window?.makeToast("登录失效，请重新登录".localized, duration: 3)
//                            UCUserManager.shared.logOut {
//                                DispatchQueue.main.asyncAfter(deadline: .now()+1, execute: {
//                                    AppDelegate.shared.resetRootViewController()
//                                })
//                            }
                        } else {
                            // code 不为0 HUD显示错误信息
                            let msg = result[RESULT_MESSAGE] as? String
//                            // 这里将result塞到error的userInfo里，可以方便在单个网络请求里处理业务逻辑，避免都在网络库中处理
                            let error = NSError(domain: "REQUEST_STATUS_CODE_ERROR", code: code, userInfo: [NSLocalizedDescriptionKey: msg ?? "", ERROR_RESULT: result])
//                            failure(error)
//                            // 网络请求错误码通用处理
//                            UCCommonTool.networkErrorCommonHandle(code: code, result: result)
                            Log("接口请求失败--路径：\(target.path),响应码：\(code)--错误信息：\(error.localizedDescription)")
                        }
                    }
                } catch {
                    // error异常的对象
                    assert(false, error.localizedDescription)
                }
                
            } else {
//                var errorStr = "网络连接失败".localized
//                if let manager = NetworkReachabilityManager.default, !manager.isReachable {
//                    errorStr = "无网络连接，请检查网络".localized
//                }
//                let error = NSError(domain: "REQUEST_STATUS_CODE_ERROR", code: response.statusCode, userInfo: [NSLocalizedDescriptionKey: errorStr])
//                AppDelegate.shared.window?.makeToast(error.userInfo[NSLocalizedDescriptionKey] as? String)
//                failure(error)
//                
                Log("接口请求失败--路径：\(target.path),响应码：\(response.statusCode)--错误信息")
            }
        case let .failure(error):
            break
            // 网络连接失败，提示用户
//            var errorStr = "网络连接失败".localized
//            if let manager = NetworkReachabilityManager.default, !manager.isReachable {
//                errorStr = "无网络连接，请检查网络".localized
//            }
//            let errorMsg = NSError(domain: "REQUEST_CONNECT_ERROR", code: error.errorCode, userInfo: [NSLocalizedDescriptionKey: errorStr])
//            AppDelegate.shared.window?.makeToast(errorMsg.userInfo[NSLocalizedDescriptionKey] as? String)
//            failure(errorMsg)
//            
            Log("接口请求失败--路径：\(target.path),响应码：\(error.errorCode)--错误信息：\(error.localizedDescription)")
        }
    }
}
