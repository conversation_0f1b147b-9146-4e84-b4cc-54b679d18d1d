//
//  LNImageUploadManager.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/25.
//

import UIKit
import Moya

/// 图片上传管理器
class LNImageUploadManager {
    
    /// 单例
    static let shared = LNImageUploadManager()
    
    private init() {}
    
    /// 上传进度回调
    typealias ProgressCallback = (Double) -> Void
    
    /// 上传成功回调
    typealias SuccessCallback = (String) -> Void
    
    /// 上传失败回调
    typealias FailureCallback = (Error) -> Void
    
    // MARK: - 公开方法
    
    /// 上传图片
    /// - Parameters:
    ///   - image: 要上传的图片
    ///   - compressionQuality: 压缩质量 (0.0-1.0)，默认0.7
    ///   - progress: 上传进度回调
    ///   - success: 上传成功回调，返回图片URL
    ///   - failure: 上传失败回调
    func uploadImage(
        _ image: UIImage,
        compressionQuality: CGFloat = 0.7,
        progress: ProgressCallback? = nil,
        success: @escaping SuccessCallback,
        failure: @escaping FailureCallback
    ) {
        // 压缩图片
        guard let imageData = compressImage(image, quality: compressionQuality) else {
            failure(LNImageUploadError.imageCompressionFailed)
            return
        }
        
        // 生成文件名
        let fileName = generateFileName()
        
        // 构建上传参数
        let parameters: [String: Any] = [
            "imageData": imageData,
            "fileName": fileName
        ]
        
        Log("开始上传图片，文件名: \(fileName)，大小: \(imageData.count) bytes")
        
        // 执行上传
        NetWorkRequest(
            LNApiProfile.fileUpload(par: parameters),
            completion: { result in
                DispatchQueue.main.async {
                    Log("图片上传成功: \(result)")
                    
                    // 解析返回的图片URL
                    if let data = result["data"] as? String {
                        success(data)
                    } else if let code = result["code"] as? Int, code == 200,
                              let data = result["data"] as? String {
                        success(data)
                    } else {
                        let errorMsg = result["msg"] as? String ?? "上传失败"
                        failure(LNImageUploadError.serverError(errorMsg))
                    }
                }
            },
            failure: { error in
                DispatchQueue.main.async {
                    Log("图片上传失败: \(error.localizedDescription)")
                    failure(error)
                }
            },
            progressCallback: { progressValue in
                DispatchQueue.main.async {
                    progress?(progressValue)
                }
            }
        )
    }
    
    // MARK: - 私有方法
    
    /// 压缩图片
    /// - Parameters:
    ///   - image: 原始图片
    ///   - quality: 压缩质量
    /// - Returns: 压缩后的图片数据
    private func compressImage(_ image: UIImage, quality: CGFloat) -> Data? {
        // 先调整图片尺寸（如果太大）
        let resizedImage = resizeImageIfNeeded(image)
        
        // 压缩为JPEG格式
        return resizedImage.jpegData(compressionQuality: quality)
    }
    
    /// 如果图片太大则调整尺寸
    /// - Parameter image: 原始图片
    /// - Returns: 调整后的图片
    private func resizeImageIfNeeded(_ image: UIImage) -> UIImage {
        let maxSize: CGFloat = 1024 // 最大边长
        let size = image.size
        
        // 如果图片尺寸在限制内，直接返回
        if size.width <= maxSize && size.height <= maxSize {
            return image
        }
        
        // 计算新尺寸
        let ratio = min(maxSize / size.width, maxSize / size.height)
        let newSize = CGSize(width: size.width * ratio, height: size.height * ratio)
        
        // 重绘图片
        UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
        image.draw(in: CGRect(origin: .zero, size: newSize))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return resizedImage ?? image
    }
    
    /// 生成文件名
    /// - Returns: 唯一的文件名
    private func generateFileName() -> String {
        let timestamp = Int(Date().timeIntervalSince1970)
        let random = Int.random(in: 1000...9999)
        return "avatar_\(timestamp)_\(random).jpg"
    }
}

// MARK: - 错误定义

/// 图片上传错误
enum LNImageUploadError: LocalizedError {
    case imageCompressionFailed
    case serverError(String)
    
    var errorDescription: String? {
        switch self {
        case .imageCompressionFailed:
            return "图片压缩失败"
        case .serverError(let message):
            return "服务器错误: \(message)"
        }
    }
}

// MARK: - 便捷扩展

extension LNImageUploadManager {
    
    /// 上传头像图片（便捷方法）
    /// - Parameters:
    ///   - image: 头像图片
    ///   - success: 成功回调
    ///   - failure: 失败回调
    func uploadAvatar(
        _ image: UIImage,
        success: @escaping SuccessCallback,
        failure: @escaping FailureCallback
    ) {
        uploadImage(
            image,
            compressionQuality: 0.8, // 头像使用较高质量
            progress: nil,
            success: success,
            failure: failure
        )
    }
    
    /// 上传头像图片（带进度）
    /// - Parameters:
    ///   - image: 头像图片
    ///   - progress: 进度回调
    ///   - success: 成功回调
    ///   - failure: 失败回调
    func uploadAvatarWithProgress(
        _ image: UIImage,
        progress: @escaping ProgressCallback,
        success: @escaping SuccessCallback,
        failure: @escaping FailureCallback
    ) {
        uploadImage(
            image,
            compressionQuality: 0.8,
            progress: progress,
            success: success,
            failure: failure
        )
    }
}
