//
//  NetApiCommon.swift
//  LiveNow
//
//  Created by edy on 2025/8/12.
//

import UIKit
import Moya

public enum LNApiCommon {
    /// 配置
    case systemSettings(par: [String: Any])
    /// 发送验证码
    case phoneverifycodeSend(par: [String: Any])
    /// 用户登录
    case authToken(par: [String: Any])
}

extension LNApiCommon: Moya.TargetType {
    public var baseURL: URL {
        return URL(string: apiURL + "/blade-auth/auth/")!
    }
    
    public var path: String {
        var baseURL = ""
        var p: [String: Any]?
        switch self {
        case .systemSettings(let par):
            baseURL = "sys/param"
            p = par
        case .phoneverifycodeSend:
            baseURL = "v1/phoneverifycode/send"
        case .authToken:
            baseURL = "token"
            
        }
        
        if let parDict = p {
            var namedPaird = [String]()
            for(key, value) in parDict {
                if let valueStr = value as? String, let utf8Str = valueStr.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
                    // 处理url上包含中文时，进行编码
                    namedPaird.append("\(key)=\(utf8Str)")
                } else {
                    namedPaird.append("\(key)=\(value)")
                }
            }
            let signedString = namedPaird.joined(separator:"&")
            return baseURL + "?" + signedString
        } else {
            return baseURL
        }
    }
        
    public var method: Moya.Method {
        switch self {
        case .systemSettings:
            return .get
        case
             .authToken,
             .phoneverifycodeSend:
            return .post
        }
    }
    
    /// 这个是做单元测试模拟的数据，必须要实现，只在单元测试文件中有作用
    public var sampleData: Data {
        return "".data(using: String.Encoding.utf8)!
    }
    
    public var task: Task {
        var params: [String: Any]? = nil
        
        switch self {
        case .phoneverifycodeSend(let par),
             .authToken(let par):
            params = par
        default:
            return .requestPlain
        }
        if let params = params {
            
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        }
        return .requestPlain
    }
    
    public var headers: [String: String]? {
        
        
        return LNNetApiTool.networkHeaders()
    }
}
