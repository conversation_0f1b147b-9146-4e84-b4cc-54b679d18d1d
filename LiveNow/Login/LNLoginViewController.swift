//
//  LNLoginViewController.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/12.
//

import UIKit
import SnapKit
import JKSwiftExtension

/// 登录页面控制器
class LNLoginViewController: LNBaseController {
    
    // MARK: - UI Elements
    
    /// 背景渐变视图
    private let backgroundGradientView: UIView = {
        let view = UIView()
        return view
    }()
    
    /// 背景图案视图
    private let backgroundPatternView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.image = UIImage(named: "ic_login_top_bg")
        return imageView
    }()
    
    /// 应用图标
    private let appIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.layer.cornerRadius = 20
        imageView.clipsToBounds = true
        // 设置默认图标
        imageView.backgroundColor = UIColor.hex(hexString: "00E5A0")
        return imageView
    }()
    
    /// 按钮容器
    private let buttonsContainer: UIView = {
        let view = UIView()
        return view
    }()
    
    /// Apple登录按钮
    private let appleSignInButton: UIButton = {
        let button = UIButton(type: .system)
        button.backgroundColor = UIColor.white
        button.layer.cornerRadius = 25
        button.layer.shadowColor = UIColor.black.cgColor
        button.layer.shadowOpacity = 0.1
        button.layer.shadowOffset = CGSize(width: 0, height: 2)
        button.layer.shadowRadius = 4
        
        // 设置标题和图标
        button.setTitle("Sign in with Apple", for: .normal)
        button.setTitleColor(UIColor.black, for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        
        // 设置Apple图标
        let appleIcon = UIImage(systemName: "applelogo")
        button.setImage(appleIcon, for: .normal)
        button.tintColor = UIColor.black
        button.imageEdgeInsets = UIEdgeInsets(top: 0, left: -8, bottom: 0, right: 8)
        
        return button
    }()
    /// apple图标
    private let appleIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_login_apple")
        return imageView
    }()
    
    /// Google登录按钮
    private let googleSignInButton: UIButton = {
        let button = UIButton(type: .system)
        button.backgroundColor = UIColor.white
        button.layer.cornerRadius = 25
        button.layer.shadowColor = UIColor.black.cgColor
        button.layer.shadowOpacity = 0.1
        button.layer.shadowOffset = CGSize(width: 0, height: 2)
        button.layer.shadowRadius = 4
        
        // 设置标题
        button.setTitle("Connect with Google", for: .normal)
        button.setTitleColor(UIColor.black, for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        
        return button
    }()
    
    
    /// Google图标
    private let googleIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.image = UIImage(named: "ic_login_google")
        return imageView
    }()
    
    /// 手机号登录按钮
    private let phoneSignInButton: UIButton = {
        let button = UIButton(type: .system)
        button.backgroundColor = UIColor.white
        button.layer.cornerRadius = 25
        button.layer.shadowColor = UIColor.black.cgColor
        button.layer.shadowOpacity = 0.1
        button.layer.shadowOffset = CGSize(width: 0, height: 2)
        button.layer.shadowRadius = 4
        
        // 设置标题
        button.setTitle("Login with phone number", for: .normal)
        button.setTitleColor(UIColor.black, for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        
        return button
    }()
    
    /// 手机图标
    private let phoneIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_login_phone")
        return imageView
    }()
    
    /// 协议文本
    private let agreementLabel: UILabel = {
        let label = UILabel()
        label.numberOfLines = 0
        label.textAlignment = .center
        label.font = LNFont.regular(12)
        label.textColor = UIColor.white.withAlphaComponent(0.8)
        
        let text = "By Registering, You Agree To The User Agreement And Privacy Policy"
        let attributedString = NSMutableAttributedString(string: text)
        
        // 设置"User Agreement"为可点击
        let userAgreementRange = (text as NSString).range(of: "User Agreement")
        attributedString.addAttribute(.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: userAgreementRange)
        
        // 设置"Privacy Policy"为可点击
        let privacyPolicyRange = (text as NSString).range(of: "Privacy Policy")
        attributedString.addAttribute(.underlineStyle, value: NSUnderlineStyle.single.rawValue, range: privacyPolicyRange)
        
        label.attributedText = attributedString
        return label
    }()
    
    /// 底部指示器
    private let bottomIndicator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.5)
        view.layer.cornerRadius = 2
        return view
    }()

    #if DEBUG
    /// 测试按钮（仅Debug模式）
    private let testButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("🧪 Run Tests", for: .normal)
        button.titleLabel?.font = LNFont.regular(14)
        button.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 15
        return button
    }()
    #endif
    
    // MARK: - Properties
    override var prefersNavigationBarHide: Bool {
        return true
    }
    
    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()
        setupGradient()
        
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateGradientFrame()
    }
    
    // MARK: - Private Methods
    private func setupUI() {
        view.backgroundColor = UIColor.hex(hexString: "00E5A0")
        
        // 添加子视图
        view.addSubview(backgroundGradientView)
        view.addSubview(backgroundPatternView)
        view.addSubview(appIconImageView)
        view.addSubview(buttonsContainer)
        view.addSubview(agreementLabel)
        
        
        // 按钮相关
        buttonsContainer.addSubview(appleSignInButton)
        buttonsContainer.addSubview(googleSignInButton)
        buttonsContainer.addSubview(phoneSignInButton)
        
        // 图标
        appleSignInButton.addSubview(appleIconView)
        googleSignInButton.addSubview(googleIconView)
        phoneSignInButton.addSubview(phoneIconView)
    }
    
    private func setupConstraints() {
        // 背景渐变视图
        backgroundGradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 背景图案
        backgroundPatternView.snp.makeConstraints { make in
            make.leading.trailing.top.equalToSuperview()
            make.height.equalTo(s(516))
        }
        
        // 应用图标
        appIconImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(s(166)+jk_kStatusBarFrameH)
            make.size.equalTo(s(110))
        }
        
        // 按钮容器
        buttonsContainer.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(32)
            make.bottom.equalTo(agreementLabel.snp.top).offset(-40)
        }
        
        // Apple登录按钮
        appleSignInButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(s(40))
        }
        appleIconView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(s(16))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(26))
        }
        
        // Google登录按钮
        googleSignInButton.snp.makeConstraints { make in
            make.top.equalTo(appleSignInButton.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(s(40))
        }
        
        // Google图标
        googleIconView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(s(16))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(26))
        }
        
        // 手机号登录按钮
        phoneSignInButton.snp.makeConstraints { make in
            make.top.equalTo(googleSignInButton.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(s(40))
            make.bottom.equalToSuperview()
        }
        
        // 手机图标
        phoneIconView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(s(16))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(26))
        }
        
        // 协议文本
        agreementLabel.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(32)
            make.bottom.equalTo(-jk_kSafeDistanceBottom-s(20))
        }
    }
    
    private func setupActions() {
        appleSignInButton.addTarget(self, action: #selector(appleSignInTapped), for: .touchUpInside)
        googleSignInButton.addTarget(self, action: #selector(googleSignInTapped), for: .touchUpInside)
        phoneSignInButton.addTarget(self, action: #selector(phoneSignInTapped), for: .touchUpInside)
        
        // 添加协议文本点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(agreementTapped(_:)))
        agreementLabel.isUserInteractionEnabled = true
        agreementLabel.addGestureRecognizer(tapGesture)

    }
    
    private func setupGradient() {
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.hex(hexString: "00FFB7").cgColor,
            UIColor.hex(hexString: "2ADFD0").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1)
        gradientLayer.frame = view.bounds
        
        backgroundGradientView.layer.sublayers?.removeAll()
        backgroundGradientView.layer.insertSublayer(gradientLayer, at: 0)
    }
    
    private func updateGradientFrame() {
        if let gradientLayer = backgroundGradientView.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = view.bounds
        }
    }

   
    // MARK: - Actions
    @objc private func appleSignInTapped() {
        // 处理Apple登录
        print("Apple Sign In tapped")
        showAlert(title: "Apple Sign In", message: "Apple登录功能即将上线")
    }
    
    @objc private func googleSignInTapped() {
        // 处理Google登录
        print("Google Sign In tapped")
//        showAlert(title: "Google Sign In", message: "Google登录功能即将上线")
        let vc = LNCompleteDataViewController()
        navigationController?.pushViewController(vc, animated: true)
        
    }
    
    @objc private func phoneSignInTapped() {
        // 处理手机号登录
        LNUserManager.shared.visitorLogin { result in
            
        } failureBlock: { error in
            
        }
    }
    
    @objc private func agreementTapped(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: agreementLabel)
        let textStorage = NSTextStorage(attributedString: agreementLabel.attributedText!)
        let layoutManager = NSLayoutManager()
        let textContainer = NSTextContainer(size: agreementLabel.bounds.size)
        
        textStorage.addLayoutManager(layoutManager)
        layoutManager.addTextContainer(textContainer)
        
        let characterIndex = layoutManager.characterIndex(for: location, in: textContainer, fractionOfDistanceBetweenInsertionPoints: nil)
        
        let text = agreementLabel.text!
        let userAgreementRange = (text as NSString).range(of: "User Agreement")
        let privacyPolicyRange = (text as NSString).range(of: "Privacy Policy")
        
        if NSLocationInRange(characterIndex, userAgreementRange) {
            showAlert(title: "User Agreement", message: "用户协议页面")
        } else if NSLocationInRange(characterIndex, privacyPolicyRange) {
            showAlert(title: "Privacy Policy", message: "隐私政策页面")
        }
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

}
