//
//  LNVideoChatVC.swift
//  LiveNow
//
//  Created by edy on 2025/8/8.
//

import UIKit
import AgoraRtcKit
import JKSwiftExtension

class LNVideoChatVC: LNBaseController {
    
    override var prefersNavigationBarHide: Bool { return true }
    
    // RTC引擎
    var localVideo: AgoraRtcVideoCanvas?
    var remoteVideo: AgoraRtcVideoCanvas?
    
    var role: LNLiveRole = .inviter
    
    /// 视频聊时长统计
    var lnTimer: LNSwiftTimer?
    /// 视频聊时长
    var videoDuration: Int = 0
    /// 渠道
    var channel: String = ""
    /// token
    var rtcToken: String = ""
    var videoType: LNLiveVideoType = .normalVideo
    var targetUser: LNUserModel?
    
    init(role: LNLiveRole, targetUser: LNUserModel?) {
        super.init(nibName: nil, bundle: nil)
        self.role = role
        self.targetUser = targetUser
        // 设置页面
        layoutUI()
    }
    
    @MainActor required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 监测当前设备是否处于录屏状态
        if #available(iOS 11.0, *) {
            if UIScreen.main.isCaptured {
                // 正在录制
                self.screenCapturedMaskView.isHidden = false
            }
            // 检测到当前设备录屏状态发生变化
            NotificationCenter.default.addObserver(self, selector: #selector(screenCapturedDidChange), name: UIScreen.capturedDidChangeNotification, object: nil)
        } else {
            // Fallback on earlier versions
        }

        // 当前设备截屏通知
        NotificationCenter.default.addObserver(self, selector: #selector(takeScreenshot), name: UIApplication.userDidTakeScreenshotNotification, object: nil)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 键盘监听通知
        NotificationCenter.default.addObserver(self, selector: #selector(self.keyboardWillChangeFrame(node:)), name: UIResponder.keyboardWillChangeFrameNotification, object: nil)
        
        // 设置不自动锁屏
        UIApplication.shared.isIdleTimerDisabled = true
        
        setConfigVideo()
        configLocalVideo()
        joinAgoraChannel()
    }
    
    private func layoutUI() {
        view.addSubview(remoteContainer)
        view.addSubview(localContainer)
        localContainer.addSubview(frontBtn)
        localContainer.addSubview(timeView)
        timeView.addSubview(timeLbl)
        view.addSubview(lockView)
        view.addSubview(bottomTool)
        view.addSubview(infoView)
        view.addSubview(reportBtn)
        view.addSubview(closeBtn)
        view.addSubview(chatView)
        
        view.addSubview(chatBGMask)
        view.addSubview(chatInput)
        view.addSubview(giftContainerView)
        view.addSubview(giftEffectView)
        
        frontBtn.snp.makeConstraints { make in
            make.top.equalTo(s(8))
            make.trailing.equalTo(s(-8))
            make.size.equalTo(s(28))
        }
        timeView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(s(28))
        }
        timeLbl.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalTo(s(16))
        }
        
        bottomTool.snp.makeConstraints { make in
            make.bottom.equalTo(s(-30)-jk_kSafeDistanceBottom)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(s(52))
        }
        lockView.snp.makeConstraints { make in
            make.bottom.equalTo(bottomTool.snp.top).offset(s(-20))
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSize(width: s(344), height: s(56)))
        }
        closeBtn.snp.makeConstraints { make in
            make.trailing.equalTo(s(-15))
            make.top.equalTo(s(22)+jk_kSafeDistanceTop)
            make.size.equalTo(s(32))
        }
        reportBtn.snp.makeConstraints { make in
            make.trailing.equalTo(closeBtn.snp.leading).offset(s(-12))
            make.centerY.equalTo(closeBtn.snp.centerY)
            make.size.equalTo(s(32))
        }
        infoView.snp.makeConstraints { make in
            make.leading.equalTo(s(15))
            make.centerY.equalTo(closeBtn.snp.centerY)
            make.height.equalTo(s(50))
        }
    }
    
    /// 监听键盘
    @objc func keyboardWillChangeFrame(node: Notification) {
        if chatInput.textInput.isFirstResponder {
            // 1.获取动画执行的时间
            let duration = node.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as! TimeInterval
            
            // 2.获取键盘最终 Y值
            let endFrame = (node.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as! NSValue).cgRectValue
            let y = endFrame.origin.y
            
            // 3.计算工具栏距离底部的间距
            let margin = UIScreen.main.bounds.height - y
            
            // 4.执行动画
            UIView.animate(withDuration: duration) {
                self.chatInput.jk.bottom = self.view.jk.height-margin
            }
        }
    }
    
    @objc func chatBackgroundMaskTap() {
        chatInput.textInput.resignFirstResponder()
    }
    

    // MARK: -
    lazy var localContainer: UIView = {
        let view = UIView(frame: CGRect(x: jk_kScreenW-s(135), y: jk_kStatusBarFrameH+s(85), width: s(120), height: s(168)))
        view.backgroundColor = .red
        view.corner(s(8))
//        let tap = UITapGestureRecognizer(target: self, action: #selector(didClickLocalContainer))
//        view.addGestureRecognizer(tap)
        return view
    }()
    lazy var frontBtn: UIButton = {
        let view = UIButton(type: .custom)
        view.bgImage(UIImage(named: "ic_change_front"))
        view.addTarget(self, action: #selector(frontBtnAction), for: .touchUpInside)
        return view
    }()
    lazy var timeView: UIView = {
        let view = UIView()
        view.backgroundColor(UIColor(hexString: "#000000", alpha: 0.5))
        return view
    }()
    lazy var timeLbl: UILabel = {
        let view = UILabel()
        view.font(LNFont.regular(14)).color("#FFFFFF").text("01:18:35")
        return view
    }()
    lazy var remoteContainer: UIView = {
        let view = UIView(frame: CGRect(x: 0, y: 0, width: self.view.frame.size.width, height: self.view.frame.size.height))
        view.backgroundColor = .lightGray
        let tap = UITapGestureRecognizer(target: self, action: #selector(didClickRemoteContainer))
        view.addGestureRecognizer(tap)
        return view
    }()
    
    // 锁定提示
    lazy var lockView: LNVideoLockView = {
        let view = LNVideoLockView()
        view.unlockBtn.addTarget(self, action: #selector(lockViewAction), for: .touchUpInside)
        return view
    }()
    
    // 底部操作按钮
    lazy var bottomTool: LNVideoBottomToolView = {
        let view = LNVideoBottomToolView()
        view.delegate = self
        return view
    }()
    
    lazy var reportBtn: UIButton = {
        let view = UIButton(type: .custom)
        view.bgImage(UIImage(named: "ic_live_report"))
        return view
    }()
    
    lazy var closeBtn: UIButton = {
        let view = UIButton(type: .custom)
        view.bgImage(UIImage(named: "ic_live_close"))
        view.addTarget(self, action: #selector(closeBtnAction), for: .touchUpInside)
        return view
    }()
    
    lazy var infoView: LNTopInfoView = {
        let view = LNTopInfoView()
        view.followButton.addTarget(self, action: #selector(followButtonAction), for: .touchUpInside)
        return view
    }()
    /// 聊天视图
    lazy var chatView: LNVideoMessageView = {
        let view = LNVideoMessageView()
        view.frame = CGRect(x: s(16), y: s(300), width: self.view.jk.width-s(32), height: s(300))
        return view
    }()
    lazy var chatBGMask: UIView = {
        let view = UIView(frame: self.view.bounds)
        view.backgroundColor = .clear
        let tapGes = UITapGestureRecognizer(target: self, action: #selector(chatBackgroundMaskTap))
        view.addGestureRecognizer(tapGes)
        view.isHidden = true
        return view
    }()
    lazy var chatInput: LNChatInputView = {
        let view = LNChatInputView()
        view.frame = CGRect(x: 0, y: self.view.jk.height, width: self.view.jk.width, height: s(65))
        view.textInput.delegate = self
        view.isHidden = true
        return view
    }()
    
    lazy var screenCapturedMaskView: UIView = {
        let view = UIView(frame: self.view.bounds)
        view.backgroundColor(.black)
        view.isHidden = true
        return view
    }()
    
    /// 礼物横幅动画视图
    lazy var giftContainerView: LNGiftContainerView = {
        let view = LNGiftContainerView(frame: CGRect(x: 0, y: s(281)+jk_kSafeDistanceTop, width: s(200), height: s(90)))
        view.isUserInteractionEnabled = false
        return view
    }()
    /// 礼物特效
    lazy var giftEffectView: LNGiftEffectView = {
        let view = LNGiftEffectView(frame: self.view.bounds)
        return view
    }()

}
