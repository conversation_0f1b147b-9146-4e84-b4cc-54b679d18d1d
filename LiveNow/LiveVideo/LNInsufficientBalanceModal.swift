//
//  LNInsufficientBalanceModal.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/22.
//

import UIKit
import SnapKit

/// 余额不足弹框
class LNInsufficientBalanceModal: UIView {
    
    // MARK: - 回调
    var onRecharge: (() -> Void)?
    var onHangUp: (() -> Void)?
    var onClose: (() -> Void)?
    
    // MARK: - UI Elements
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        return view
    }()
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = s(20)
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 20
        view.layer.shadowOpacity = 0.1
        return view
    }()
    
    private lazy var iconContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.hex(hexString: "#00E5A0")
        view.layer.cornerRadius = s(30)
        return view
    }()
    
    private lazy var coinStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = s(-8)
        stackView.alignment = .center
        return stackView
    }()
    
    private lazy var warningIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = UIColor.hex(hexString: "#FFB800")
        imageView.layer.cornerRadius = s(10)
        imageView.clipsToBounds = true
        return imageView
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Insufficient balance~"
        label.font = LNFont.bold(s(24))
        label.textColor = UIColor.hex(hexString: "#00E5A0")
        label.textAlignment = .center
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "Your call balance is less than one minute.\nPlease top it up as soon as possible"
        label.font = LNFont.regular(s(16))
        label.textColor = UIColor.systemGray
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var rechargeButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Recharge", for: .normal)
        button.titleLabel?.font = LNFont.bold(s(18))
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor.hex(hexString: "#00E5A0")
        button.layer.cornerRadius = s(25)
        button.addTarget(self, action: #selector(rechargeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var hangUpButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Hang Up", for: .normal)
        button.titleLabel?.font = LNFont.medium(s(16))
        button.setTitleColor(UIColor.systemGray, for: .normal)
        button.backgroundColor = .clear
        button.addTarget(self, action: #selector(hangUpButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupActions()
        setupCoinStack()
        setupWarningIcon()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 私有方法
    private func setupUI() {
        addSubview(backgroundView)
        addSubview(containerView)
        
        containerView.addSubview(iconContainerView)
        iconContainerView.addSubview(coinStackView)
        iconContainerView.addSubview(warningIconView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(descriptionLabel)
        containerView.addSubview(rechargeButton)
        containerView.addSubview(hangUpButton)
    }
    
    private func setupConstraints() {
        // 背景视图
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 容器视图
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(s(320))
            make.height.equalTo(s(300))
        }
        
        // 图标容器
        iconContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(-30))
            make.centerX.equalToSuperview()
            make.width.height.equalTo(s(60))
        }
        
        // 硬币堆叠视图
        coinStackView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(s(40))
            make.height.equalTo(s(30))
        }
        
        // 警告图标
        warningIconView.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(s(5))
            make.trailing.equalToSuperview().offset(s(5))
            make.width.height.equalTo(s(20))
        }
        
        // 标题标签
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(iconContainerView.snp.bottom).offset(s(25))
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(s(20))
        }
        
        // 描述标签
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(15))
            make.centerX.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(s(20))
        }
        
        // 充值按钮
        rechargeButton.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(s(30))
            make.centerX.equalToSuperview()
            make.width.equalTo(s(260))
            make.height.equalTo(s(50))
        }
        
        // 挂断按钮
        hangUpButton.snp.makeConstraints { make in
            make.top.equalTo(rechargeButton.snp.bottom).offset(s(15))
            make.centerX.equalToSuperview()
            make.width.equalTo(s(100))
            make.height.equalTo(s(30))
        }
    }
    
    private func setupActions() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundView.addGestureRecognizer(tapGesture)
    }
    
    private func setupCoinStack() {
        // 创建硬币图标
        for i in 0..<3 {
            let coinView = createCoinView()
            coinStackView.addArrangedSubview(coinView)
            
            // 添加轻微的旋转效果
            let rotation = CGFloat(i - 1) * 0.1
            coinView.transform = CGAffineTransform(rotationAngle: rotation)
        }
    }
    
    private func createCoinView() -> UIView {
        let coinView = UIView()
        coinView.backgroundColor = UIColor.hex(hexString: "#FFD700")
        coinView.layer.cornerRadius = s(6)
        coinView.layer.borderWidth = 1
        coinView.layer.borderColor = UIColor.hex(hexString: "#FFA500").cgColor
        
        coinView.snp.makeConstraints { make in
            make.width.height.equalTo(s(12))
        }
        
        return coinView
    }
    
    private func setupWarningIcon() {
        // 创建感叹号图标
        let exclamationLabel = UILabel()
        exclamationLabel.text = "!"
        exclamationLabel.font = LNFont.bold(s(12))
        exclamationLabel.textColor = .white
        exclamationLabel.textAlignment = .center
        
        warningIconView.addSubview(exclamationLabel)
        exclamationLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - 事件处理
    @objc private func backgroundTapped() {
        dismiss()
    }
    
    @objc private func rechargeButtonTapped() {
        onRecharge?()
        
        // 添加按钮点击动画
        UIView.animate(withDuration: 0.1, animations: {
            self.rechargeButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.rechargeButton.transform = .identity
            }
        }
    }
    
    @objc private func hangUpButtonTapped() {
        onHangUp?()
        
        // 添加按钮点击动画
        UIView.animate(withDuration: 0.1, animations: {
            self.hangUpButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.hangUpButton.transform = .identity
            }
        }
    }
    
    // MARK: - 公共方法
    func show(in parentView: UIView) {
        parentView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 显示动画
        containerView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        containerView.alpha = 0
        backgroundView.alpha = 0
        
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.containerView.transform = .identity
            self.containerView.alpha = 1
            self.backgroundView.alpha = 1
        }
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseIn, animations: {
            self.containerView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
            self.containerView.alpha = 0
            self.backgroundView.alpha = 0
        }) { _ in
            self.removeFromSuperview()
            self.onClose?()
        }
    }
}
