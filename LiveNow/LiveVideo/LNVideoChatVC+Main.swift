//
//  LNVideoChatVC+Main.swift
//  LiveNow
//
//  Created by edy on 2025/8/8.
//

import UIKit
import JKSwiftExtension

extension LNVideoChatVC {

    
    @objc func closeBtnAction() {
        self.navigationController?.popViewController(animated: true)
    }
    
    @objc func frontBtnAction() {
        
    }
    @objc func didClickRemoteContainer() {
        
    }
    
    
    @objc func lockViewAction() {
        
    }
    
    @objc func followButtonAction() {
        infoView.followView.isHidden(true)
    }
    
    @objc func screenCapturedDidChange(notify: Notification) {
        Log("录屏状态改变---")
        if #available(iOS 11.0, *) {
            if UIScreen.main.isCaptured {
                // 正在录制
                screenCapturedMaskView.isHidden = false
            } else {
                screenCapturedMaskView.isHidden = true
            }
        } else {
            // Fallback on earlier versions
        }
    }

    @objc func takeScreenshot(notify: Notification) {
        Log("截屏了")
//        self.view.makeToast("To protect users' privacy, screenshot is not allowed".localized)
    }

}
