//
//  LNTopInfoView.swift
//  LiveNow
//
//  Created by edy on 2025/8/11.
//

import UIKit
import JKSwiftExtension

class LNTopInfoView: UIView {

    override init(frame: CGRect) {
        super.init(frame: frame)
        corner(s(25)).backgroundColor(UIColor(hexString: "#000000", alpha: 0.5))
        layer.borderWidth(s(1)).borderColor(UIColor(hexString: "#00DFAB")!)
        setupSubviews()
        
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        addSubview(hStack)
        hStack.addArrangedSubviews(avatarImageView, leftContentV, followView)
        hStack.setCustomSpacing(s(3), after: avatarImageView)
        hStack.setCustomSpacing(s(10), after: leftContentV)
        
        leftContentV.addSubview(nameLabel)
        leftContentV.addSubview(btView)
        btView.addSubview(genderView)
        btView.addSubview(countryContainer)
        countryContainer.addSubview(countryLabel)
        followView.addSubview(followButton)
        
        hStack.snp.makeConstraints { make in
            make.leading.equalTo(s(5))
            make.trailing.equalTo(s(-10))
            make.centerY.equalToSuperview()
        }
        leftContentV.snp.makeConstraints { make in
            make.height.equalTo(s(50))
            make.width.greaterThanOrEqualTo(nameLabel)
            make.width.greaterThanOrEqualTo(btView)
        }
        followView.snp.makeConstraints { make in
            make.height.equalTo(s(50))
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(s(4))
            make.width.height.equalTo(s(36))
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(s(4))
            make.leading.equalTo(avatarImageView.snp.trailing).offset(s(4))
            make.height.equalTo(s(16))
            make.width.lessThanOrEqualTo(s(100))
        }
        btView.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(s(7))
            make.leading.equalTo(nameLabel.snp.leading)
            make.height.equalTo(s(14))
        }
        genderView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalToSuperview()
            make.height.equalTo(s(14))
        }
        countryContainer.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(genderView.snp.trailing).offset(s(4))
            make.height.equalTo(s(14))
            make.trailing.equalToSuperview()
        }
        
        countryLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 2, left: 8, bottom: 2, right: 8))
        }

        followButton.snp.makeConstraints { make in
            make.centerY.equalTo(followView.snp.centerY)
            make.trailing.equalToSuperview()
            make.width.equalTo(s(88))
            make.height.equalTo(s(32))
            make.leading.equalToSuperview()
        }
    }
    
    
    
    private lazy var hStack: UIStackView = {
        let view = UIStackView()
        view.set(axis: .horizontal).set(spacing: s(15)).set(alignment: .center)
        return view
    }()
    
    private lazy var leftContentV: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = s(18)
        imageView.backgroundColor = UIColor.systemGray5
        return imageView
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.text = "Name"
        label.font = LNFont.bold(16)
        label.textColor = UIColor.white
        return label
    }()
    
    private lazy var btView: UIView = {
        let view = UIView()
        return view
    }()
    private lazy var genderView: LNGenderView = {
        let view = LNGenderView()
        return view
    }()
    
    private lazy var countryContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hexString: "#44E3D0")
        view.layer.cornerRadius = s(7)
        return view
    }()
    
    private lazy var countryLabel: UILabel = {
        let label = UILabel()
        label.text = "Indonesia"
        label.font = LNFont.regular(11)
        label.textColor = UIColor.white
        return label
    }()
    lazy var followView: UIView = {
        let view = UIView()
        return view
    }()
    lazy var followButton: UIButton = {
        let button = UIButton(type: .custom)
        button.image(UIImage(named: "ic_follow_add")).title("Follow").font(LNFont.regular(14)).textColor(UIColor.white).corner(s(16)).bgImage(UIImage.jk.gradient(["#04E798", "#0ADCE1"], size: CGSize(width: s(88), height: s(32)), direction: .horizontal))
        button.jk.setImageTitleLayout(.imgLeft, spacing: s(4))
        return button
    }()

}
