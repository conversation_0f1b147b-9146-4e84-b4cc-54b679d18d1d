//
//  LNGenderView.swift
//  LiveNow
//
//  Created by pp on 2025/8/17.
//

import UIKit
import JKSwiftExtension

class LNGenderView: UIView {
    
    private var gender: Int = 0 {
        didSet {
            if gender == 1 {
                // 男性
                genderIcon.image = UIImage(named: "ic_male")
                backgroundColor("#77DBFF")
            } else {
                // 女性（gender == 2 或其他值）
                genderIcon.image = UIImage(named: "ic_female")
                backgroundColor("#FF77C9")
            }
        }
    }

    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor("#FF77C9").corner(s(7))
        setupSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        addSubview(genderIcon)
        addSubview(ageLabel)
        genderIcon.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(s(4))
            make.size.equalTo(s(12))
        }
        ageLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(genderIcon.snp.trailing).offset(s(2))
            make.height.equalTo(s(10))
            make.trailing.equalTo(s(-6))
        }
    }
    
    func bindModel(_ user: LNUserModel) {
        ageLabel.text("\(user.age)")
        // 将字符串类型的gender转换为Int
        if let genderInt = Int(user.gender) {
            gender = genderInt
        } else {
            gender = 2 // 默认为女性
        }
    }

    // MARK: - Lazy
    lazy var genderIcon: UIImageView = {
        let view = UIImageView()
        view.backgroundColor(.clear)
        view.image = UIImage(named: "ic_gender_female")
        return view
    }()
    
    lazy var ageLabel: UILabel = {
        let label = UILabel()
        label.font(LNFont.regular(10)).color("#FFFFFF").text("0")
        return label
    }()
}
