//
//  LNPriceView.swift
//  LiveNow
//
//  Created by pp on 2025/8/17.
//

import UIKit

/// 价格
class LNPriceView: UIView {

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        
        addSubview(priceNumLbl)
        addSubview(diamondImageView)
        addSubview(priceUnitLbl)
        
        priceNumLbl.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.centerY.equalToSuperview()
            make.height.equalTo(s(19))
        }
        diamondImageView.snp.makeConstraints { make in
            make.leading.equalTo(priceNumLbl.snp.trailing).offset(s(4))
            make.centerY.equalToSuperview()
            make.size.equalTo(s(18))
        }
        priceUnitLbl.snp.makeConstraints { make in
            make.leading.equalTo(diamondImageView.snp.trailing).offset(s(4))
            make.centerY.equalToSuperview()
            make.height.equalTo(s(19))
            make.trailing.equalToSuperview()
        }
        
    }

    lazy var priceNumLbl: UILabel = {
        let label = UILabel()
        label.font = LNFont.regular(16)
        label.textColor = UIColor.white
        label.textAlignment = .left
        label.text("50")
        return label
    }()
    
    private lazy var diamondImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_diamond")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    private lazy var priceUnitLbl: UILabel = {
        let label = UILabel()
        label.font = LNFont.regular(16)
        label.textColor = UIColor.white
        label.textAlignment = .left
        label.text("/min")
        return label
    }()
}
