//
//  LNVideoBottomToolView.swift
//  LiveNow
//
//  Created by edy on 2025/8/11.
//

import UIKit
import JKSwiftExtension

protocol LNVideoBottomToolViewDelegate: NSObjectProtocol {
    
    func bottomGiftItemAction()
    func bottomMicItemAction()
    func bottomBlurItemAction()
    func bottomChatItemAction()
    
}

class LNVideoBottomToolView: UIView {

    weak var delegate: LNVideoBottomToolViewDelegate?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .clear
        setupSubviews()
        
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        
        addSubview(stack)
        stack.addArrangedSubviews(micItem, blurItem, giftItem, chatItem)
        
        stack.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        [micItem, blurItem, giftItem, chatItem].forEach { view in
            view.snp.makeConstraints { make in
                make.size.equalTo(s(52))
            }
        }
        
    }
    
    @objc func giftItemAction() {
        delegate?.bottomGiftItemAction()
    }
    @objc func micItemAction() {
        delegate?.bottomMicItemAction()
    }
    @objc func blurItemAction() {
        delegate?.bottomBlurItemAction()
    }
    @objc func chatItemAction() {
        delegate?.bottomChatItemAction()
    }
    
    
    //
    lazy var stack: UIStackView = {
        let view = UIStackView()
        view.axis = .horizontal
        view.alignment = .center
        view.set(spacing: s(42))
        return view
    }()
    
    
    lazy var micItem: UIButton = {
        let view = UIButton(type: .custom)
        view.bgImage(UIImage(named: "ic_live_mic"))
        view.addTarget(self, action: #selector(micItemAction), for: .touchUpInside)
        return view
    }()
    
    lazy var blurItem: UIButton = {
        let view = UIButton(type: .custom)
        view.bgImage(UIImage(named: "ic_live_blur"))
        view.addTarget(self, action: #selector(blurItemAction), for: .touchUpInside)
        return view
    }()
    
    lazy var giftItem: UIButton = {
        let view = UIButton(type: .custom)
        view.bgImage(UIImage(named: "ic_live_gift"))
        view.addTarget(self, action: #selector(giftItemAction), for: .touchUpInside)
        return view
    }()
    
    lazy var chatItem: UIButton = {
        let view = UIButton(type: .custom)
        view.bgImage(UIImage(named: "ic_live_chat"))
        view.addTarget(self, action: #selector(chatItemAction), for: .touchUpInside)
        return view
    }()

}
