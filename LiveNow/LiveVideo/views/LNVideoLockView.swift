//
//  LNVideoLockView.swift
//  LiveNow
//
//  Created by edy on 2025/8/11.
//

import UIKit
import JKSwiftExtension

class LNVideoLockView: UIView {

    override init(frame: CGRect) {
        super.init(frame: frame)
        layer.insertSublayer(gradientLayer, at: 0)
        corner(s(8))
        setupSubviews()
        
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        addSubview(desLbl)
        addSubview(unlockBtn)
        desLbl.snp.makeConstraints { make in
            make.leading.equalTo(s(16))
            make.top.equalTo(s(12))
            make.width.equalTo(s(160))
        }
        unlockBtn.snp.makeConstraints { make in
            make.trailing.equalTo(s(-26))
            make.centerY.equalToSuperview()
            make.size.equalTo(s(32))
        }
    }
    
    lazy var desLbl: UILabel = {
        let view = UILabel()
        view.text("Recharge diamonds to \ncontinue the call").line(2).color(UIColor(hexString: "#FFF951")!)
        return view
    }()
    
    lazy var unlockBtn: UIButton = {
        let view = UIButton(type: .custom)
        view.bgImage(UIImage(named: "ic_live_lock"))
        return view
    }()
    
    lazy var gradientLayer: CAGradientLayer = {
        let bgLayer = CAGradientLayer()
        bgLayer.colors = [UIColor(hexString: "#F4AC6D")?.cgColor as Any, UIColor(hexString: "#F55DE1")?.cgColor as Any]
        bgLayer.locations = [0, 1]
        bgLayer.frame = CGRect(x: 0, y: 0, width: s(344), height: s(56))
        bgLayer.startPoint = CGPoint(x: 0, y: 0)
        bgLayer.endPoint = CGPoint(x: 1, y: 1)
        return bgLayer
    }()

}
