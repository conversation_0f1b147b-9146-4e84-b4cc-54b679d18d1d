//
//  LNChatInputView.swift
//  LiveNow
//
//  Created by edy on 2025/8/11.
//

import UIKit
import YYText
import JKSwiftExtension

protocol LNChatInputViewDelegate: NSObjectProtocol {
    func sendChatMessage(text: String)
}

class LNChatInputView: UIView {
    
    weak var delegate: LNChatInputViewDelegate?
    
    /// 输入框最大高度
    var maxTextH: CGFloat {
        return s(80)
    }
    /// 输入框最小高度
    var minTextH: CGFloat {
        return s(40)
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        setupSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        backgroundColor = UIColor(hexString: "#FFFFFF")
        addSubview(textInput)
        addSubview(sendBtn)
    }
    
    // MARK: - Event
    @objc func onSendMessageBtn() {
        if textInput.text.count == 0 {return}
        
        delegate?.sendChatMessage(text: textInput.text)
        textInput.text = ""
        textInput.resignFirstResponder()
    }
    
    // MARK: - Lazy
    /// 输入框
    lazy var textInput: YYTextView = {
        let textInput = YYTextView(frame: CGRect(x: s(16), y: s(16), width: s(247), height: s(40)))
        textInput.backgroundColor = UIColor(hexString: "#F5F7F9")
        textInput.layer.cornerRadius = s(20)
        textInput.layer.masksToBounds = true
        textInput.tintColor = UIColor(hexString: "#66FE6B")
//        textInput.font =
        textInput.textContainerInset = UIEdgeInsets(top: s(10), left: s(16), bottom: s(10), right: s(16))
        textInput.textColor = UIColor(hexString: "#111111")
        textInput.placeholderText = "一起聊聊吧"
        textInput.placeholderTextColor = UIColor(hexString: "#999999")
        textInput.returnKeyType = .default
        textInput.keyboardAppearance = .light
        return textInput
    }()
    
    lazy var sendBtn: UIButton = {
        let btn = UIButton(type: .custom)
        btn.frame = CGRect(x: self.jk.width-s(80)-s(16), y: s(16), width: s(80), height: s(40))
        btn.setTitleColor(UIColor(hexString: "#111111"), for: .normal)
        btn.setTitleColor(UIColor(hexString: "#999999"), for: .disabled)
        btn.setTitle("send", for: .normal)
        btn.setBackgroundImage(UIImage.jk.gradient(["#A3FF2C", "#31FFA1"], size: btn.jk.size), for: .normal)
        btn.setBackgroundImage(UIImage.jk.image(color: UIColor(hexString: "#E9EAEF")!, size: btn.jk.size), for: .disabled)
//        btn.titleLabel?.font(.pingFangRegular(14))
        btn.layer.cornerRadius = s(20)
        btn.layer.masksToBounds = true
        btn.addTarget(self, action: #selector(onSendMessageBtn), for: .touchUpInside)
        btn.isEnabled = false
        return btn
    }()

}
