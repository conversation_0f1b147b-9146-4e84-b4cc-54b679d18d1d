//
//  LNVideoChatVC+Delegate.swift
//  LiveNow
//
//  Created by edy on 2025/8/11.
//

import YYText

extension LNVideoChatVC: LNVideoBottomToolViewDelegate {
    func bottomGiftItemAction() {
        giftContainerView.showModel(LNGiftChannelModel("", "", 1, "", 1, "", "", 1, 1))
    }
    
    func bottomMicItemAction() {
        
    }
    
    func bottomBlurItemAction() {
        
    }
    
    func bottomChatItemAction() {
        self.chatInput.textInput.becomeFirstResponder()
    }
    
    
    
    
}

// MARK: - YYTextViewDelegate
extension LNVideoChatVC: YYTextViewDelegate {
    func textView(_ textView: YYTextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        if text == "" {
            // 判断删除的是一个@ 中间的字符就整体删除
            var index = range.location
            if let textStr = textView.text {
                let txtMutabSting = NSMutableString(string: textStr)
                do {
                    let regex = try NSRegularExpression(pattern: "@[\\u4e00-\\u9fa5\\w\\-\\_]+ ", options: .caseInsensitive)
                    let matches = regex.matches(in: textStr, range: NSRange(location: 0, length: textStr.count))
                    for match in matches {
                        let newRange = NSRange(location: match.range.location + 1, length: match.range.length - 1)
                        if NSLocationInRange(range.location, newRange) {
                            index = match.range.location
                            txtMutabSting.replaceCharacters(in: match.range, with: "")
                            textView.selectedRange = NSRange(location: index, length: 0)
                            textView.text = txtMutabSting as String
                            return false
                        }
                    }
                } catch {
                    
                }
            }
        }
        
        if text == "\n" {
            if textView.text.count != 0 {
                if let textRange = textView.textRange(from: textView.beginningOfDocument, to: textView.endOfDocument) {
                    let newText = (textView.text as NSString).replacingCharacters(in: range, with: "\n")
                    textView.text = newText
                    
                    // 更新光标位置
                    let newPosition = textView.position(from: textView.beginningOfDocument, offset: range.location + 1)
                    if let newPosition = newPosition {
                        textView.selectedTextRange = textView.textRange(from: newPosition, to: newPosition)
                    }
                }
            }
            
            return false
        }
        
        chatInput.sendBtn.isEnabled = textView.text.count + (text.count - range.length) > 0
        
        return true
    }
    
    func textViewDidChange(_ textView: YYTextView) {
        
        // 获取textview的真实高度
        let frame = textView.frame
        let constrainSize = CGSize(width: frame.size.width, height: CGFloat(MAXFLOAT))
        var size = textView.sizeThatFits(constrainSize)
        if size.height >= chatInput.maxTextH {
            size.height = chatInput.maxTextH
            textView.isScrollEnabled = true
        } else if size.height <= chatInput.minTextH {
            size.height = chatInput.minTextH
            textView.isScrollEnabled = false
        } else {
            textView.isScrollEnabled = false
        }
        
        UIView.animate(withDuration: 0.25) {
            // 重新设置textview的高度
            self.chatInput.textInput.jk.height = size.height
            
            // 重新设置chatInput的 Frame
            let originFrame = self.chatInput.frame
            self.chatInput.jk.height = size.height + s(25)
            self.chatInput.jk.top = originFrame.origin.y - (self.chatInput.jk.height-originFrame.size.height)
            self.chatInput.sendBtn.jk.bottom = self.chatInput.jk.height - s(14)
        }
    }
    
    func textViewDidBeginEditing(_ textView: YYTextView) {
        chatInput.isHidden = false
        chatBGMask.isHidden = false
    }
    
    func textViewDidEndEditing(_ textView: YYTextView) {
        chatInput.isHidden = true
        chatBGMask.isHidden = true
    }
}
