//
//  LNGiftContainerView.swift
//  LiveNow
//
//  Created by edy on 2025/8/20.
//

import UIKit
import JKSwiftExtension

private let kChannelCount = 2
private let kChannelViewH : CGFloat = s(40)
private let kChannelMargin : CGFloat = s(10)

class LNGiftContainerView: UIView {
    
    fileprivate lazy var channelViews : [LNGiftChannelView] = [LNGiftChannelView]()
    fileprivate lazy var cacheGiftModels : [LNGiftChannelModel] = [LNGiftChannelModel]()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        setupSubviews()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        
        let w : CGFloat = frame.width
        let h : CGFloat = kChannelViewH
        let x : CGFloat = 0
        
        for i in 0..<kChannelCount {
            
            let y: CGFloat = (h + kChannelMargin) * CGFloat(i)
            
            var channelView = LNGiftChannelView(frame: CGRect(x: x, y: y, width:w, height: h))
            channelView.alpha = 0
            addSubview(channelView)
            channelView.jk.left = x
            
            channelViews.append(channelView)
            
            channelView.complectionCallback = { [weak self] channelView in
                guard let `self` = self else {return}
                // 1.取出缓存中的模型
                guard self.cacheGiftModels.count != 0 else {
                    return
                }
                
                // 2.取出缓存中的第一个模型数据
                let maxValueIndex = self.findHighestValueGiftItemIndex()
                let firstModel = self.cacheGiftModels[maxValueIndex]
                self.cacheGiftModels.remove(at: maxValueIndex)
                
                // 3.让闲置的channelView执行动画
                channelView.giftModel = firstModel
                
                // 4.将数组中剩余有和firstModel相同的模型放入到ChanelView缓存中
                for i in (0..<self.cacheGiftModels.count).reversed() {
                    
                    let giftModel = self.cacheGiftModels[i]
                    
                    if giftModel .isEqual(firstModel) {
                        
                        channelView.addOneToCache()
                        
                        self.cacheGiftModels.remove(at: i)
                    }
                }
                
            }
        }
    }
    
    func showModel(_ giftModel : LNGiftChannelModel){
    
        // 1.判断正在忙的ChanelView和赠送的新礼物的(senderName/receiverName/giftname)
        if let channelView = checkUsingChanelView(giftModel) {
            // 更新当前赠送的礼物数量
            channelView.giftModel?.giftCount = giftModel.giftCount
            channelView.addOneToCache()
            return
        }
        // 2. 判断有没有闲置的channelView
        if let channelView = checkIdleChannelView(){
        
            channelView.giftModel = giftModel
            
            return
        }
        // 3. 将数据加入缓存中
        cacheGiftModels.append(giftModel)
        
    }
    
    // 检查正在使用的channelView
    private func checkUsingChanelView(_ giftModel : LNGiftChannelModel) -> LNGiftChannelView? {
        
        for channelView in channelViews {
            
            if giftModel.isEqual(channelView.giftModel)
              && channelView.channelViewState != .endAnimating {
                
                return channelView
            }
        }
        
        return nil
    }
    
    // 检查有没有闲置的channel
    private func checkIdleChannelView() -> LNGiftChannelView? {
        
        for channelView in channelViews {
            
            if channelView.channelViewState == .idle {
                return channelView
            }
            
        }
        
        return nil
    }
    
    /// 找到礼物价值最高的礼物特效索引
    /// - Returns: 索引值
    private func findHighestValueGiftItemIndex() -> Int {
        var maxValueIndex: Int = 0
        var maxValue: Int = 0
        for (index, effectItem) in cacheGiftModels.enumerated() {
            if effectItem.giftValue > maxValue {
                maxValue = effectItem.giftValue
                maxValueIndex = index
            }
        }
        return maxValueIndex
    }
}
