//
//  LNGiftChannelModel.swift
//  LiveNow
//
//  Created by edy on 2025/8/20.
//

import UIKit

class LNGiftChannelModel: NSObject {
    /// 发送方昵称
    var senderName: String = ""
    /// 发送方性别
    var senderGender: Int = 1
    /// 发送方头像
    var senderUrl: String = ""
    /// 接收方昵称
    var receiverName: String = ""
    /// 接收方性别
    var receiverGender: Int = 1
    /// 礼物名称
    var giftName: String = ""
    /// 礼物图标
    var giftIcon: String = ""
    /// 礼物个数
    var giftCount: Int = 1
    /// 礼物价值（金币礼物：银币礼物=1:10）
    var giftValue: Int = 0
    /// 礼物横幅唯一Key
    var giftChannelKey: String = ""
    
    init(_ senderName: String, _ senderUrl: String, _ senderGender: Int, _ receiverName: String, _ receiverGender: Int, _ giftName: String, _ giftIcon: String, _ giftCount: Int, _ giftValue: Int) {
        
        self.senderName = senderName
        self.senderUrl = senderUrl
        self.senderGender = senderGender
        self.receiverName = receiverName
        self.receiverGender = receiverGender
        self.giftName = giftName
        self.giftIcon = giftIcon
        self.giftCount = giftCount
        self.giftValue = giftValue
        self.giftChannelKey = senderName + receiverName + giftName
    }
    
    // 重写isEqual方法
    override func isEqual(_ object: Any?) -> Bool {
        
        guard let object = object as? LNGiftChannelModel  else {
            return false
        }
        
        guard object.senderName == senderName && object.receiverName == receiverName && object.giftName == giftName else {
            
            return false
        }
        
        return true
    }
    
}
