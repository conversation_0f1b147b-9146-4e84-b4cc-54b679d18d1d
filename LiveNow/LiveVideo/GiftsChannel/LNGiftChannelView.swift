//
//  LNGiftChannelView.swift
//  LiveNow
//
//  Created by edy on 2025/8/20.
//

import UIKit
import JKSwiftExtension

enum LNGiftChannelState {

    case idle  //闲置的
    case animating //正在执行动画
    case willEnd  //将要结束动画
    case endAnimating //已经结束动画
    
}

class LNGiftChannelView: UIView {
    
    var complectionCallback: ((LNGiftChannelView) -> Void)?
    
    //当前礼物数
    var currentNumber: Int = 0
    //当前缓存数
    var currentCacheNumber: Int = 0
    
    var channelViewState: LNGiftChannelState = .idle
  
    var giftModel: LNGiftChannelModel? {
        didSet{
            // 1. 模型校验
            guard let giftModel = giftModel else {
                return
            }
            // 2. 设置基本信息
            bindDataModel(giftModel: giftModel)
            // 3. 执行动画将ChanelView弹出
            channelViewState = .animating
            performAnimation()
        }
    
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupSubviews()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        addSubview(cornerView)
        cornerView.addSubview(giftIconView)
        cornerView.addSubview(digitLabel)
    }
    
    private func setupConstraints() {
        cornerView.snp.makeConstraints { make in
            make.leading.equalTo(s(20))
            make.top.bottom.equalToSuperview()
            make.width.equalTo(s(150))
        }
    
        giftIconView.snp.makeConstraints { (make) in
            make.size.equalTo(s(40))
            make.leading.equalTo(s(15))
            make.centerY.equalToSuperview()
        }
        digitLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.height.equalTo(s(30))
            make.leading.equalTo(giftIconView.snp.trailing).offset(s(9))
        }

    }
    
    private func bindDataModel(giftModel: LNGiftChannelModel) {
//        avatarView.uc_setImage(imgStr: giftModel.senderUrl)
//        nickNameLabel.text = giftModel.senderName
//        nickNameLabel.textColor = UIColor(hexString: giftModel.senderGender == 2 ? "#FFA5A5" : "#50FFF3")
//        let receiverName = giftModel.receiverName
//        let receiverGender: Int = giftModel.receiverGender
//        let totalStr = "送给" + receiverName
//        let attributeStr = NSMutableAttributedString(string: totalStr)
//        if let range = totalStr.uc_exMatchStrRange(receiverName).first {
//            attributeStr.addAttributes([.foregroundColor: UIColor(hexString: receiverGender == 2 ? "#FFA5A5" : "#50FFF3")!], range: range)
//        }
//        giftLabel.attributedText = attributeStr
//       // giftIconView.uc_setImage(imgStr: giftModel.giftIcon)
        digitLabel.text = " x 1"
//        giftIconView.uc_setImage(imgStr: giftModel.giftIcon)
    }
    
    // 添加到缓存池
    func addOneToCache(){
        if channelViewState == .willEnd {
            performAnimation()
            // 取消延迟2秒
            NSObject.cancelPreviousPerformRequests(withTarget: self)
            
        } else {
            currentCacheNumber += 1
        }
    }
    
    // MARK: - Lazy
    lazy var cornerView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "ic_gift_channel")
        return view
    }()
    lazy var giftIconView: UIImageView = {
        let view = UIImageView()
        view.backgroundColor(.red)
        return view
    }()
    lazy var digitLabel: LNGiftDigitLabel = {
        let label = LNGiftDigitLabel()
        label.font = LNFont.bold(30)
        label.textColor = UIColor(hexString: "#FFFFFF")
        return label
    }()
}
// MARK: - 执行动画
extension LNGiftChannelView {

    func performAnimation(){
        
        digitLabel.alpha = 1.0
        
        UIView.animate(withDuration: 0.25, animations: {
            
            self.alpha = 1.0
            self.frame.origin.x = 0
            
        }) { (isFinished) in
            
            self.performDigitAnimation()
        }
    }
    
    func performDigitAnimation(){
    
        if let gift = giftModel {
            currentNumber += gift.giftCount
        } else {
            currentNumber += 1
        }
        digitLabel.text = " x\(currentNumber)"
        
        digitLabel.showDigitAnimation {
        
            if self.currentCacheNumber > 0 {
            
                self.currentCacheNumber -= 1
            
                self.performDigitAnimation()
                
            } else {
            
                self.channelViewState = .willEnd
                self.perform(#selector(self.performEndAnimation), with: nil, afterDelay: 2.0)
            }
            
        }
    }
    
    @objc fileprivate func performEndAnimation() {
    
        channelViewState = .endAnimating
        UIView.animate(withDuration: 0.25, animations: {
            
            self.frame.origin.x = -self.frame.width
            self.alpha = 0.0
            
        }, completion: { (isFinished) in
            
            self.currentNumber = 0
            self.currentCacheNumber = 0
            self.giftModel = nil
            self.frame.origin.x = -self.frame.width
            self.channelViewState = .idle
            self.digitLabel.alpha = 0
          
            if let complectionCallback = self.complectionCallback {
                
                complectionCallback(self)
            }
        })
    }
    
}

class LNGiftDigitLabel: UILabel {
    
    func showDigitAnimation(_ complection: @escaping () -> ()) {
        
        UIView.animateKeyframes(withDuration: 0.1, delay: 0, options: [], animations: {
            UIView.addKeyframe(withRelativeStartTime: 0, relativeDuration: 0.5, animations: {
                self.transform = CGAffineTransform(scaleX: 1.5, y: 1.5)
            })
            
        }) { (isFinished) in
            UIView.animate(withDuration: 0.1, delay: 0, usingSpringWithDamping: 0.3, initialSpringVelocity: 10, options: [], animations: {
                self.transform = .identity
            }, completion: { (isFinished) in
                complection()
            })
        }
    }
}
