//
//  LNGiftEffectView.swift
//  LiveNow
//
//  Created by edy on 2025/8/20.
//

import UIKit
import SVGAPlayer

class LNGiftEffectView: UIView {
    
    let playQueue = DispatchQueue.init(label: "com.svga.queue", attributes: .concurrent)
    let effectQueue = DispatchQueue.init(label: "com.svgaEffect.queue", attributes: .concurrent)
    let svgaLock = NSLock.init()
    let svgaGroup = DispatchGroup.init()
    /// 总共需要播放的次数
    var playTimes = 0
    /// 当前已经播放的次数
    var completeTimes = 0
    /// 存储礼物svga特效
    var effectGiftArray: [String] = []
    
    /// 通知是否调用播放
    var isEffectFinished: Bool = true
    /// 当前播放的svga
    var currentEffectName: String = ""
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        setupSubviews()
        
//        self.addObserver(self, forKeyPath: "effectArray", options: .new, context: nil)
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        self.isUserInteractionEnabled = false
        
        addSubview(aPlayer)
    }
    
    private func clean() {
        aPlayer.stopAnimation()
        aPlayer.clear()
        aPlayer.clearDynamicObjects()
        aPlayer.delegate = nil
    }
    
    /// type:  默认为0 ：礼物。1：坐骑
    func addSvgaEffects(effectName: String, type: Int) {
        if type == 0 { // 礼物数组
            effectGiftArray.append(effectName)
        }
        
        if effectGiftArray.count > 0, isEffectFinished == true {// 如果数组有值
            
            isEffectFinished = false
            
            // 播放礼物或坐骑特效
            playSvga(effectArray: effectGiftArray)
        }
    }
    
    /// 播放svga
    func playSvga(effectArray: [String]) {
        
        guard let effectName = effectArray.first else {return}
        
        currentEffectName = effectName
        
        LNPreloadManager.shared.load(urlString: effectName) { [weak self] result in
            guard let `self` = self else {return}
            switch result {
            case let .success(filePath):
                Log("加载礼物特效成功---\(filePath)")
                guard let data = try? Data(contentsOf: URL(fileURLWithPath: filePath)) else { return }
                self.parser.parse(with: data, cacheKey: effectName) { (videoItem) in
                    self.aPlayer.videoItem = videoItem
                    self.aPlayer.startAnimation()
                } failureBlock: { (error)  in
                    Log("特效播放失败---\(error.localizedDescription)")
                }
            case let .failure(error):
                Log("加载礼物特效失败---\(error.localizedDescription)")
                self.playerFinished()
            }
        }
    }
    
    /// 播放礼物特效
    func playSvgaEffects(effectName: String, type: Int = 0) {
        
        if type == 0 {// 礼物
            // 是否开启礼物，默认开启
//            if WOVoiceRoomSettingManager.shared.giftVisualEffectsEnable {
                addSvgaEffects(effectName: effectName, type: type)
//            }
        }
        

    }
    
    private func addNotify() {
        svgaGroup.notify(queue: playQueue) {
            Log("\(self.playTimes)次动画已经全部播放完毕🌹🌹🌹🌹💐")
            // 动画播放完毕，将指示器全部重置，并且清除播放器
            self.playTimes = 0
            self.completeTimes = 0
            self.aPlayer.clear()
        }
    }
    
    private func lock() {
        // 上锁，并且group的enter+1，当group的enter和leave成对出现时，才会触发notify
        self.svgaGroup.enter()
        self.svgaLock.lock()
    }
    
    private func unlock() {
        // 解锁
        playQueue.async(group: svgaGroup) {
            self.svgaLock.unlock()
            self.svgaGroup.leave()
        }
    }
    
    deinit {
        clean()
        Log("WOGiftSvgaEffectsView---Dealloc")
    }
    // MARK: - Lazy
    lazy var aPlayer: SVGAPlayer = {
        let aPlayer = SVGAPlayer(frame: self.bounds)
        aPlayer.contentMode = .scaleAspectFill
        aPlayer.delegate = self
        aPlayer.backgroundColor = .clear
        // 重复播放次数
        aPlayer.loops = 1
        // 播放完成之后移除View
        aPlayer.clearsAfterStop = true
        return aPlayer
    }()
    
    lazy var parser: SVGAParser = {
        let parser = SVGAParser()
        return parser
    }()
}
// MARK: - SVGAPlayerDelegate
extension LNGiftEffectView: SVGAPlayerDelegate {
    // 动画播完毕
    func svgaPlayerDidFinishedAnimation(_ player: SVGAPlayer!) {
        Log("动画播完毕")
//        //播放完毕，解锁，进行下一个操作
//        unlock()

        playerFinished()
        
    }
    
    func playerFinished() {
        // 关闭礼物特效(ps：清除礼物数组里的数据，后续就没有播放的了)
//        if WOVoiceRoomSettingManager.shared.giftVisualEffectsEnable == false {
//            effectGiftArray.removeAll()
//        }
        
        // 播放完毕后，移除当前播放的特效
        if effectGiftArray.contains(currentEffectName) {
            effectGiftArray.remove(currentEffectName)
        }
        
        // 播放完毕，播放下一个
        if effectGiftArray.count > 0 {// 再播放礼物
            self.playSvga(effectArray: effectGiftArray)
            
        } else if effectGiftArray.count == 0  {
            isEffectFinished = true
        }
    }
}

