//
//  LNVideoChatVC+Agora.swift
//  LiveNow
//
//  Created by edy on 2025/8/8.
//

import UIKit
import AgoraRtcKit

extension LNVideoChatVC {

    // 配置代理
    func setConfigVideo() {
        LNAgoraManager.shared.rtcEngine.delegate = self
        LNAgoraManager.shared.rtcEngine.enableVideo()
        LNAgoraManager.shared.rtcEngine.enableAudio()
        
        // 复位摄像头
        let configuration = AgoraCameraCapturerConfiguration()
        configuration.cameraDirection = .front
        LNAgoraManager.shared.rtcEngine.setCameraCapturerConfiguration(configuration)
    }
    // 设置本地视频采集
    func configLocalVideo() {
        
        // 设计者本地采集
        let view = UIView(frame: CGRect(origin: CGPoint(x: 0, y: 0), size: localContainer.frame.size))
        localVideo = AgoraRtcVideoCanvas()
        localVideo!.view = view
        localVideo!.renderMode = .hidden
        localVideo!.uid = 0
        localContainer.addSubview(localVideo!.view!)
        LNAgoraManager.shared.rtcEngine.setupLocalVideo(localVideo)
        LNAgoraManager.shared.rtcEngine.startPreview()
    }
    
    // 加入频道
    func joinAgoraChannel() {
        LNAgoraManager.shared.rtcEngine.setDefaultAudioRouteToSpeakerphone(true)
        // 设置频道场景-聊天
        _ = LNAgoraManager.shared.rtcEngine.setChannelProfile(.liveBroadcasting)
        LNAgoraManager.shared.rtcEngine.setClientRole(AgoraClientRole.broadcaster)
        // 快速出图，在joinchannel前调用即可
        LNAgoraManager.shared.rtcEngine.enableInstantMediaRendering()
//        DDLogInfo("设置频道场景-聊天:\(result)")
        if let targetUser = self.targetUser {
            Log("rtcToken: \(self.rtcToken)\n channelId:\(self.channel) \n uid:\(targetUser.id)")
            
            let code = LNAgoraManager.shared.rtcEngine.joinChannel(byToken: self.rtcToken, channelId: self.channel, info: nil, uid: UInt(targetUser.id)) {  (channel, uid, elapsed) -> Void in
                // Did join channel
                Log("加入房间channel\(channel),uid\(uid),elapsed\(elapsed)")
                let dic = [
                    "channelId": self.channel
                ]
                NetWorkRequest(LNApiVideo.postVideoPush(par: dic)) { result in
                    
                } failure: { error in
                    
                }
                
            }
            Log("rtc join code: \(code)")

        }

    }
    
    // 移除试图
    func removeFromParent(_ canvas: AgoraRtcVideoCanvas?) -> UIView? {
        if let it = canvas, let view = it.view {
            let parent = view.superview
            if parent != nil {
                view.removeFromSuperview()
                return parent
            }
        }
        return nil
    }
    
    // 离开频道
    func leavelChannel() {
        LNAgoraManager.shared.rtcEngine.stopPreview()
        LNAgoraManager.shared.rtcEngine.leaveChannel(nil)

        UIApplication.shared.isIdleTimerDisabled = false
    }
    
    func clearAgoraKit() {
        
        AgoraRtcEngineKit.destroy()
    }
    

}

extension LNVideoChatVC: AgoraRtcEngineDelegate {
    
    // 远端用户加入频道
    func rtcEngine(_ engine: AgoraRtcEngineKit, didJoinedOfUid uid: UInt, elapsed: Int) {
        
        let parent: UIView = remoteContainer
        
        if remoteVideo != nil {
            return
        }
        
        let view = UIView(frame: CGRect(origin: CGPoint(x: 0, y: 0), size: parent.frame.size))
        remoteVideo = AgoraRtcVideoCanvas()
        remoteVideo!.view = view
        remoteVideo!.renderMode = .hidden
        remoteVideo!.uid = uid
        parent.addSubview(remoteVideo!.view!)
        LNAgoraManager.shared.rtcEngine.setupRemoteVideo(remoteVideo!)
        // 用户进入 开始第一次扣费
        self.postVideoDeduct(isFirst: true)
    }
    // 远端用户离开频道
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOfflineOfUid uid: UInt, reason: AgoraUserOfflineReason) {
        if let it = remoteVideo, it.uid == uid {
            _ = removeFromParent(it)
            remoteVideo = nil
        }
    }
    
    func rtcEngine(_ engine: AgoraRtcEngineKit, didVideoMuted muted: Bool, byUid: UInt) {
        
    }
    
    // 接收到远端用户
    func rtcEngine(_ engine: AgoraRtcEngineKit, firstRemoteVideoFrameOfUid uid: UInt, size: CGSize, elapsed: Int) {
        
    }
    
    func rtcEngine(_ engine: AgoraRtcEngineKit, connectionChangedTo state: AgoraConnectionState, reason: AgoraConnectionChangedReason) {
        
    }
    
    // rtc状态
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOccurWarning warningCode: AgoraWarningCode) {
        
    }
    
    // 报错
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOccurError errorCode: AgoraErrorCode) {
        
    }
    
}
