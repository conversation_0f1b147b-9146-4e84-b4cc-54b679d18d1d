//
//  LNVideoMessageGiftCell.swift
//  LiveNow
//
//  Created by edy on 2025/8/25.
//

import UIKit

// 礼物消息
class LNVideoMessageGiftCell: UITableViewCell {
    
    var onAvatarTapBlock: ((_ userid: Int) -> Void)?
    
//    private var userModel: UCUserModel?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        initViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func initViews() {
        self.backgroundColor = .clear
        self.selectionStyle = .none
        contentView.addSubview(avatarView)
        contentView.addSubview(nicknameTagsView)
        contentView.addSubview(messageView)
        messageView.addSubview(messageLabel)
    }
    
    @objc func onAvatarTap() {
//        if let user = userModel {
//            onAvatarTapBlock?(user.userid)
//        }
    }
    
//    func setMessageFrameModel(_ msgFrame: UCRCMsgFrameModel) {
//        if let contentModel = msgFrame.msgModel as? UCRCTextMsgModel {
//            let chatBoxMsg = contentModel.chatBoxMsg
//            if let user = chatBoxMsg.user {
//                userModel = user
//                avatarView.uc_setImage(imgStr: user.avatar_url)
//                avatarView.rtl_frame = msgFrame.avatarF
//                avatarView.layer.cornerRadius = avatarView.height * 0.5
//                // 设置昵称标签视图并布局
//                nicknameTagsView.rtl_frame = msgFrame.nicknameTagsF
//                nicknameTagsView.configSubviews(user: user)
//            }
//            // 气泡背景
//            messageView.rtl_frame = msgFrame.textBubbleF
//            messageView.bindDataModel(chatBoxMsg.user?.chat_bubble)
//            var textColorStr = "#FFFFFF"
//            if let chat_bubble = chatBoxMsg.user?.chat_bubble {
//                // 有聊天气泡时
//                textColorStr = chat_bubble.font_color
//            } else {
//                messageView.image = UIImage.image(color: UIColor(hexString: "#000000", alpha: 0.2)!, size: messageView.size, corners: .allCorners, radius: pixw(p: 8))
//            }
//            // 文本内容
//            messageLabel.rtl_frame = msgFrame.textF
//            messageLabel.text = chatBoxMsg.content
//            messageLabel.textColor = UIColor(hexString: textColorStr)
//            // 译文
//            if let translatedTxt = chatBoxMsg.translatedTxt {
//                translatedBubbleView.isHidden = false
//                translatedBubbleView.rtl_frame = msgFrame.translatedTxtBubbleF
//                translatedLabel.rtl_frame = msgFrame.translatedTxtF
//                translatedLabel.attributedText = translatedTxt
//            } else {
//                translatedBubbleView.isHidden = true
//            }
//        }
//    }
    
    // MARK: - Lazy
    lazy var messageView: UIView = {
        let view = UIView(frame: .zero)
        view.isUserInteractionEnabled = true
        return view
    }()
    lazy var avatarView: UIImageView = {
        let view = UIImageView()
        view.layer.masksToBounds = true
        view.contentMode = .scaleAspectFill
        view.isUserInteractionEnabled = true
        let tapGes = UITapGestureRecognizer(target: self, action: #selector(onAvatarTap))
        view.addGestureRecognizer(tapGes)
        return view
    }()
    // 昵称+各种标签
    lazy var nicknameTagsView: UILabel = {
        let view = UILabel()
        return view
    }()
    lazy var messageLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.regular(14)
        label.numberOfLines = 0
        label.textColor = .white
        return label
    }()
}
