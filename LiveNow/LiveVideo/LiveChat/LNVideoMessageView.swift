//
//  LNVideoMessageView.swift
//  LiveNow
//
//  Created by edy on 2025/8/25.
//

import UIKit
import JKSwiftExtension

class LNVideoMessageView: UIView {
    
    var dataArray: [String] = []
    /// 用于存储消息还未刷新到tableView的时候接收到的消息
    private var tempMsgArray: [String] = []
    /// 互斥锁
    private var mutex: pthread_mutex_t = pthread_mutex_t()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .clear
        pthread_mutex_init(&mutex, nil)
        setupSubviews()
        
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        self.tableView.frame = self.bounds
    }
    
    private func setupSubviews() {
        addSubview(tableView)
    }
    
    // MARK: - 消息追加
    func addNewMsgFrameModel(_ msgFrame: String) {
        pthread_mutex_lock(&mutex)
        // 消息不直接加入到数据源
        tempMsgArray.append(msgFrame)
        pthread_mutex_unlock(&mutex)
        
        appendAndScrollToBottom()
    }

    /// 追加数据源
    private func appendAndScrollToBottom() {
        if tempMsgArray.count < 1 {return}
        
        pthread_mutex_lock(&mutex)
        // 执行插入
        var indexPaths: [IndexPath] = []
        for item in tempMsgArray {
            dataArray.append(item)
            let indexPath = IndexPath(row: dataArray.count-1, section: 0)
            indexPaths.append(indexPath)
        }
        tableView.insertRows(at: indexPaths, with: .none)
        tempMsgArray.removeAll()
        
        pthread_mutex_unlock(&mutex)
        
        //执行插入动画并滚动
        scrollToBottom(animated: false)
    }
    
    /// 执行插入动画并滚动
    private func scrollToBottom(animated: Bool) {
        // 有多少组
        let section = tableView.numberOfSections
        if section < 1 {return}
        // 最后一组行
        let row = tableView.numberOfRows(inSection: section-1)
        if row < 1 {return}
        // 取最后一行数据
        let ipIndexPath = IndexPath(row: row-1, section: section-1)
        // 滚动到最后一行
        tableView.scrollToRow(at: ipIndexPath, at: .bottom, animated: animated)
    }
    
    
    // MARK: -
    lazy var tableView: UITableView = {
        let view = UITableView(frame: self.bounds, style: .plain)
//        view.delegate = self
        view.dataSource = self
        view.backgroundColor(.clear)
        view.jk.tableViewNeverAdjustContentInset()
        view.showsVerticalScrollIndicator = false
        view.register(LNVideoMessageGiftCell.self, forCellReuseIdentifier: LNVideoMessageGiftCell.className)
        view.register(LNVideoMessageTextCell.self, forCellReuseIdentifier: LNVideoMessageTextCell.className)
        return view
    }()

}

extension LNVideoMessageView: UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return dataArray.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: LNVideoMessageTextCell = tableView.dequeueReusableCell(withIdentifier: LNVideoMessageTextCell.className, for: indexPath) as! LNVideoMessageTextCell
//        cell.setMessageFrameModel(messageF)
//        cell.onAvatarTapBlock = { [weak self] userid in
//            guard let `self` = self else {return}
//            self.delegate?.roomChatViewOnAvatarTap(userid: userid)
//        }
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 10
//        let message = dataArray[indexPath.row]
//        return message.cellHeight
    }
    
    
}
