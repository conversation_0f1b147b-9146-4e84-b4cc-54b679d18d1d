//
//  LNVideoCallModal.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/12.
//

import UIKit
import SnapKit
import JKSwiftExtension

/// 视频通话弹框模态视图
class LNVideoCallModal: UIView {
    
    // MARK: - UI Elements
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_live_bg")
        return imageView
    }()
    
    private lazy var cardView: UIView = {
        let view = UIView()
        view.backgroundColor(.lightGray).corner(s(30))
        return view
    }()
    
    lazy var botInfoView: UIView = {
        let view = UIView()
        view.layer.insertSublayer(bgLayer, at: 0)
        return view
    }()
    
    lazy var bgLayer: CAGradientLayer = {
        let bgLayer = CAGradientLayer()
        
        bgLayer.colors = [UIColor(hexString: "#000000", alpha: 0)?.cgColor as Any, UIColor(hexString: "#000000", alpha: 0.5)?.cgColor as Any]
        bgLayer.locations = [0, 1]
        bgLayer.frame = CGRect(x: 0, y: 0, width: jk_kScreenW, height: s(82))
        bgLayer.startPoint = CGPoint(x: 0.5, y: 0)
        bgLayer.endPoint = CGPoint(x: 0.5, y: 1)
        return bgLayer
    }()
    
    private lazy var vipBadge: UIView = {
        let view = UIView()
        view.corner(s(13))
        view.layer.insertSublayer(vipLayer, at: 0)
        return view
    }()
    lazy var vipLayer: CAGradientLayer = {
        let bgLayer = CAGradientLayer()
        
        bgLayer.colors = [UIColor(hexString: "#FF9F04").cgColor as Any, UIColor(hexString: "#C9FFF2", alpha: 0)?.cgColor as Any]
        bgLayer.locations = [0, 1]
        bgLayer.frame = CGRect(x: 0, y: 0, width: s(141), height: s(26))
        bgLayer.startPoint = CGPoint(x: 0, y: 0.5)
        bgLayer.endPoint = CGPoint(x: 1, y: 0.5)
        return bgLayer
    }()
    
    private lazy var vipIcon: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "ic_call_alter_vip")
        return view
    }()
    private lazy var vipPriceView: LNPriceView = {
        let view = LNPriceView()
        return view
    }()
    
    private lazy var priceView: LNPriceView = {
        let view = LNPriceView()
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = s(29)
        imageView.backgroundColor = UIColor.systemGray5
        return imageView
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.text = "Name"
        label.font = LNFont.bold(16)
        label.textColor = UIColor.white
        return label
    }()
    
    private lazy var genderView: LNGenderView = {
        let view = LNGenderView()
        return view
    }()
    
    private lazy var countryContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hexString: "#44E3D0")
        view.layer.cornerRadius = s(7)
        return view
    }()
    
    private lazy var countryLabel: UILabel = {
        let label = UILabel()
        label.text = "Indonesia"
        label.font = LNFont.regular(11)
        label.textColor = UIColor.white
        return label
    }()
    
    private lazy var followButton: UIButton = {
        let button = UIButton(type: .custom)
        button.image(UIImage(named: "ic_follow_add")).title("Follow").font(LNFont.regular(14)).textColor(UIColor.white).corner(s(16)).bgImage(UIImage.jk.gradient(["#04E798", "#0ADCE1"], size: CGSize(width: s(88), height: s(32)), direction: .horizontal))
        button.jk.setImageTitleLayout(.imgLeft, spacing: s(4))
        return button
    }()
    
    private lazy var freeBgView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "ic_call_modal_tips")
        return view
    }()
    private lazy var freeLabel: UILabel = {
        let label = UILabel()
        label.text = "Free!"
        label.font = LNFont.regular(14)
        label.textColor = UIColor.white
        return label
    }()
    
    private lazy var hangUpButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "ic_call_modal_hangup"), for: .normal)
        return button
    }()
    
    private lazy var answerButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "ic_call_modal_accept"), for: .normal)
        return button
    }()
    
    private lazy var hangUpLabel: UILabel = {
        let label = UILabel()
        label.text = "Hang Up"
        label.font = LNFont.regular(14)
        label.textColor = UIColor.white
        label.textAlignment = .center
        return label
    }()
    
    private lazy var answerLabel: UILabel = {
        let label = UILabel()
        label.text = "Answer"
        label.font = LNFont.regular(14)
        label.textColor = UIColor.white
        label.textAlignment = .center
        return label
    }()
    
    // MARK: - Properties
    var userInfo: LNUserModel? {
        didSet {
            updateUI()
        }
    }
    
    var onHangUp: (() -> Void)?
    var onAnswer: (() -> Void)?
    var onFollow: (() -> Void)?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupActions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private Methods
    private func setupUI() {
        backgroundColor = UIColor.clear
        
        addSubview(backgroundImageView)
        addSubview(cardView)
        addSubview(freeBgView)
        freeBgView.addSubview(freeLabel)
        addSubview(hangUpButton)
        addSubview(answerButton)
        addSubview(hangUpLabel)
        addSubview(answerLabel)
        
        cardView.addSubview(botInfoView)
        botInfoView.addSubview(avatarImageView)
        botInfoView.addSubview(nameLabel)
        botInfoView.addSubview(genderView)
        botInfoView.addSubview(countryContainer)
        countryContainer.addSubview(countryLabel)
        
        cardView.addSubview(vipBadge)
        vipBadge.addSubview(vipIcon)
        vipBadge.addSubview(vipPriceView)
        cardView.addSubview(priceView)
        cardView.addSubview(followButton)
        
    }
    
    private func setupConstraints() {
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        cardView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(s(-80))
            make.width.equalTo(s(343))
            make.height.equalTo(s(496))
        }
        botInfoView.snp.makeConstraints { make in
            make.leading.bottom.trailing.equalToSuperview()
            make.height.equalTo(s(82))
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.equalTo(s(24))
            make.width.height.equalTo(s(58))
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(s(8))
            make.leading.equalTo(avatarImageView.snp.trailing).offset(s(10))
        }
        genderView.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(s(7))
            make.leading.equalTo(nameLabel.snp.leading)
            make.height.equalTo(s(14))
        }
        countryContainer.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(8)
            make.leading.equalTo(genderView.snp.trailing).offset(s(4))
            make.height.equalTo(s(14))
        }
        
        countryLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 2, left: 8, bottom: 2, right: 8))
        }
        
        followButton.snp.makeConstraints { make in
            make.centerY.equalTo(avatarImageView.snp.centerY)
            make.trailing.equalToSuperview().offset(s(-18))
            make.width.equalTo(s(88))
            make.height.equalTo(s(32))
        }
        
        vipBadge.snp.makeConstraints { make in
            make.bottom.equalTo(priceView.snp.top).offset(s(-8))
            make.trailing.equalToSuperview()
            make.height.equalTo(s(26))
            make.width.equalTo(s(141))
        }
        vipIcon.snp.makeConstraints { make in
            make.leading.equalTo(s(6))
            make.centerY.equalToSuperview()
            make.size.equalTo(s(20))
        }
        vipPriceView.snp.makeConstraints { make in
            make.leading.equalTo(vipIcon.snp.trailing).offset(s(4))
            make.centerY.equalToSuperview()
            make.height.equalTo(s(20))
        }
        
        priceView.snp.makeConstraints { make in
            make.bottom.equalTo(botInfoView.snp.top).offset(s(-50))
            make.trailing.equalTo(s(-8))
            make.height.equalTo(s(19))
        }
        
        freeBgView.snp.makeConstraints { make in
            make.centerX.equalTo(answerButton.snp.centerX)
            make.bottom.equalTo(answerButton.snp.top).offset(s(-2))
            make.size.equalTo(CGSize(width: s(79), height: s(31)))
        }
        freeLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(s(5))
            make.height.equalTo(s(14))
        }
        
        hangUpButton.snp.makeConstraints { make in
            make.top.equalTo(cardView.snp.bottom).offset(s(34))
            make.leading.equalTo(s(75))
            make.width.height.equalTo(s(64))
        }
        
        answerButton.snp.makeConstraints { make in
            make.centerY.equalTo(hangUpButton.snp.centerY)
            make.trailing.equalTo(s(-75))
            make.width.height.equalTo(s(64))
        }
        
        hangUpLabel.snp.makeConstraints { make in
            make.top.equalTo(hangUpButton.snp.bottom).offset(8)
            make.centerX.equalTo(hangUpButton)
        }
        
        answerLabel.snp.makeConstraints { make in
            make.top.equalTo(answerButton.snp.bottom).offset(8)
            make.centerX.equalTo(answerButton)
        }
    }
    
    private func setupActions() {
        hangUpButton.addTarget(self, action: #selector(hangUpTapped), for: .touchUpInside)
        answerButton.addTarget(self, action: #selector(answerTapped), for: .touchUpInside)
        followButton.addTarget(self, action: #selector(followTapped), for: .touchUpInside)
    }
    
    private func updateUI() {
        guard let userInfo = userInfo else { return }
        
        
    }

    
    // MARK: - Actions
    @objc private func hangUpTapped() {
        self.dismiss()
        onHangUp?()
    }
    
    @objc private func answerTapped() {
        
        self.dismiss()
        LNLiveVideoManager.shared.startVideoChat(targetUser: LNUserModel())
        onAnswer?()
    }
    
    @objc private func followTapped() {
        onFollow?()
    }
}

// MARK: - Public Methods
extension LNVideoCallModal {
    func show(with userInfo: LNUserModel) {
        self.userInfo = userInfo
        JKPOP.keyWindow?.addSubview(self)
        
        
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Animation
        alpha = 0
        transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut) {
            self.alpha = 1
            self.transform = .identity
        }
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2) {
            self.alpha = 0
            self.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
}
