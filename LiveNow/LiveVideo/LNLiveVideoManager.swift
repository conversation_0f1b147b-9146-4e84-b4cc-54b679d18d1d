//
//  LNLiveVideoManager.swift
//  LiveNow
//
//  Created by edy on 2025/8/8.
//

import UIKit
import AgoraRtcKit
import AVFAudio
import JKSwiftExtension
import AVFoundation

enum LNLiveRole {
    case inviter    // 邀请方
    case receiver   // 接收方
}
 
enum LNLiveVideoType: String {
    
    case normalVideo = "1"
    case compareVideo = "2"
    case virtueVideo = "3"
}

class LNLiveVideoManager: NSObject {
    /// 单例
    static let shared = LNLiveVideoManager()
    private override init() {}
    /// 防暴力点击
    var limitClick: Bool = true

    // MARK: - 入口
    /*
     1. 先获取渠道channel
     2. 根据渠道获取Token
     3. 加入频道后给主播推送消息
     4. 主播端操作
     */
    
    func startVideoChat(targetUser: LNUserModel, videoType: LNLiveVideoType = .normalVideo) {
        if self.limitClick == false { return }
        self.limitClick = false
        DispatchQueue.main.asyncAfter(deadline: .now()+2) { [weak self] in
            guard let `self` = self else { return }
            self.limitClick = true
        }
        // 检验相机和语音权限是否开启
        self.checkCallVideoAuth { [weak self] in
            guard let `self` = self else { return }
            #warning("这里报错了，我写了默认值")
            self.enterVideoRoom(targetUser: targetUser, videoType: .normalVideo, channelId: "0", rtcToken: "0")
        }
        
    }
    
    // 先获取渠道
    private func getChannel(targetUser: LNUserModel, videoType: LNLiveVideoType = .normalVideo) {
        let dic = [
            "userId": targetUser.id,
            "userRole": "1",
            "freeFlag": "0",
            "sourceType": videoType.rawValue
        ] as [String : Any]
        NetWorkRequest(LNApiVideo.genVideoChannel(par: dic)) { result in
            
        } failure: { error in
            
        }
    }
    
    // 根据渠道号获取token
    private func getRTCToken(targetUser: LNUserModel, videoType: LNLiveVideoType = .normalVideo, channelId: String) {
        let dic = [
            "channelId": channelId,
            "userCode": targetUser.userCode
        ]
        
        NetWorkRequest(LNApiVideo.getVideoRtcToken(par: dic)) { result in
            
            // 进入视频房间
            self.enterVideoRoom(targetUser: targetUser, videoType: videoType, channelId: channelId, rtcToken: "")
            
        } failure: { error in
            
        }

        
    }
    
    
    
    // 进入视频房间
    private func enterVideoRoom(targetUser: LNUserModel, videoType: LNLiveVideoType = .normalVideo, channelId: String, rtcToken: String) {
        guard let navigationVC = UIViewController.jk.topViewController()?.navigationController else {return}
        // 常规方式进入
        // 进入视频通话页面
        let videoChatVC = LNVideoChatVC(role: .inviter, targetUser: targetUser)
        videoChatVC.channel = channelId
        videoChatVC.rtcToken = rtcToken
        videoChatVC.videoType = videoType
        navigationVC.pushViewController(videoChatVC, animated: true)
        
    }
    
    
    // MARK: - 检查权限
    func checkCallVideoAuth(complete: (() -> Void)?) {
        let alter = LNPermisssionAlter()
        alter.sureBlock = {
            complete?()
        }
        let videoStatus = AVCaptureDevice.authorizationStatus(for: .video)
        let microStatus = AVAudioSession.sharedInstance().recordPermission
        if videoStatus == .authorized && microStatus == .granted {
            // 权限开启
            complete?()
        } else {
            alter.showAlert()
        }
    }
    
//    var sponsorPlayer: AVAudioPlayer? = {
//        let soundURL = Bundle.main.url(forResource: "video_chat_bell", withExtension: "mp3")
//        
//        do {
//            let sponsorPlayer = try AVAudioPlayer(contentsOf: soundURL!)
//            sponsorPlayer.numberOfLoops = LONG_MAX
//            return sponsorPlayer
//        } catch {
//            Log(error)
//        }
//        return nil
//    }()
//    var receiverPlayer: AVAudioPlayer? = {
//        let soundURL = Bundle.main.url(forResource: "video_chat_bell", withExtension: "mp3")
//        
//        do {
//            let receiverPlayer = try AVAudioPlayer(contentsOf: soundURL!)
//            receiverPlayer.numberOfLoops = LONG_MAX
//            return receiverPlayer
//        } catch {
//            Log(error)
//        }
//        return nil
//    }()
//    // 电话结束音
//    var overPlayer: AVAudioPlayer? = {
//        let soundURL = Bundle.main.url(forResource: "robot_call_timeout", withExtension: "mp3")
//        
//        do {
//            let overPlayer = try AVAudioPlayer(contentsOf: soundURL!)
//            overPlayer.numberOfLoops = 0
//            return overPlayer
//        } catch {
//            Log(error)
//        }
//        return nil
//    }()
}

