//
//  LNAgoraManager.swift
//  LiveNow
//
//  Created by edy on 2025/8/20.
//

import UIKit
import AgoraRtcKit

class LNAgoraManager: NSObject {
    
    static let shared = LNAgoraManager()
//    public var beautyAPI: ?
    public var rtcEngine: AgoraRtcEngineKit!
    
    // MARK: - 初始化声网RTC服务
    func initRtcEngine() {
        let config = AgoraRtcEngineConfig()
        config.appId = "AgoraAppId"
        config.channelProfile = .liveBroadcasting
        config.audioScenario = .gameStreaming
        config.areaCode = .global
        rtcEngine = AgoraRtcEngineKit.sharedEngine(with: config, delegate: nil)
        rtcEngine.setClientRole(.broadcaster)

        // 设置视频encode编码
        let videoConfig = AgoraVideoEncoderConfiguration(size: AgoraVideoDimension480x360, frameRate: .fps15, bitrate: AgoraVideoBitrateStandard, orientationMode: .adaptative, mirrorMode: AgoraVideoMirrorMode.disabled)

        // 美颜初始化
        // 设置基础美颜
        let bt = AgoraBeautyOptions()
        bt.lighteningContrastLevel = .normal
        bt.lighteningLevel = 1.0
        bt.smoothnessLevel = 1.0
        bt.rednessLevel = 0.1
        rtcEngine.setBeautyEffectOptions(true, options: bt)

    }

}
