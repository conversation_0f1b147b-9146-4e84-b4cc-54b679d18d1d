# 会员中心页面 (Membership Center)

## 概述
根据提供的UI设计图，创建了一个完整的会员中心页面，包含VIP等级选择、购买选项和权益展示。

## 文件结构

### 主要文件
- `LNMembershipCenterViewController.swift` - 会员中心主页面
- `LNMembershipDemoViewController.swift` - 演示页面（可选）

### 修改的文件
- `LNProfileViewController.swift` - 个人资料页面，添加了会员中心入口

## 功能特性

### 1. VIP等级卡片
- **Bronze VIP**: +600钻石，US$12.79
- **Silver VIP**: +1100钻石，原价US$24.79，现价US$9.99
- **Gold VIP**: +2200钻石，US$19.99
- 支持选中状态显示（Bronze VIP默认选中）
- 使用项目现有的钻石图标

### 2. 购买选项
- **Restore Purchase** 按钮 - 恢复购买功能
- **US$4.99 For week** 按钮 - 周费用订阅，使用主题渐变色

### 3. VIP权益展示
- 6个VIP权益项目
- 每个权益包含图标、标题和描述
- 使用项目现有的VIP星星图标

### 4. 底部操作
- **Check who liked me** 按钮 - 查看谁喜欢我，使用主题渐变色

## 设计规范

### 颜色方案
- 主题渐变色：`#04E798` -> `#0ADCE1`
- Bronze VIP背景：`#FFE066`
- 其他VIP背景：`#E5E5E5`
- 文本颜色：黑色、灰色系

### 布局规范
- 使用SnapKit进行约束布局
- 所有尺寸使用`s()`函数进行屏幕适配
- 遵循项目的设计规范和间距标准

### 字体
- 使用项目统一的`LNFont`字体系统
- 标题使用bold字重
- 正文使用regular和medium字重

## 集成方式

### 1. 从个人资料页面进入
在个人资料页面中有两种方式进入会员中心：
- 点击VIP统计卡片
- 点击菜单中的"Membership Center"项目

### 2. 直接跳转
```swift
let membershipVC = LNMembershipCenterViewController()
membershipVC.hidesBottomBarWhenPushed = true
navigationController?.pushViewController(membershipVC, animated: true)
```

## 技术实现

### 继承结构
- 继承自`LNBaseController`
- 支持导航栏自动配置
- 支持深浅色模式

### 核心组件
- `UIScrollView` + `UIStackView` 布局
- 自定义VIP卡片视图
- 渐变按钮实现
- 权益列表动态生成

### 响应式设计
- 支持不同屏幕尺寸
- 使用屏幕适配函数`s()`
- 自动布局约束

## 待实现功能

### 业务逻辑
- [ ] VIP等级选择逻辑
- [ ] 购买流程集成
- [ ] 恢复购买功能
- [ ] 权益状态管理

### 数据集成
- [ ] 用户VIP状态获取
- [ ] 价格信息动态加载
- [ ] 权益列表配置化

### 交互优化
- [ ] 卡片选择动画
- [ ] 购买成功反馈
- [ ] 错误处理提示

## 使用说明

1. 确保项目中已包含所需的图标资源
2. 导入会员中心页面文件
3. 在需要的地方添加跳转逻辑
4. 根据实际业务需求实现购买逻辑

## 测试验证

项目包含了完整的测试文件 `LNMembershipCenterTests.swift`，可以运行以下代码进行测试：

```swift
// 运行所有测试
LNMembershipCenterTests.runAllTests()

// 查看集成示例
LNMembershipCenterUsageExample.showIntegrationExample()

// 测试页面性能
LNMembershipCenterPerformanceTests.testLoadingPerformance()
```

## 页面预览

会员中心页面包含以下主要区域：

```
┌─────────────────────────────────┐
│        Membership center        │ ← 导航栏
├─────────────────────────────────┤
│  [Bronze] [Silver]  [Gold]      │ ← VIP等级卡片
│   +600     +1100    +2200       │
│  $12.79    $9.99    $19.99      │
├─────────────────────────────────┤
│      Restore Purchase           │ ← 恢复购买按钮
├─────────────────────────────────┤
│     US$4.99 For week           │ ← 周费用按钮
├─────────────────────────────────┤
│ ⭐ VIP 权益一                    │ ← VIP权益列表
│ ⭐ VIP 权益二                    │
│ ⭐ VIP 权益三                    │
│ ⭐ VIP 权益四                    │
│ ⭐ VIP 权益五                    │
│ ⭐ VIP 权益六                    │
├─────────────────────────────────┤
│    Check who liked me          │ ← 底部操作按钮
└─────────────────────────────────┘
```

## 注意事项

- 所有价格和权益信息目前为静态数据，需要根据实际业务进行动态配置
- 购买相关功能需要集成应用内购买(In-App Purchase)
- 建议添加网络请求错误处理和加载状态
- 页面已适配深浅色模式和不同屏幕尺寸
