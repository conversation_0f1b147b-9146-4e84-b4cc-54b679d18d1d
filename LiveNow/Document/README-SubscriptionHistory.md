# Subscription History 页面说明

文件：LiveNow/Setting/LNSubscriptionHistoryViewController.swift

## 功能特性

- **列表样式**：卡片化 cell，左边显示VIP类型，右边价格；第二行时间+状态（红/绿）
- **入口**：我的（LNProfileViewController）菜单项 "Subscription History"
- **底部提示**：No More Data
- **适配**：iOS 13+、深色模式、动态字体
- **下拉刷新**：支持 MJRefresh 下拉刷新和上拉加载更多

## 页面结构

```
┌─────────────────────────────────┐
│        Subscription History     │ ← 导航栏标题
├─────────────────────────────────┤
│  ┌─────────────────────────────┐ │
│  │ Bronze VIP        US$4.99   │ │ ← 卡片式列表项
│  │ 2025-01-02 16:25:45 Unfinished │
│  └─────────────────────────────┘ │
│  ┌─────────────────────────────┐ │
│  │ Bronze VIP        US$4.99   │ │
│  │ 2025-01-02 16:25:45 Unfinished │
│  └─────────────────────────────┘ │
│  ┌─────────────────────────────┐ │
│  │ Bronze VIP        US$4.99   │ │
│  │ 2025-01-02 16:25:45 Finished │
│  └─────────────────────────────┘ │
│                                 │
│         No More Data            │ ← 底部提示
└─────────────────────────────────┘
```

## 技术实现

### 继承结构

```swift
class LNSubscriptionHistoryViewController: LNBaseController {
    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }
    
    // 使用顶部渐变背景
    override var useTopGradientBackground: Bool { return true }
}
```

### 数据模型

```swift
struct Record { 
    let vipType: String
    let priceUSD: String
    let time: String
    let status: Status 
}

enum Status { 
    case finished, unfinished 
}
```

### Cell 设计

- **LNSubscriptionHistoryCell**：自定义 UITableViewCell
- **布局**：使用 SnapKit 进行约束布局
- **适配**：所有尺寸使用 `s()` 函数进行屏幕适配
- **状态颜色**：
  - Finished: 绿色 (UIColor.systemGreen)
  - Unfinished: 红色 (UIColor.systemRed)

## 使用方法

### 1. 从个人页面访问
```swift
// 在 LNProfileViewController 中已集成
// 点击 "Subscription History" 菜单项即可跳转
```

### 2. 直接创建
```swift
let subscriptionHistoryVC = LNSubscriptionHistoryViewController()
subscriptionHistoryVC.hidesBottomBarWhenPushed = true
navigationController?.pushViewController(subscriptionHistoryVC, animated: true)
```

## 功能特点

### 1. 分页加载
- 支持下拉刷新
- 支持上拉加载更多
- 模拟网络请求延迟
- 自动处理加载状态

### 2. 数据展示
- VIP类型：Bronze VIP, Silver VIP, Gold VIP
- 价格：US$4.99, US$9.99, US$19.99
- 时间：2025-01-02 16:25:45 格式
- 状态：Finished（绿色）/ Unfinished（红色）

### 3. UI 特性
- 卡片式设计，圆角 12pt
- 透明导航栏配合顶部渐变背景
- 系统背景色适配深色模式
- 使用项目统一的 LNFont 字体

## 扩展建议

后续可以扩展的功能：
- 从后端分页拉取订阅订单列表
- 支付状态实时刷新（轮询/推送）
- 点击某条进入订单详情/申诉
- 添加筛选功能（按状态、时间等）
- 支持搜索功能

## 依赖项

- **SnapKit**：用于自动布局
- **MJRefresh**：用于下拉刷新和上拉加载
- **LNBaseController**：项目基础控制器
- **LNFont**：项目字体管理系统

## 文件结构

```
LiveNow/Setting/
├── LNSubscriptionHistoryViewController.swift  # 主控制器
└── LNSubscriptionHistoryCell.swift           # 自定义 Cell
```

## 测试验证

1. 运行项目
2. 进入个人页面（Profile）
3. 点击 "Subscription History" 菜单项
4. 验证页面显示和交互功能
5. 测试下拉刷新和上拉加载更多功能
