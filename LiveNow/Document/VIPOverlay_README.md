# VIP蒙层组件使用指南

## 概述

VIP蒙层组件是一个通用的UI组件，用于在用户不是VIP时显示蒙层，提示用户升级VIP以解锁功能。该组件包含钻石图标、提示文字和升级按钮，支持自定义内容和动画效果。

## 组件结构

### 1. LNVIPOverlayView
- **位置**: `LiveNow/CustomUI/LNVIPOverlayView.swift`
- **功能**: VIP蒙层视图组件
- **特性**:
  - 半透明背景遮罩
  - 钻石图标（使用 `ic_bg_zs` 图片）
  - 可自定义的标题和副标题
  - 升级VIP按钮
  - 显示/隐藏动画效果

### 2. LNVIPManager
- **位置**: `LiveNow/CustomUI/LNVIPManager.swift`
- **功能**: VIP状态管理和权限验证
- **特性**:
  - VIP状态检查
  - 权限验证
  - 自动显示升级蒙层
  - 便捷使用方法

## 使用方法

### 基础使用

#### 1. 直接使用LNVIPOverlayView

```swift
// 创建默认的"谁喜欢我"蒙层
let overlay = LNVIPOverlayView.createWhoLikedMeOverlay()

// 设置升级回调
overlay.onUpgradeVIP = {
    print("用户点击了升级VIP")
    // 处理升级逻辑
}

// 添加到视图
view.addSubview(overlay)
overlay.snp.makeConstraints { make in
    make.edges.equalToSuperview()
}

// 显示蒙层
overlay.show(animated: true)
```

#### 2. 创建自定义蒙层

```swift
let overlay = LNVIPOverlayView.createCustomOverlay(
    title: "VIP功能",
    subtitle: "升级VIP解锁更多功能",
    buttonTitle: "立即升级"
)

overlay.onUpgradeVIP = {
    // 处理升级逻辑
}

view.addSubview(overlay)
overlay.snp.makeConstraints { make in
    make.edges.equalToSuperview()
}

overlay.show(animated: true)
```

### 推荐使用 - LNVIPManager

#### 1. 检查VIP状态

```swift
// 检查当前用户是否为VIP
let isVIP = LNVIPManager.shared.isCurrentUserVIP()

// 获取VIP状态描述
let statusDescription = LNVIPManager.shared.getVIPStatusDescription()
print("VIP状态: \(statusDescription)")
```

#### 2. 权限验证（推荐）

```swift
// 验证VIP权限，如果不是VIP会自动显示升级蒙层
let hasPermission = LNVIPManager.shared.requireVIPPermission(in: view) {
    print("用户点击了升级按钮")
}

if hasPermission {
    // 用户是VIP，执行相关功能
    print("用户是VIP，可以使用功能")
} else {
    // 用户不是VIP，已显示升级蒙层
    print("用户不是VIP，已显示升级提示")
}
```

#### 3. 特定功能的权限验证

```swift
// 为"谁喜欢我"功能验证权限
LNVIPManager.shared.requireVIPForWhoLikedMe(in: view) {
    print("用户想要升级VIP查看谁喜欢我")
}

// 为聊天功能验证权限
LNVIPManager.shared.requireVIPForChat(in: view) {
    print("用户想要升级VIP使用聊天功能")
}

// 为视频通话功能验证权限
LNVIPManager.shared.requireVIPForVideoCall(in: view) {
    print("用户想要升级VIP使用视频通话")
}
```

#### 4. 自定义权限验证

```swift
LNVIPManager.shared.requireVIPPermission(
    in: view,
    title: "高级功能",
    subtitle: "升级VIP解锁高级功能，享受更好的体验",
    buttonTitle: "立即升级"
) {
    print("用户点击了升级按钮")
    // 可以在这里添加自定义逻辑，比如统计、跳转等
}
```

## 实际应用示例

### 在"谁喜欢我"页面中使用

```swift
class LNWhoLikedMeViewController: LNBaseController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        checkVIPPermission()
    }
    
    private func checkVIPPermission() {
        // 验证VIP权限
        let hasPermission = LNVIPManager.shared.requireVIPForWhoLikedMe(in: view) {
            // 用户点击升级按钮的回调
            print("用户想要升级VIP查看谁喜欢我")
        }
        
        if hasPermission {
            // 用户是VIP，加载数据
            loadData()
        }
        // 如果不是VIP，蒙层已自动显示，不需要额外处理
    }
    
    private func loadData() {
        // 加载"谁喜欢我"的数据
    }
}
```

### 在聊天页面中使用

```swift
class LNChatViewController: LNBaseController {
    
    @objc private func sendMessageButtonTapped() {
        // 发送消息前验证VIP权限
        let hasPermission = LNVIPManager.shared.requireVIPForChat(in: view) {
            print("用户想要升级VIP使用聊天功能")
        }
        
        if hasPermission {
            // 用户是VIP，发送消息
            sendMessage()
        }
    }
    
    private func sendMessage() {
        // 发送消息逻辑
    }
}
```

## 自定义配置

### 修改蒙层样式

如果需要修改蒙层的样式，可以直接编辑 `LNVIPOverlayView.swift` 文件：

- 修改背景透明度：调整 `backgroundMaskView` 的 `backgroundColor`
- 修改钻石图标：更换 `diamondImageView` 的图片
- 修改文字样式：调整 `titleLabel` 和 `subtitleLabel` 的字体和颜色
- 修改按钮样式：调整 `upgradeButton` 的背景色和圆角

### 添加新的便捷方法

在 `LNVIPManager` 的扩展中添加新的便捷方法：

```swift
extension LNVIPManager {
    
    /// 为新功能验证VIP权限
    @discardableResult
    func requireVIPForNewFeature(in parentView: UIView, onUpgrade: (() -> Void)? = nil) -> Bool {
        return requireVIPPermission(
            in: parentView,
            title: "新功能标题",
            subtitle: "新功能描述",
            buttonTitle: "升级VIP",
            onUpgrade: onUpgrade
        )
    }
}
```

## 注意事项

1. **图片资源**: 确保 `ic_bg_zs` 图片资源存在于项目中
2. **VIP状态**: VIP状态基于 `LNUserModel` 的 `vipExpireFlag` 和 `vipExpireDay` 字段
3. **内存管理**: 蒙层会自动管理生命周期，无需手动释放
4. **动画效果**: 显示和隐藏都有平滑的动画效果
5. **重复显示**: 系统会自动移除已存在的蒙层，避免重复显示

## 技术实现

- **UI框架**: UIKit + SnapKit
- **动画**: Core Animation
- **设计模式**: 单例模式 + 工厂模式
- **内存管理**: ARC自动管理
- **兼容性**: iOS 13+

---

*该组件遵循项目的LN命名规范和设计风格，可以在任何需要VIP权限验证的地方使用。*
