# LNImageLoader - 统一图片加载工具

## 概述

LNImageLoader 是基于 Kingfisher 封装的统一图片加载工具，为 LiveNow 项目提供了简单易用的远程图片加载接口。

## 特性

- ✅ **统一接口**: 项目中所有远程图片加载使用统一的API
- ✅ **多种类型**: 支持头像、缩略图、封面图等不同类型的图片加载
- ✅ **自动优化**: 根据图片类型自动应用合适的处理选项
- ✅ **缓存管理**: 完善的内存和磁盘缓存管理
- ✅ **占位图支持**: 支持自定义占位图
- ✅ **错误处理**: 完整的加载状态回调
- ✅ **预加载**: 支持批量预加载图片
- ✅ **便捷扩展**: UIImageView 扩展方法，使用更简单

## 快速开始

### 1. 基本使用

```swift
// 加载普通图片
imageView.ln_setImage(url: "https://example.com/image.jpg")

// 加载头像（自动圆角处理）
avatarImageView.ln_setAvatar(url: "https://example.com/avatar.jpg")

// 加载缩略图（压缩处理）
thumbnailImageView.ln_setThumbnail(url: "https://example.com/thumb.jpg")

// 加载封面图（高质量处理）
coverImageView.ln_setCover(url: "https://example.com/cover.jpg")
```

### 2. 带回调的加载

```swift
LNImageLoader.loadImage(imageView, url: imageURL) { result in
    switch result {
    case .success(let value):
        print("图片加载成功: \(value.source)")
    case .failure(let error):
        print("图片加载失败: \(error.localizedDescription)")
    }
}
```

### 3. 自定义占位图

```swift
let customPlaceholder = UIImage(named: "custom_placeholder")
LNImageLoader.loadImage(imageView, url: imageURL, placeholder: customPlaceholder)
```

## API 参考

### 主要方法

#### 静态方法

```swift
// 通用图片加载
LNImageLoader.loadImage(_:url:placeholder:completion:)

// 头像加载（圆角处理）
LNImageLoader.loadAvatar(_:url:placeholder:completion:)

// 缩略图加载（压缩处理）
LNImageLoader.loadThumbnail(_:url:placeholder:completion:)

// 封面图加载（高质量处理）
LNImageLoader.loadCover(_:url:placeholder:completion:)
```

#### UIImageView 扩展方法

```swift
// 便捷方法
imageView.ln_setImage(url:placeholder:)
imageView.ln_setAvatar(url:placeholder:)
imageView.ln_setThumbnail(url:placeholder:)
imageView.ln_setCover(url:placeholder:)
```

### 缓存管理

```swift
// 清除所有缓存
LNImageLoader.clearCache()

// 清除内存缓存
LNImageLoader.clearMemoryCache()

// 获取缓存大小
LNImageLoader.getCacheSize { size in
    print("缓存大小: \(size) bytes")
}

// 预加载图片
LNImageLoader.preloadImages(["url1", "url2", "url3"])
```

## 图片类型说明

### 1. 普通图片 (loadImage)
- **用途**: 一般的图片显示
- **处理**: 标准压缩，淡入动画
- **缓存**: PNG格式缓存
- **尺寸**: 最大300x300

### 2. 头像图片 (loadAvatar)
- **用途**: 用户头像显示
- **处理**: 自动圆角处理，淡入动画
- **缓存**: PNG格式缓存
- **特点**: 8px圆角

### 3. 缩略图 (loadThumbnail)
- **用途**: 列表中的小图显示
- **处理**: 压缩处理，快速加载
- **缓存**: JPEG格式缓存（80%质量）
- **尺寸**: 最大150x150

### 4. 封面图 (loadCover)
- **用途**: 封面、横幅等大图显示
- **处理**: 高质量处理
- **缓存**: JPEG格式缓存（90%质量）
- **尺寸**: 最大400x300

## 在项目中的使用

### 1. 主播列表Cell

```swift
func configure(with model: LNAnchorModel) {
    // 设置头像 - 支持远程和本地图片
    if model.headFileName.hasPrefix("http") {
        avatarImageView.ln_setAvatar(url: model.headFileName)
    } else {
        avatarImageView.image = UIImage(named: model.avatarName)
    }
    
    // 设置封面图
    if !model.coverVideoUrl.isEmpty {
        coverImageView.ln_setCover(url: model.coverVideoUrl)
    }
}
```

### 2. 个人资料页面

```swift
class ProfileViewController: UIViewController {
    @IBOutlet weak var avatarImageView: UIImageView!
    @IBOutlet weak var backgroundImageView: UIImageView!
    
    func updateUI(with user: LNUserModel) {
        // 加载头像
        avatarImageView.ln_setAvatar(url: user.headFileName)
        
        // 加载背景图
        backgroundImageView.ln_setCover(url: user.backgroundImageURL)
    }
}
```

### 3. 批量预加载

```swift
class HomeViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 预加载热门主播的头像
        let avatarURLs = hotAnchors.map { $0.headFileName }
        LNImageLoader.preloadImages(avatarURLs)
    }
}
```

## 配置说明

### 全局配置（AppDelegate）

```swift
private func setupKingfisher() {
    let cache = ImageCache.default
    
    // 内存缓存配置 - 100MB
    cache.memoryStorage.config.totalCostLimit = 100 * 1024 * 1024
    
    // 磁盘缓存配置 - 500MB，7天过期
    cache.diskStorage.config.sizeLimit = 500 * 1024 * 1024
    cache.diskStorage.config.expiration = .days(7)
    
    // 网络配置 - 30秒超时
    KingfisherManager.shared.downloader.downloadTimeout = 30.0
}
```

### 默认占位图

- **普通图片**: `default_avatar`
- **头像**: `default_avatar`
- **缩略图**: `default_thumbnail`
- **封面图**: `default_cover`

## 最佳实践

### 1. 选择合适的加载方法
- 用户头像 → `ln_setAvatar()`
- 列表缩略图 → `ln_setThumbnail()`
- 封面横幅 → `ln_setCover()`
- 其他图片 → `ln_setImage()`

### 2. 占位图设计
- 占位图尺寸应与最终图片相近
- 使用统一的占位图风格
- 考虑深色模式适配

### 3. 性能优化
- 合理使用预加载功能
- 在内存警告时清除内存缓存
- 定期清理磁盘缓存

### 4. 错误处理
- 对重要图片使用回调处理
- 提供合适的fallback机制
- 记录加载失败的日志

## 注意事项

1. **URL检查**: 工具会自动检查URL有效性
2. **本地图片**: 对于本地图片，仍使用 `UIImage(named:)` 方式
3. **内存管理**: 自动处理图片内存管理，无需手动释放
4. **线程安全**: 所有方法都是线程安全的
5. **缓存策略**: 默认缓存原图，根据需要进行处理

## 故障排除

### 常见问题

1. **图片不显示**
   - 检查URL是否有效
   - 确认网络连接
   - 查看控制台日志

2. **加载缓慢**
   - 检查图片大小
   - 考虑使用缩略图
   - 启用预加载

3. **内存占用高**
   - 调整缓存配置
   - 及时清理缓存
   - 使用合适的图片尺寸

## 更新日志

### v1.0.0 (2025/8/23)
- ✅ 初始版本发布
- ✅ 支持多种图片类型加载
- ✅ 完整的缓存管理
- ✅ UIImageView便捷扩展
- ✅ 全局配置支持
