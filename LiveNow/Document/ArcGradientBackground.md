# 圆弧形渐变背景使用指南

## 概述

在 `LNBaseController` 中已经集成了圆弧形渐变背景功能，子类可以通过简单的配置来启用这个美观的背景效果。

## 功能特点

- 🎨 美观的圆弧形渐变背景
- 🔧 可配置的渐变颜色
- 📏 可调整的背景高度
- 🎯 选择性启用，不影响其他页面

## 使用方法

### 1. 基本启用

在你的 ViewController 中，只需要重写 `useArcGradientBackground` 属性：

```swift
class YourViewController: LNBaseController {
    
    // 启用圆弧形渐变背景
    override var useArcGradientBackground: Bool { return true }
    
}
```

### 2. 自定义渐变颜色

如果需要使用不同的渐变颜色，可以重写 `arcGradientColors` 属性：

```swift
class YourViewController: LNBaseController {
    
    // 启用圆弧形渐变背景
    override var useArcGradientBackground: Bool { return true }
    
    // 自定义渐变颜色
    override var arcGradientColors: [UIColor] {
        return [
            UIColor.hex(hexString: "#FF6B6B"),  // 起始颜色
            UIColor.hex(hexString: "#4ECDC4")   // 结束颜色
        ]
    }
    
}
```

### 3. 自定义背景高度

如果需要调整背景高度，可以重写 `arcGradientBackgroundHeight` 属性：

```swift
class YourViewController: LNBaseController {
    
    // 启用圆弧形渐变背景
    override var useArcGradientBackground: Bool { return true }
    
    // 自定义背景高度
    override var arcGradientBackgroundHeight: CGFloat { return s(320) }
    
}
```

## 默认配置

- **默认状态**: 关闭 (`useArcGradientBackground = false`)
- **默认颜色**: `#B8FFE6` → `#E2FFD9` (浅绿色渐变)
- **默认高度**: `s(280)` (适配屏幕尺寸)

## 已启用的页面

以下页面已经启用了圆弧形渐变背景：

1. `LNProfileViewController` - 个人资料页面
2. `LNRewardTasksViewController` - 奖励任务页面  
3. `LNPersonalDataViewController` - 个人信息编辑页面
4. `LNCustomerServiceViewController` - 客服中心页面

## 注意事项

1. 圆弧形背景会自动添加到视图的最底层
2. 背景使用 CAGradientLayer 和 CAShapeLayer 实现，性能良好
3. 背景会在 `viewDidLayoutSubviews` 中自动更新尺寸
4. 与导航栏渐变背景可以同时使用，互不冲突

## 技术实现

圆弧形背景通过以下技术实现：

- `CAGradientLayer`: 创建渐变效果
- `CAShapeLayer`: 创建圆弧形遮罩
- `UIBezierPath`: 定义圆弧路径
- `SnapKit`: 自动布局约束

背景路径使用贝塞尔曲线创建，形成自然的圆弧过渡效果。
