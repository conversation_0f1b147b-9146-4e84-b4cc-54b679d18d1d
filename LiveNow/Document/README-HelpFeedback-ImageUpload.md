# 意见反馈页面图片上传功能实现

## 概述

本文档描述了在 `LNHelpFeedbackViewController` 中实现的图片上传和反馈提交功能。用户可以选择图片，系统会先上传图片获取URL，然后调用反馈接口提交完整的反馈信息。

## 功能特性

### ✅ 已实现功能

1. **图片选择和预览**
   - 支持从相册选择图片
   - 图片预览功能
   - 支持替换和删除已选择的图片

2. **图片上传**
   - 使用 `LNImageUploadManager` 进行图片上传
   - 支持图片压缩（质量0.7）
   - 上传进度处理
   - 错误处理和重试机制

3. **反馈提交**
   - 构建完整的API参数
   - 调用 `LNApiProfile.userFeedback` 接口
   - 支持带图片和不带图片的反馈提交

4. **用户体验优化**
   - 加载状态指示器
   - 防止重复提交
   - 详细的错误提示
   - 成功提交后自动清空表单

## 技术实现

### 核心流程

```
用户点击提交 → 验证输入 → 检查登录状态 → 上传图片(如有) → 提交反馈 → 显示结果
```

### 关键方法

#### 1. 主提交方法
```swift
@objc private func submitTapped() {
    // 防止重复提交
    guard !isSubmitting else { return }
    
    // 验证输入内容
    let text = textView.text.trimmingCharacters(in: .whitespacesAndNewlines)
    guard !text.isEmpty else {
        showAlert(title: "提示", message: "请输入反馈内容。")
        return
    }
    
    // 检查用户登录状态
    guard let user = LNUserManager.shared.userModel else {
        showAlert(title: "提示", message: "请先登录后再提交反馈。")
        return
    }
    
    isSubmitting = true
    showLoadingIndicator()
    
    // 根据是否有图片选择不同的处理流程
    if let image = selectedImage {
        uploadImageAndSubmitFeedback(image: image, content: text, user: user)
    } else {
        submitFeedback(imageUrls: [], content: text, user: user)
    }
}
```

#### 2. 图片上传方法
```swift
private func uploadImageAndSubmitFeedback(image: UIImage, content: String, user: LNUserModel) {
    LNImageUploadManager.shared.uploadImage(
        image,
        compressionQuality: 0.7,
        progress: nil,
        success: { [weak self] imageUrl in
            guard let self = self else { return }
            Log("图片上传成功，URL: \(imageUrl)")
            self.submitFeedback(imageUrls: [imageUrl], content: content, user: user)
        },
        failure: { [weak self] error in
            guard let self = self else { return }
            Log("图片上传失败: \(error.localizedDescription)")
            self.hideLoadingIndicator()
            self.isSubmitting = false
            self.showAlert(title: "上传失败", message: "图片上传失败，请重试。\n错误信息：\(error.localizedDescription)")
        }
    )
}
```

#### 3. 反馈提交方法
```swift
private func submitFeedback(imageUrls: [String], content: String, user: LNUserModel) {
    // 构建反馈接口参数
    let parameters: [String: Any] = [
        "fileUrlList": imageUrls,
        "reportContent": content,
        "reportUserHeadFileName": user.headFileName,
        "reportUserId": user.id,
        "reportUserNickName": user.nickName,
        "reportUserRole": "\(user.userRole)",
        "userId": user.id
    ]
    
    NetWorkRequest(
        LNApiProfile.userFeedback(par: parameters),
        completion: { [weak self] result in
            // 处理成功响应
        },
        failure: { [weak self] error in
            // 处理失败响应
        }
    )
}
```

### API 参数说明

根据提供的接口参数示例，我们构建的参数包括：

```json
{
  "fileUrlList": ["图片URL数组"],
  "reportContent": "反馈内容",
  "reportUserHeadFileName": "用户头像文件名",
  "reportUserId": "用户ID",
  "reportUserNickName": "用户昵称", 
  "reportUserRole": "用户角色",
  "userId": "用户ID"
}
```

## 依赖组件

### 1. LNImageUploadManager
- 负责图片上传功能
- 支持图片压缩和进度回调
- 提供错误处理机制

### 2. LNApiProfile.userFeedback
- 用户反馈API接口
- POST请求方式
- 接受完整的反馈参数

### 3. LNUserManager
- 用户信息管理
- 提供当前登录用户信息
- 包含用户ID、昵称、头像等信息

## 错误处理

### 1. 输入验证
- 检查反馈内容是否为空
- 提示用户输入必要信息

### 2. 登录状态检查
- 验证用户是否已登录
- 未登录时提示用户先登录

### 3. 图片上传错误
- 网络错误处理
- 服务器错误处理
- 图片压缩失败处理

### 4. 反馈提交错误
- API调用失败处理
- 服务器响应错误处理
- 网络超时处理

## 用户体验优化

### 1. 加载状态
- 提交时显示"提交中..."
- 禁用提交按钮防止重复提交
- 按钮透明度变化提供视觉反馈

### 2. 成功反馈
- 显示成功提示信息
- 自动清空表单内容
- 重置图片选择状态

### 3. 错误提示
- 详细的错误信息显示
- 用户友好的提示文案
- 提供重试建议

## 测试

### 测试文件
- `LNHelpFeedbackViewControllerTests.swift`

### 测试覆盖
- 控制器创建和初始化
- UI元素存在性验证
- 图片选择功能测试
- 网络请求参数构建测试
- 内存管理测试

### 运行测试
```swift
let feedbackVC = LNHelpFeedbackViewController()
feedbackVC.runFeedbackTests()
```

## 使用方法

### 1. 基本使用
```swift
let feedbackVC = LNHelpFeedbackViewController()
navigationController?.pushViewController(feedbackVC, animated: true)
```

### 2. 用户操作流程
1. 用户输入反馈内容
2. 可选择添加图片
3. 点击提交按钮
4. 系统自动处理图片上传和反馈提交
5. 显示提交结果

## 注意事项

### 1. 权限要求
- 需要在 Info.plist 中添加 `NSPhotoLibraryUsageDescription`
- 确保用户已授权访问相册

### 2. 网络要求
- 需要网络连接进行图片上传和反馈提交
- 建议在网络不佳时提供相应提示

### 3. 用户登录
- 功能需要用户已登录
- 未登录用户会收到登录提示

## 后续优化建议

1. **离线支持**
   - 支持离线保存反馈内容
   - 网络恢复时自动提交

2. **多图片支持**
   - 支持选择多张图片
   - 批量上传功能

3. **反馈类型分类**
   - 添加反馈类型选择
   - 不同类型使用不同处理流程

4. **进度显示优化**
   - 显示具体的上传进度
   - 提供取消上传功能
