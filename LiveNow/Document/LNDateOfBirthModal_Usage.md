# LNDateOfBirthModal 使用说明

## 概述

`LNDateOfBirthModal` 是一个从底部弹出的全屏遮罩日期选择器，专门用于选择出生日期。该组件完全按照提供的设计图实现，具有优雅的动画效果和用户友好的交互体验。

## 功能特性

- ✅ 从底部弹出的全屏遮罩设计
- ✅ 三列滚轮选择器（年、月、日）
- ✅ 智能日期验证（自动处理不同月份的天数）
- ✅ 优雅的弹出和消失动画
- ✅ 支持点击背景或关闭按钮取消
- ✅ 完全适配屏幕尺寸（使用 `s()` 函数）
- ✅ 遵循项目设计规范（LN 前缀、SnapKit 布局）

## 设计还原

该组件完全按照提供的图片设计实现：

- **标题**: "Date Of Birth"
- **副标题**: "Please Choose Your Real Birthday, Which Cannot Be Modified After Submission"
- **年份范围**: 1950年到当前年份（倒序排列）
- **默认日期**: 1998-02-03
- **确认按钮**: 绿色渐变背景，圆角设计
- **关闭按钮**: 左上角 X 图标

## 使用方法

### 基本用法

```swift
// 1. 创建日期选择器实例
let modal = LNDateOfBirthModal()

// 2. 设置回调
modal.onConfirm = { [weak self] date in
    // 用户确认选择的日期
    self?.handleSelectedDate(date)
}

modal.onCancel = {
    // 用户取消选择
    print("Date picker cancelled")
}

// 3. 显示选择器
modal.show(in: self.view, initialDate: currentDate)
```

### 在 LNPersonalDataViewController 中的集成

该组件已经集成到 `LNPersonalDataViewController` 中，替换了原有的 `UIDatePicker` 实现：

```swift
@objc private func dobTapped() {
    let modal = LNDateOfBirthModal()
    modal.onConfirm = { [weak self] date in
        self?.data.birthDate = date
        self?.dobValueLabel.text = Self.dateFormatter.string(from: date)
    }
    modal.onCancel = {
        // 用户取消选择
    }
    modal.show(in: self.view, initialDate: data.birthDate)
}
```

## API 文档

### 初始化

```swift
let modal = LNDateOfBirthModal()
```

### 属性

- `onConfirm: ((Date) -> Void)?` - 确认选择回调
- `onCancel: (() -> Void)?` - 取消选择回调

### 方法

#### show(in:initialDate:)

显示日期选择器

**参数:**
- `parentView: UIView` - 父视图
- `initialDate: Date?` - 初始日期（可选，默认为1998-02-03）

**示例:**
```swift
modal.show(in: self.view, initialDate: Date())
```

## 技术实现

### 核心技术栈

- **UI框架**: UIKit
- **布局**: SnapKit
- **动画**: Core Animation
- **选择器**: UIPickerView
- **屏幕适配**: `s()` 函数

### 关键实现细节

1. **三列选择器设计**
   - 年份：1950年到当前年份（倒序）
   - 月份：01-12
   - 日期：根据选中年月动态计算

2. **智能日期验证**
   ```swift
   private func updateDaysForSelectedMonth() {
       let year = years[yearIndex]
       let month = monthIndex + 1
       let range = calendar.range(of: .day, in: .month, for: date)
       let daysInMonth = range?.count ?? 31
       days = Array(1...daysInMonth)
   }
   ```

3. **优雅动画效果**
   ```swift
   // 显示动画
   UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut) {
       self.backgroundView.alpha = 1
       self.containerView.transform = .identity
   }
   ```

## 测试方法

1. 运行项目
2. 进入 "我的" -> "Personal Data"
3. 点击 "Date of birth" 行
4. 测试以下功能：
   - 选择不同年份、月份、日期
   - 验证2月份天数（平年28天，闰年29天）
   - 测试动画效果
   - 测试取消和确认功能
   - 验证弹框覆盖整个屏幕包括导航栏

## 问题解决

### 1. 弹框覆盖问题
原问题：弹框只覆盖当前视图，不包括导航栏
解决方案：使用 `JKPOP.keyWindow` 作为父视图，确保覆盖整个屏幕

### 2. 动画问题
原问题：初始动画可能不流畅
解决方案：在动画完成后设置默认选中值，确保 pickerView 完全准备好

## 自定义扩展

如需自定义样式，可以修改以下属性：

```swift
// 修改颜色
confirmButton.backgroundColor = UIColor.systemBlue

// 修改字体
titleLabel.font = LNFont.bold(20)

// 修改年份范围
years = Array(1900...2030)
```

## 注意事项

1. 该组件使用了项目的全局屏幕适配函数 `s()`
2. 遵循项目的命名规范（LN 前缀）
3. 使用了项目的字体管理系统 `LNFont`
4. 支持深色模式和动态字体

## 总结

`LNDateOfBirthModal` 是一个功能完整、设计精美的日期选择器组件，完全按照设计图实现，具有良好的用户体验和代码质量。该组件可以轻松集成到任何需要日期选择功能的页面中。
