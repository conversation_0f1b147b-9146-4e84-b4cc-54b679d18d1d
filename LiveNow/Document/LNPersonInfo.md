根据图片内容完善 LNPersonInfoController，页面顶部是一个图片作为背景图片，做成下拉图片放大的效果
中间展示用户信息，NIKENAME，性别，下面展示用户 ID，右侧展示 Follow 按钮
然后下面展示位置，和关注的用户数
再下面是用户个人 Personal Profile 标签内容，单独创建视图 LNPersonalTagView，用来展示这块内容
在下面是 Personal photo 的内容，也单独创建视图 LNPersonalPhotoView，展示，是个可以横向滑动的 CollectionView
在下面是 Received a gift 的内容，也单独创建视图 LNReceivedAGiftView，展示，是个可以横向滑动的 CollectionView
在下面是 Maybe like 的内容，也单独创建视图 LNMaybeLikeView，展示，是个可以横向滑动的 CollectionView
然后底部是一个 Hello 和 Call Me 的按钮，这 2 个按钮常驻在页面底部，不跟随页面滑动