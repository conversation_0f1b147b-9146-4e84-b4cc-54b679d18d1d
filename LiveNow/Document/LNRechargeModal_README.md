# LNRechargeModal - 充值弹框组件

## 概述

`LNRechargeModal` 是一个功能完整的充值弹框组件，完全按照设计图实现，支持钻石充值和VIP套餐购买。该组件具有精美的UI设计、流畅的动画效果和完善的交互体验。

## 功能特性

### ✨ 核心功能
- 💎 **钻石充值**: 支持多种钻石套餐选择
- 👑 **VIP套餐**: 金牌、银牌、铜牌VIP选项
- 🎁 **奖励显示**: 支持额外奖励钻石展示
- 🏷️ **折扣标签**: 动态显示折扣信息
- 💰 **价格展示**: 清晰的价格标签

### 🎨 视觉效果
- 渐变背景头部区域
- 圆角卡片设计
- 流畅的弹出/收起动画
- 点击反馈动画
- 响应式布局设计

### 📱 技术特性
- 基于 SnapKit 约束布局
- 支持屏幕适配 (`s()` 函数)
- 使用项目统一字体系统 (`LNFont`)
- 内存安全的回调机制
- 支持深浅色模式

## 文件结构

```
LiveNow/
├── LNRechargeModal.swift           # 主要弹框组件
├── LNRechargeModalExample.swift    # 使用示例扩展
├── LNRechargeModalDemo.swift       # 演示页面
└── Document/
    └── LNRechargeModal_README.md   # 本文档
```

## 快速开始

### 1. 基本使用

```swift
// 创建充值弹框
let rechargeModal = LNRechargeModal()

// 设置当前钻石数量
rechargeModal.updateDiamondCount(100)

// 设置选择回调
rechargeModal.onItemSelected = { item in
    print("选择了: \(item.amount) - \(item.price)")
    // 处理购买逻辑
}

// 设置关闭回调
rechargeModal.onClose = {
    print("弹框已关闭")
}

// 显示弹框
rechargeModal.show(in: view)
```

### 2. 在视图控制器中使用

```swift
class YourViewController: UIViewController {
    
    @IBAction func showRechargeModal(_ sender: UIButton) {
        showRechargeModal()
    }
}

// 使用扩展方法
extension UIViewController {
    func showRechargeModal() {
        let rechargeModal = LNRechargeModal()
        rechargeModal.updateDiamondCount(getCurrentDiamondCount())
        
        rechargeModal.onItemSelected = { [weak self] item in
            self?.handlePurchase(item)
        }
        
        rechargeModal.show(in: view)
    }
}
```

## 数据模型

### RechargeItem 结构

```swift
struct RechargeItem {
    let type: ItemType          // 项目类型
    let amount: String          // 数量/名称
    let bonus: String?          // 奖励（可选）
    let price: String           // 价格
    let discount: String?       // 折扣（可选）
    let icon: String           // 图标
    
    enum ItemType {
        case diamond            // 钻石
        case goldVIP           // 金牌VIP
        case silverVIP         // 银牌VIP
        case bronzeVIP         // 铜牌VIP
    }
}
```

### 预设充值项目

组件内置了以下充值选项：
- 💎 199钻石 (+100奖励) - US$4.99
- 💎 199钻石 (+100奖励) - US$4.99 (30%折扣)
- 👑 Gold VIP (+100钻石) - US$4.99 (50%折扣)
- 🥈 Silver VIP (+1000钻石) - US$4.99 (50%折扣)
- 🥉 Bronze VIP (+800钻石) - US$4.99 (50%折扣)

## API 参考

### 公共方法

#### `show(in parentView: UIView)`
在指定父视图中显示弹框
- **参数**: `parentView` - 父视图容器
- **动画**: 从底部滑入，背景渐显

#### `dismiss()`
关闭弹框
- **动画**: 向底部滑出，背景渐隐
- **回调**: 触发 `onClose` 回调

#### `updateDiamondCount(_ count: Int)`
更新当前钻石数量显示
- **参数**: `count` - 钻石数量

### 回调属性

#### `onItemSelected: ((RechargeItem) -> Void)?`
用户选择充值项目时的回调
- **参数**: 选中的充值项目

#### `onClose: (() -> Void)?`
弹框关闭时的回调

## 自定义配置

### 修改充值项目

```swift
// 在 LNRechargeModal.swift 中修改 rechargeItems 数组
private let rechargeItems: [RechargeItem] = [
    RechargeItem(
        type: .diamond,
        amount: "500",
        bonus: "+200",
        price: "US$9.99",
        discount: "限时优惠",
        icon: "💎"
    ),
    // 添加更多项目...
]
```

### 修改样式

```swift
// 修改头部渐变色
gradientLayer.colors = [
    UIColor.hex(hexString: "#FF6B6B").cgColor,  // 自定义颜色
    UIColor.hex(hexString: "#4ECDC4").cgColor
]

// 修改价格标签颜色
priceLabel.backgroundColor = UIColor.hex(hexString: "#FF6B6B")
```

## 集成支付功能

### 1. Apple Pay 集成示例

```swift
rechargeModal.onItemSelected = { [weak self] item in
    self?.processApplePayment(for: item)
}

private func processApplePayment(for item: LNRechargeModal.RechargeItem) {
    // 集成 Apple Pay 或其他支付SDK
    let paymentRequest = PKPaymentRequest()
    paymentRequest.merchantIdentifier = "your.merchant.id"
    paymentRequest.supportedNetworks = [.visa, .masterCard, .amex]
    paymentRequest.merchantCapabilities = .capability3DS
    paymentRequest.countryCode = "US"
    paymentRequest.currencyCode = "USD"
    
    // 设置支付项目
    let paymentItem = PKPaymentSummaryItem(
        label: item.amount,
        amount: NSDecimalNumber(string: item.price.replacingOccurrences(of: "US$", with: ""))
    )
    paymentRequest.paymentSummaryItems = [paymentItem]
    
    // 显示支付界面
    let paymentController = PKPaymentAuthorizationViewController(paymentRequest: paymentRequest)
    paymentController?.delegate = self
    present(paymentController!, animated: true)
}
```

### 2. 第三方支付集成

```swift
private func processThirdPartyPayment(for item: LNRechargeModal.RechargeItem) {
    // 集成支付宝、微信支付等
    PaymentSDK.shared.pay(
        amount: item.price,
        productId: item.type.rawValue,
        completion: { [weak self] result in
            DispatchQueue.main.async {
                self?.handlePaymentResult(result, for: item)
            }
        }
    )
}
```

## 演示页面

项目包含了一个完整的演示页面 `LNRechargeModalDemo`，展示了如何使用充值弹框：

```swift
let demoVC = LNRechargeModalDemo()
navigationController?.pushViewController(demoVC, animated: true)
```

## 注意事项

1. **内存管理**: 使用 `weak self` 避免循环引用
2. **线程安全**: UI更新确保在主线程执行
3. **错误处理**: 支付失败时提供用户友好的错误提示
4. **数据验证**: 购买前验证用户状态和商品信息
5. **安全性**: 支付验证应在服务端完成

## 兼容性

- **iOS版本**: iOS 13.0+
- **设备支持**: iPhone、iPad
- **屏幕适配**: 支持所有屏幕尺寸
- **深色模式**: 完全支持

## 更新日志

### v1.0.0 (2025-08-18)
- ✅ 初始版本发布
- ✅ 完整的UI实现
- ✅ 基础功能完成
- ✅ 演示页面和文档

---

*LiveNow - 让生活更精彩* ✨
