# 视频通话弹框组件 (LNVideoCallModal)

## 概述

`LNVideoCallModal` 是一个高度可定制的视频通话弹框UI组件，模仿了现代社交应用中的视频通话界面设计。该组件提供了优雅的动画效果、渐变背景和完整的用户交互功能。

## 功能特性

- ✨ 优雅的弹出动画效果
- 🎨 渐变背景和毛玻璃效果
- 👤 用户头像展示
- 👑 VIP标识和等级显示
- 💎 价格信息展示
- 🆓 免费通话标识
- 🌍 国家/地区标识
- ➕ 关注按钮
- 📞 挂断和接听按钮
- 🔄 完全可定制的用户信息

## 使用方法

### 1. 基本使用

```swift
// 创建用户信息
let userInfo = LNVideoCallModal.UserInfo(
    name: "Sophia",
    country: "Indonesia", 
    avatarURL: nil,
    isVIP: true,
    vipLevel: 25,
    pricePerMinute: 50,
    isFree: false
)

// 创建弹框
let modal = LNVideoCallModal()

// 设置回调
modal.onHangUp = {
    // 处理挂断逻辑
    print("用户挂断了通话")
}

modal.onAnswer = {
    // 处理接听逻辑
    print("用户接听了通话")
}

modal.onFollow = {
    // 处理关注逻辑
    print("用户点击了关注")
}

// 显示弹框
modal.show(in: self.view, with: userInfo)
```

### 2. 免费通话示例

```swift
let freeUserInfo = LNVideoCallModal.UserInfo(
    name: "Emma",
    country: "Philippines",
    avatarURL: nil,
    isVIP: false,
    vipLevel: 0,
    pricePerMinute: 0,
    isFree: true
)

modal.show(in: self.view, with: freeUserInfo)
```

### 3. 手动关闭弹框

```swift
modal.dismiss()
```

## UserInfo 数据模型

```swift
struct UserInfo {
    let name: String           // 用户名
    let country: String        // 国家/地区
    let avatarURL: String?     // 头像URL（可选）
    let isVIP: Bool           // 是否为VIP用户
    let vipLevel: Int         // VIP等级
    let pricePerMinute: Int   // 每分钟价格（钻石数量）
    let isFree: Bool          // 是否免费通话
}
```

## 回调函数

- `onHangUp: (() -> Void)?` - 用户点击挂断按钮时调用
- `onAnswer: (() -> Void)?` - 用户点击接听按钮时调用  
- `onFollow: (() -> Void)?` - 用户点击关注按钮时调用

## 自定义样式

组件使用了以下颜色方案，可以通过修改源码进行自定义：

- **渐变背景**: 青绿色渐变 (#4ECDC4 到 #44A08D)
- **VIP标识**: 金色 (#FFD700)
- **价格标识**: 绿色系 (#4CAF50, #2E7D32)
- **国家标识**: 粉红色 (#E91E63)
- **关注按钮**: 绿色 (#00E676)
- **免费标识**: 橙色 (#FF9800)

## 动画效果

- **显示动画**: 淡入 + 缩放弹性动画
- **隐藏动画**: 淡出 + 缩放动画
- **动画时长**: 显示 0.3秒，隐藏 0.2秒

## 依赖

- UIKit
- SnapKit (用于自动布局)

## 注意事项

1. 确保在主线程中调用 `show` 和 `dismiss` 方法
2. 头像加载功能需要额外实现网络图片加载逻辑
3. 建议在视图控制器的 `viewDidLoad` 之后使用
4. 弹框会自动适配不同屏幕尺寸

## 演示

运行项目后，在首页点击"📹 视频通话弹框演示"按钮即可查看效果。演示页面提供了两种类型的弹框：

1. **付费通话**: 显示VIP标识和价格信息
2. **免费通话**: 显示免费标识

## 扩展建议

- 添加网络图片加载功能
- 支持更多自定义颜色主题
- 添加音效和震动反馈
- 支持横屏适配
- 添加更多动画效果
