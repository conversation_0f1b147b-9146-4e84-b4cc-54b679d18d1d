# 登录页面 (LNLoginViewController)

## 概述

`LNLoginViewController` 是一个完全按照设计图实现的登录页面控制器，继承自 `LNBaseController`。该页面提供了三种登录方式：Apple登录、Google登录和手机号登录，并包含用户协议和隐私政策的链接。

## 功能特性

- ✨ 渐变背景效果 (青绿色系)
- 🎨 自定义应用图标和嘴唇形状
- 🍎 Apple Sign In 集成准备
- 🔍 Google 登录集成准备
- 📱 手机号登录功能
- 📄 用户协议和隐私政策链接
- 🎯 响应式布局设计
- 🌟 优雅的UI动画效果

## 设计还原

### 视觉元素

1. **背景渐变**: 青绿色 (#00E5A0) 到深青色的渐变效果
2. **应用图标**: 白色圆角矩形容器，内含青绿色背景和白色嘴唇图标
3. **登录按钮**: 三个白色圆角按钮，带阴影效果
4. **图标设计**: Apple、Google和手机图标
5. **协议文本**: 底部半透明白色文字，带下划线链接
6. **底部指示器**: 白色半透明圆角矩形

### 布局结构

```
┌─────────────────────────────┐
│        状态栏 (浅色)          │
├─────────────────────────────┤
│                             │
│         渐变背景             │
│                             │
│      ┌─────────────┐        │
│      │  应用图标    │        │
│      │   (嘴唇)    │        │
│      └─────────────┘        │
│                             │
│                             │
│  ┌─────────────────────────┐ │
│  │  🍎 Sign in with Apple  │ │
│  └─────────────────────────┘ │
│                             │
│  ┌─────────────────────────┐ │
│  │  G  Connect with Google │ │
│  └─────────────────────────┘ │
│                             │
│  ┌─────────────────────────┐ │
│  │  📱 Login with phone    │ │
│  └─────────────────────────┘ │
│                             │
│     用户协议和隐私政策        │
│                             │
│         ────────            │
│        底部指示器            │
└─────────────────────────────┘
```

## 技术实现

### 继承结构

```swift
class LNLoginViewController: LNBaseController {
    // 隐藏导航栏
    override var prefersNavigationBarHide: Bool { return true }
    
    // 设置状态栏为浅色
    override var preferredStatusBarStyle: UIStatusBarStyle { return .lightContent }
}
```

### 核心组件

#### 1. 渐变背景
```swift
private func setupGradient() {
    let gradientLayer = CAGradientLayer()
    gradientLayer.colors = [
        UIColor.hex(hexString: "00E5A0").cgColor,
        UIColor.hex(hexString: "00D4AA").cgColor,
        UIColor.hex(hexString: "00C3B4").cgColor
    ]
    gradientLayer.startPoint = CGPoint(x: 0, y: 0)
    gradientLayer.endPoint = CGPoint(x: 1, y: 1)
}
```

#### 2. 自定义嘴唇图标
```swift
private func updateLipsShape() {
    let path = UIBezierPath()
    // 创建嘴唇形状的贝塞尔曲线
    // ... 具体路径绘制代码
    lipsShapeLayer.path = path.cgPath
}
```

#### 3. 登录按钮样式
```swift
private let appleSignInButton: UIButton = {
    let button = UIButton(type: .system)
    button.backgroundColor = UIColor.white
    button.layer.cornerRadius = 25
    button.layer.shadowColor = UIColor.black.cgColor
    button.layer.shadowOpacity = 0.1
    button.layer.shadowOffset = CGSize(width: 0, height: 2)
    button.layer.shadowRadius = 4
    return button
}()
```

## 使用方法

### 基本使用

```swift
// 创建登录控制器
let loginViewController = LNLoginViewController()

// 推送到导航栈
navigationController?.pushViewController(loginViewController, animated: true)

// 或者模态展示
present(loginViewController, animated: true)
```

### 从首页访问

在首页点击 "🔐 登录页面演示" 按钮即可查看登录页面效果。

## 交互功能

### 登录按钮回调

- **Apple登录**: 点击显示"Apple登录功能即将上线"提示
- **Google登录**: 点击显示"Google登录功能即将上线"提示  
- **手机号登录**: 点击显示"手机号登录功能即将上线"提示

### 协议链接

- **用户协议**: 点击文本中的"User Agreement"显示用户协议页面
- **隐私政策**: 点击文本中的"Privacy Policy"显示隐私政策页面

## 自定义配置

### 颜色主题

```swift
// 主要渐变色
UIColor.hex(hexString: "00E5A0") // 主色调
UIColor.hex(hexString: "00D4AA") // 中间色
UIColor.hex(hexString: "00C3B4") // 结束色

// 按钮样式
backgroundColor: UIColor.white
shadowColor: UIColor.black.cgColor
shadowOpacity: 0.1
```

### 布局参数

```swift
// 应用图标
iconSize: 100x100
cornerRadius: 25

// 登录按钮
buttonHeight: 50
cornerRadius: 25
horizontalMargin: 32
verticalSpacing: 16

// 底部指示器
width: 134
height: 4
cornerRadius: 2
```

## 扩展建议

1. **真实登录集成**
   - 集成 Apple Sign In SDK
   - 集成 Google Sign In SDK
   - 实现手机号验证码登录

2. **动画增强**
   - 添加按钮点击动画
   - 实现页面进入动画
   - 添加加载状态动画

3. **国际化支持**
   - 多语言文本支持
   - 不同地区的登录方式适配

4. **无障碍功能**
   - VoiceOver 支持
   - 动态字体支持
   - 高对比度模式支持

## 依赖

- UIKit
- SnapKit (自动布局)
- LNBaseController (基础控制器)
- UIColor+Extend (颜色扩展)

## 注意事项

1. 确保在主线程中更新UI
2. 渐变图层需要在 `viewDidLayoutSubviews` 中更新frame
3. 自定义形状需要在布局完成后绘制
4. 状态栏样式设置为浅色以适配深色背景

## 测试

运行项目后：
1. 在首页点击 "🔐 登录页面演示"
2. 查看页面布局和视觉效果
3. 测试各个按钮的点击响应
4. 测试协议文本的链接点击
