# Settings 页面说明

本页实现 iOS 13+ 的设置界面（LNSettingViewController），遵循 UIKit + SnapKit 架构，支持深色模式与动态字体。

## 入口
- 我的(LNProfileViewController) -> 菜单项“Setting”

## 功能
1. Do Not Disturb（免打扰）开关
   - 存储键：UserDefaults.LNDoNotDisturbEnabled
2. Blacklist / Help and Feed back / Delete account
   - Help and Feed back 跳转到 LNCustomerServiceViewController
   - 其余为示例弹窗
3. User Agreement / Privacy Policy / Terms of Use / Child Safety Standards policy / About Us
   - 示例弹窗，后续可接入 H5
4. Version / Clear cache
   - Version 显示当前版本与构建号
   - Clear cache 展示缓存大小并支持清理 Caches 目录
5. Logout
   - 渐变色按钮，示例弹窗

## 代码位置
- LiveNow/Profile/LNSettingViewController.swift

## 注意
- 若后续接入真实删除账号/协议页，请关注合规与 App Store 审核要求。

