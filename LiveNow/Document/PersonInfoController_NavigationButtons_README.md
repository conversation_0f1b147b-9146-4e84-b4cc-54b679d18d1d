# 个人信息页面导航栏按钮和WhatsApp弹框实现

## 概述
在个人信息页面 (`LNPersonInfoController`) 中添加了导航栏右侧按钮和WhatsApp获取弹框功能。

## 新增功能

### 1. 导航栏右侧按钮
- **WhatsApp按钮**: 使用 `ic_whatsapp` 图标
- **More按钮**: 使用 `ic_more` 图标
- **位置**: 导航栏右侧
- **间距**: 按钮之间有 16pt 间距
- **自适应颜色**: 根据导航栏背景透明度自动调整颜色

### 2. LNWhatsAppModal 弹框组件
- **文件位置**: `LiveNow/Setting/Modal/LNWhatsAppModal.swift`
- **功能**: 显示获取WhatsApp联系方式的弹框
- **触发方式**: 
  - 点击导航栏WhatsApp按钮
  - 点击底部Hello按钮

## 技术实现

### 导航栏按钮设置
```swift
private func setupNavigationBarButtons() {
    // WhatsApp按钮
    let whatsappButton = UIButton(type: .system)
    whatsappButton.setImage(UIImage(named: "ic_whatsapp"), for: .normal)
    whatsappButton.tintColor = .white
    whatsappButton.frame = CGRect(x: 0, y: 0, width: s(24), height: s(24))
    
    // More按钮
    let moreButton = UIButton(type: .system)
    moreButton.setImage(UIImage(named: "ic_more"), for: .normal)
    moreButton.tintColor = .white
    moreButton.frame = CGRect(x: 0, y: 0, width: s(24), height: s(24))
    
    // 设置到导航栏
    navigationItem.rightBarButtonItems = [moreBarButton, spacer, whatsappBarButton]
}
```

### 按钮颜色自适应
```swift
private func updateNavigationBarButtonColors(_ color: UIColor) {
    if let rightBarButtonItems = navigationItem.rightBarButtonItems {
        for barButtonItem in rightBarButtonItems {
            if let customView = barButtonItem.customView as? UIButton {
                customView.tintColor = color
            }
        }
    }
}
```

### WhatsApp弹框组件
```swift
class LNWhatsAppModal: UIView {
    // 主要UI元素
    - backgroundView: 半透明遮罩
    - containerView: 白色圆角容器
    - heartContainer: 爱心装饰容器
    - leftHeartView: 左侧粉色爱心 (ic_heart)
    - rightHeartView: 右侧深粉色爱心 (ic_heart)
    - titleLabel: "want to get her whatsapp ?"
    - descriptionLabel: "you can get her whatsapp number by paying 80 diamonds"
    - vipButton: "ONLY VIP can see new girls"
}
```

## 设计规范

### 弹框设计
- **容器尺寸**: 300pt × 280pt
- **圆角半径**: 20pt
- **背景色**: 白色
- **遮罩**: 黑色 50% 透明度

### 爱心装饰
- **图片资源**: ic_heart
- **左侧爱心**: 35pt × 35pt, 颜色 #FF69B4
- **右侧爱心**: 28pt × 28pt, 颜色 #FF1493
- **位置**: 容器顶部外侧

### 文字样式
- **标题**: LNFont.medium(20), 颜色 #04E798
- **描述**: LNFont.regular(16), 黑色
- **按钮**: LNFont.medium(16), 白色

### 按钮样式
- **背景色**: #04E798
- **圆角**: 25pt
- **高度**: 50pt

## 动画效果

### 弹框显示动画
- **初始状态**: 透明度 0, 缩放 0.8
- **最终状态**: 透明度 1, 缩放 1.0
- **动画时长**: 0.3秒
- **动画类型**: Spring 动画

### 弹框消失动画
- **最终状态**: 透明度 0, 缩放 0.8
- **动画时长**: 0.3秒
- **完成后**: 从父视图移除

## 事件处理

### 按钮事件
```swift
@objc private func whatsappButtonTapped() {
    // 显示WhatsApp弹框
    showWhatsAppModal()
}

@objc private func moreButtonTapped() {
    // 预留更多功能
}
```

### 弹框事件
- **VIP按钮**: 关闭弹框，预留VIP功能
- **背景点击**: 关闭弹框
- **Hello按钮**: 显示WhatsApp弹框

## 集成方式

### 在 viewDidLoad 中初始化
```swift
override func viewDidLoad() {
    super.viewDidLoad()
    setupUI()
    setupConstraints()
    setupNavigationBarButtons()  // 新增
    setupTransparentNavigationBar()
}
```

### 显示弹框
```swift
private func showWhatsAppModal() {
    let modal = LNWhatsAppModal()
    modal.show(in: view)
}
```

## 注意事项

1. **图片资源**: 需要确保项目中有以下图片资源
   - `ic_whatsapp`: WhatsApp图标
   - `ic_more`: 更多功能图标
   - `ic_heart`: 爱心图标

2. **颜色自适应**: 导航栏按钮颜色会根据滚动位置自动调整
   - 透明背景时: 白色图标
   - 不透明背景时: 黑色图标

3. **屏幕适配**: 所有尺寸都使用 `s()` 函数进行适配

4. **内存管理**: 弹框在消失动画完成后会自动从父视图移除

## 扩展功能

### 可扩展的功能点
1. **More按钮菜单**: 可以添加更多功能选项
2. **VIP验证**: 集成VIP会员验证逻辑
3. **支付功能**: 添加钻石支付接口
4. **WhatsApp集成**: 实际的WhatsApp联系功能

### 建议的改进
1. 添加网络请求获取真实的WhatsApp信息
2. 集成支付SDK处理钻石消费
3. 添加用户VIP状态验证
4. 优化动画效果和用户体验
