# 充值页面实现文档

## 概述

充值页面（LNRechargeViewController）是一个用于用户购买钻石的界面，采用现代化的卡片式设计，支持多种充值选项和折扣标签。

## 功能特性

### 1. 页面结构
- **顶部钻石显示区域**: 显示用户当前拥有的钻石数量
- **充值选项列表**: 展示多个充值套餐，包括钻石数量、奖励钻石和价格
- **折扣标签**: 支持显示促销折扣信息（如30%Off、50%Off）

### 2. 设计特点
- 使用项目统一的渐变背景设计
- 卡片式布局，提供良好的视觉层次
- 响应式设计，适配不同屏幕尺寸
- 流畅的点击动画效果

### 3. 技术实现
- 继承自 `LNBaseController`，享受统一的导航栏和背景样式
- 使用 `SnapKit` 进行自动布局
- 采用组件化设计，充值选项抽取为独立的 `LNRechargeOptionView`

## 文件结构

```
LiveNow/Setting/
├── LNRechargeViewController.swift      # 主控制器
├── LNRechargeOptionView.swift          # 充值选项视图组件
└── ...

LiveNow/Test/
├── LNRechargeViewControllerTests.swift # 测试文件
└── ...

LiveNow/Document/
├── Recharge_README.md                  # 本文档
└── ...
```

## 核心组件

### LNRechargeViewController

主控制器，负责整体页面布局和业务逻辑。

**主要属性：**
- `currentDiamonds`: 当前钻石数量
- `rechargeOptions`: 充值选项数据数组
- `diamondHeaderView`: 钻石显示头部视图
- `optionsStackView`: 充值选项容器

**主要方法：**
- `setupUI()`: 初始化UI组件
- `setupRechargeOptions()`: 创建充值选项视图
- `handleRecharge(option:)`: 处理充值逻辑
- `processPurchase(option:)`: 处理支付流程

### LNRechargeOptionView

充值选项视图组件，可复用的UI组件。

**主要属性：**
- `option`: 充值选项数据模型
- `onTapped`: 点击回调闭包
- `containerView`: 容器视图
- `discountView`: 折扣标签视图

**主要方法：**
- `configure(with:)`: 配置选项数据
- `setHighlighted(_:animated:)`: 设置高亮状态

## 数据模型

### RechargeOption
```swift
struct RechargeOption {
    let diamonds: Int           // 基础钻石数量
    let bonusDiamonds: Int     // 奖励钻石数量
    let priceUSD: String       // 价格（美元）
    let discountLabel: String? // 折扣标签（可选）
    
    var totalDiamonds: Int { return diamonds + bonusDiamonds }
}
```

## 使用方法

### 1. 基本使用
```swift
let rechargeVC = LNRechargeViewController()
navigationController?.pushViewController(rechargeVC, animated: true)
```

### 2. 自定义充值选项
```swift
// 在LNRechargeViewController中修改rechargeOptions数组
private let rechargeOptions: [RechargeOption] = [
    RechargeOption(diamonds: 100, bonusDiamonds: 50, priceUSD: "US$2.99", discountLabel: nil),
    RechargeOption(diamonds: 200, bonusDiamonds: 100, priceUSD: "US$4.99", discountLabel: "20%Off"),
    // 更多选项...
]
```

### 3. 处理支付回调
```swift
private func processPurchase(option: RechargeOption) {
    // 集成实际的支付SDK
    // 例如：Apple Pay, Google Pay, 或第三方支付
    
    // 支付成功后更新钻石数量
    currentDiamonds += option.totalDiamonds
    diamondCountLabel.text = "\(currentDiamonds)"
}
```

## 样式配置

### 1. 颜色配置
- 主色调：`#04E798` (绿色渐变)
- 折扣标签：`#FF6B35` (橙色)
- 文本颜色：`#333333` (深灰色)
- 背景渐变：`#B8FFE6` → `#E2FFD9`

### 2. 字体配置
- 标题：`LNFont.medium(16)`
- 钻石数量：`LNFont.bold(48)`
- 价格：`LNFont.bold(16)`
- 折扣标签：`LNFont.medium(12)`

### 3. 尺寸配置
- 卡片圆角：`s(12)`
- 选项高度：`s(68)`
- 钻石头部高度：`s(130)`
- 间距：`s(16)` (标准间距)

## 测试

项目包含完整的测试文件 `LNRechargeViewControllerTests.swift`，涵盖：

- 基本显示测试
- 充值选项测试
- 钻石数量显示测试
- UI布局测试
- 点击交互测试
- 渐变背景测试

运行测试：
```swift
LNRechargeViewControllerTests.runAllTests()
```

## 注意事项

1. **支付集成**: 当前使用模拟支付，实际项目中需要集成真实的支付SDK
2. **数据持久化**: 钻石数量需要与服务器同步，确保数据一致性
3. **错误处理**: 需要添加网络错误、支付失败等异常情况的处理
4. **本地化**: 文本内容需要支持多语言本地化
5. **安全性**: 支付相关的验证应该在服务器端进行

## 扩展功能

### 可能的扩展方向：
1. 添加支付历史记录
2. 支持更多支付方式
3. 添加充值优惠活动
4. 实现充值礼包功能
5. 添加充值统计分析

## 更新日志

- **v1.0.0** (2025-08-17): 初始版本实现
  - 基础充值页面功能
  - 卡片式设计
  - 折扣标签支持
  - 点击动画效果
