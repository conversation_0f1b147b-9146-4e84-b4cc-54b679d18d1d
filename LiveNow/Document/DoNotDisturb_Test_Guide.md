# DoNotDisturb 功能测试指南

## 功能概述

DoNotDisturb功能已成功集成到设置页面中，包含以下特性：

1. **获取状态**：页面加载时自动从服务器获取DoNotDisturb状态
2. **设置状态**：用户点击开关时调用API更新状态
3. **错误处理**：网络错误和服务器错误的完整处理
4. **用户反馈**：成功和失败的提示信息

## API接口

### 获取状态
- **接口**：`GET /blade-auth/user/disturb/status`
- **返回格式**：
```json
{
  "code": 200,
  "data": {
    "disturbExpireSecs": 0,
    "isNotDisturb": 0
  },
  "msg": "",
  "success": 1
}
```
- **字段说明**：
  - `isNotDisturb`: 0表示未开启免打扰，1表示开启免打扰
  - `disturbExpireSecs`: 免打扰过期时间（秒）

### 设置状态
- **接口**：`POST /blade-auth/user/disturb/switch`
- **请求参数**：
```json
{
  "isOpen": "1"  // "1"表示开启，"0"表示关闭
}
```
- **返回格式**：
```json
{
  "code": 200,
  "data": {
    "disturbExpireSecs": 0,
    "isNotDisturb": 1
  },
  "msg": "",
  "success": 1
}
```

## 测试场景

### 1. 正常流程测试
1. 打开设置页面
2. 观察DoNotDisturb开关的初始状态（应该从服务器获取）
3. 点击开关切换状态
4. 验证是否显示成功提示
5. 重新进入页面验证状态是否保持

### 2. 网络错误测试
1. 断开网络连接
2. 点击DoNotDisturb开关
3. 验证是否显示网络错误提示
4. 验证开关状态是否恢复到原来的状态

### 3. 服务器错误测试
1. 模拟服务器返回错误响应
2. 点击开关
3. 验证是否显示错误提示
4. 验证开关状态是否恢复

### 4. 重复点击测试
1. 快速多次点击开关
2. 验证开关在请求期间是否被禁用
3. 验证不会发送重复请求

## 代码实现要点

### 关键方法
- `loadDoNotDisturbStatus()`: 页面加载时获取状态
- `setDoNotDisturbStatus(_:sender:)`: 设置状态
- `dndChanged(_:)`: 开关点击事件处理

### 状态管理
- `doNotDisturbStatus`: 服务器状态
- `UserDefaults`: 本地缓存状态
- UI开关状态与服务器状态保持同步

### 用户体验优化
- 请求期间禁用开关防止重复点击
- 成功/失败提示信息
- 网络错误时恢复开关状态
- 使用本地缓存作为备用方案

## 注意事项

1. 确保网络请求在主线程更新UI
2. 使用weak self避免内存泄漏
3. 服务器状态优先于本地状态
4. 错误情况下恢复用户界面状态
