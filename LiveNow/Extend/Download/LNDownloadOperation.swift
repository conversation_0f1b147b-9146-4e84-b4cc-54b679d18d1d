//
//  LNDownloadOperation.swift
//  LiveNow
//
//  Created by edy on 2025/8/21.
//

import Foundation

/// 下载操作状态枚举
fileprivate enum LNDownloadOperationState: Int {
    case ready = 0  // 准备
    case executing  // 执行中
    case finished   // 完成
}

/// 取得下载操作状态对应的keyPath
/// - Parameter state: 下载操作状态
/// - Returns: 返回的keyPath
@inline(__always) fileprivate func downloadOperationKeyPath(with state: LNDownloadOperationState) -> String {
    switch state {
    case .ready:
        return #keyPath(Operation.isReady)
    case .executing:
        return #keyPath(Operation.isExecuting)
    case .finished:
        return #keyPath(Operation.isFinished)
    }
}

/// 下载操作代理
/// 代理全部都是在主线程调用的
protocol ZYDownloadOperationDelegate: NSObjectProtocol {
    
    /// 已经开始下载
    /// - Parameters:
    ///   - target: 下载实例
    func downloadDidStart(_ target: LNDownloadOperation)
    
    /// 下载已完成
    /// - Parameters:
    ///   - target: 下载实例
    func downloadFinished(_ target: LNDownloadOperation)
    
    /// 下载失败
    /// - Parameters:
    ///   - error: 错误信息
    ///   - target: 下载实例
    func downloadFailed(_ error: Error, target: LNDownloadOperation)
    
    /// 下载已取消
    /// - Parameter target: 下载实例
    func downloadCanceled(_ target: LNDownloadOperation)
    
    /// 下载进度变化
    /// - Parameters:
    ///   - progress: 改变后的进度 0到1
    ///   - receiveByte: 已接收的大小，字节数
    ///   - allByte: 总大小，字节数
    ///   - target: 下载实例
    func downloadProgressChanged(_ progress: Float, receiveByte: Int64, allByte: Int64, target: LNDownloadOperation)
}

/// 代理方法默认实现，防止未实现代理方法时崩溃
extension ZYDownloadOperationDelegate {
    func downloadDidStart(_ target: LNDownloadOperation) {}
    func downloadFinished(_ target: LNDownloadOperation) {}
    func downloadFailed(_ error: Error, target: LNDownloadOperation) {}
    func downloadCanceled(_ target: LNDownloadOperation) {}
    func downloadProgressChanged(_ progress: Float, receiveByte: Int64, allByte: Int64, target: LNDownloadOperation) {}
}

/// 下载操作类
class LNDownloadOperation: Operation, @unchecked Sendable {
    
    /// 下载状态代理
    public weak var delegate: ZYDownloadOperationDelegate?
    
    /// 用户自定义信息
    public var userInfo: Any?
    
    /// 下载操作Block回调
    public var downloadDidStart: ((_ target: LNDownloadOperation) -> Void)?
    public var downloadFinished: ((_ target: LNDownloadOperation) -> Void)?
    public var downloadFailed: ((_ error: Error, _ target: LNDownloadOperation) -> Void)?
    public var downloadCanceled: ((_ target: LNDownloadOperation) -> Void)?
    public var downloadProgressChanged: ((_ progress: Float, _ receiveByte: Int64, _ allByte: Int64, _ target: LNDownloadOperation) -> Void)?
    
    /// 下载的文件地址
    private(set) var urlString: String
    
    /// 下载的文件存储路劲
    private(set) var filePath: String
    
    /// 下载的文件临时路劲
    private var tempFilePath: String
    
    /// 开始下载的文件位置大小，用于断点续传
    private var startFileSize: Int64 = 0
    
    /// 下载操作状态
    private var state: LNDownloadOperationState = .ready {
        willSet {
            lock.lock()
            let oldKeyPath = downloadOperationKeyPath(with: state)
            let newKeyPath = downloadOperationKeyPath(with: newValue)
            willChangeValue(forKey: oldKeyPath)
            willChangeValue(forKey: newKeyPath)
        }
        didSet {
            let oldKeyPath = downloadOperationKeyPath(with: oldValue)
            let newKeyPath = downloadOperationKeyPath(with: state)
            didChangeValue(forKey: oldKeyPath)
            didChangeValue(forKey: newKeyPath)
            lock.unlock()
        }
    }
    
    /// 是否是ready状态
    override var isReady: Bool {
        super.isReady && state == .ready
    }
    
    /// 下载是否执行中
    override var isExecuting: Bool {
        state == .executing
    }

    /// 是否已完成
    override var isFinished: Bool {
        state == .finished
    }
    
    /// 是否异步执行
    override var isConcurrent: Bool {
        true
    }
    
    /// 禁止外部使用默认初始化方法
    private override init() {
        fatalError("Can't init, Please use `init(urlString: String, filePath: String?, needResume: Bool)`")
    }
    
    /// 统一初始化方法
    /// - Parameters:
    ///   - urlString: 文件下载地址
    ///   - filePath: 文件下载路劲，为nil使用默认路径，默认为nil
    ///   - needResume: 是否需要恢复下载，即断点续传
    init(urlString: String, filePath: String? = nil, needResume: Bool = true) {
        self.urlString = urlString
        if let path = filePath {
            self.filePath = path
        } else {
            self.filePath = LNDownloadHelper.filePath(urlString: urlString)
        }
        self.tempFilePath = self.filePath + ".tmp"
        if needResume {
            startFileSize = LNDownloadHelper.fileSize(path: self.tempFilePath)
        }
        super.init()
        
        guard let url = URL(string: urlString) else {
            fatalError("url error: \(urlString)")
        }
        var request = URLRequest(url: url)
        if startFileSize > 0 {
            // bytes=100-999 表示第100至第999字节
            // bytes=100- 表示从第100字节往后的所有字节
            let range = String(format: "bytes=%llu-", startFileSize)
            request.addValue(range, forHTTPHeaderField: "Range")
        }
        let config = URLSessionConfiguration.default
        let session = URLSession(configuration: config, delegate: self, delegateQueue: .current)
        downloadTask = session.dataTask(with: request)
    }
    
    /// 开始下载
    override func start() {
        if isCancelled {
            cancelOperation()
        } else if isReady {
            startOperation()
        }
    }
    
    override func cancel() {
        if !isFinished && !isCancelled {
            super.cancel()
            cancelOperation()
        }
    }
    
    /// 存储数据
    /// 将offset标到文件末尾,在末尾写入数据,最后关闭文件
    /// - Parameter data: 文件数据
    private func saveData(_ data: Data) {
        do {
            let fileHandle = try FileHandle(forUpdating: URL(fileURLWithPath: self.tempFilePath))
            fileHandle.seekToEndOfFile()
            if #available(iOS 13.4, *) {
                try fileHandle.write(contentsOf: data)
            } else {
                // Fallback on earlier versions
                fileHandle.write(data)
            }
            fileHandle.closeFile()
        } catch let error {
            print(error.localizedDescription)
        }
    }
    
    /// 下载任务实例
    var downloadTask: URLSessionDataTask?
    
    /// 锁，用于确保线程安全
    private lazy var lock: NSLock = {
        let lock = NSLock()
        lock.name = "com.download.operation.lock"
        return lock
    }()
}

extension LNDownloadOperation {
    
    /// 单文件快捷下载，
    /// - Parameters:
    ///   - urlString: 下载地址
    ///   - filePath: 文件存储完整路劲
    ///   - needResume: 是否需要支持断点续传
    ///   - progressChanged: 进度改变回调
    ///   - completion: 下载结束回调
    class func start(urlString: String, filePath: String? = nil, needResume: Bool = true, progressChanged: ((_ progress: Float, _ receiveByte: Int64, _ allByte: Int64, _ target: LNDownloadOperation) -> Void)? = nil, completion: ((_ error: Error?, _ target: LNDownloadOperation) -> Void)?) {
        guard URL(string: urlString) != nil else { return }
        let operation = LNDownloadOperation(urlString: urlString, filePath: filePath, needResume: needResume)
        operation.downloadFinished = { target in
            completion?(nil, target)
        }
        operation.downloadFailed = { (error, target) in
            completion?(error, target)
        }
        operation.downloadProgressChanged = progressChanged
        operation.start()
    }
}

fileprivate extension LNDownloadOperation {
    
    @objc private func startOperation() {
        state = .executing
        downloadTask?.resume()
        callStart()
    }
    
    @objc private func cancelOperation() {
        downloadTask?.cancel()
        callCanceled()
    }
    
    private func callStart() {
        DispatchQueue.main.async {
            self.delegate?.downloadDidStart(self)
            self.downloadDidStart?(self)
        }
    }
    
    private func callFinished() {
        DispatchQueue.main.async {
            self.delegate?.downloadFinished(self)
            self.downloadFinished?(self)
        }
    }
    
    private func callFailed(_ error: Error) {
        DispatchQueue.main.async {
            self.delegate?.downloadFailed(error, target: self)
            self.downloadFailed?(error, self)
        }
    }
    
    private func callCanceled() {
        DispatchQueue.main.async {
            self.delegate?.downloadCanceled(self)
            self.downloadCanceled?(self)
        }
    }
    
    private func callProgressChanged(_ progress: Float, receiveByte: Int64, allByte: Int64) {
        DispatchQueue.main.async {
            let value = max(min(1, progress), 0)
            self.delegate?.downloadProgressChanged(value, receiveByte: receiveByte, allByte: allByte, target: self)
            self.downloadProgressChanged?(value, receiveByte, allByte, self)
        }
    }
}

extension LNDownloadOperation: URLSessionDataDelegate {
    
    func urlSession(_ session: URLSession, didBecomeInvalidWithError error: Error?) {
        if let error = error {
            state = .finished
            callFailed(error)
        }
    }

    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            // 下载超时失败，直接结束该任务，否则会线程锁无法释放会堵塞线程任务
            state = .finished
            callFailed(error)
        } else {
            LNDownloadHelper.moveFile(oldPath: tempFilePath, to: filePath)
            state = .finished
            callFinished()
        }
    }

    /* 接收到数据,将数据存储 */
    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive data: Data) {
        guard let response = dataTask.response as? HTTPURLResponse else { return }
        /* 无断点续传时候,一直走200;断点续传后,一直走206 */
        if response.statusCode == 200 || response.statusCode == 206 {
            let progress = Float(dataTask.countOfBytesReceived + startFileSize)
                / Float(dataTask.countOfBytesExpectedToReceive + startFileSize)
            let receiveByte = dataTask.countOfBytesReceived + startFileSize
            let allByte = dataTask.countOfBytesExpectedToReceive + startFileSize
            callProgressChanged(progress, receiveByte: receiveByte, allByte: allByte)
            saveData(data)
        }
    }
}
