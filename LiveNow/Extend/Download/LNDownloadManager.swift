//
//  LNDownloadManager.swift
//  LiveNow
//
//  Created by edy on 2025/8/21.
//

import Foundation

/// 下载状态
enum LNDownloadState {
    case wait                   // 等待
    case start                  // 开始
    case pause(error: Error?)   // 暂停，如果是下载出错导致暂停，error有值
    case complete               // 完成
}

/// 下载管理器协议
protocol LNDownloadManagerDelegate: NSObjectProtocol {
    
    /// 下载状态改变
    /// - Parameters:
    ///   - manager: 管理器
    ///   - state: 状态
    ///   - operation: 下载实例
    func download(_ manager: LNDownloadManager, stateChanged state: LNDownloadState, with operation: LNDownloadOperation)
    
    /// 下载进度改变
    /// - Parameters:
    ///   - manager: 管理器
    ///   - progress: 进度
    ///   - receiveByte: 已接收的文件大小
    ///   - allByte: 文件总大小
    ///   - operation: 下载实例
    func download(_ manager: LNDownloadManager, progressChanged progress: Float, receiveByte: Int64, allByte: Int64, with operation: LNDownloadOperation)
}

extension LNDownloadManagerDelegate {
    func download(_ manager: LNDownloadManager, stateChanged state: LNDownloadState, with operation: LNDownloadOperation) {}
    func download(_ manager: LNDownloadManager, progressChanged progress: Float, receiveByte: Int64, allByte: Int64, with operation: LNDownloadOperation) {}
}

/// 声明回调的Block类型
/// 下载状态改变回调Block
typealias LNDownloadStateChangedBlock = ((_ manager: LNDownloadManager, _ state: LNDownloadState, _ operation: LNDownloadOperation) -> Void)

/// 下载进度改变回调Block
typealias LNDownloadProgressChangedBlock = ((_ manager: LNDownloadManager, _ progress: Float, _ receiveByte: Int64, _ allByte: Int64, _ operation: LNDownloadOperation) -> Void)
 
/// 下载管理器
class LNDownloadManager: NSObject {
    
    /// 默认创建一份管理器实例
    /// 无需UI显示进度或状态的文件下载可以使用
    /// 需UI显示进度或状态的文件下载建议自定义并初始化，以防止`default`管理器有其他正在下载的文件而导致UI显示与预期不符
    /// （例：`default`管理器同时已有3个文件正在下载，此时进入某下载管理UI界面，点击未下载的文件，预期是直接开始下载，实际点击之后却是等待）
    static let `default` = LNDownloadManager()
    
    /// 同时下载的数量
    public var maxConcurrentDownloadCount = 3 {
        didSet {
            downloadQueue.maxConcurrentOperationCount = maxConcurrentDownloadCount
        }
    }
    
    /// 下载队列优先级
    public var qualityOfService: QualityOfService = .default {
        didSet {
            downloadQueue.qualityOfService = qualityOfService
        }
    }
    
    /// 开始下载
    /// - Parameters:
    ///   - urlString: 下载地址
    ///   - delegate: 下载协议代理
    ///   - filePath: 文件存储路劲 ，默认“xx/Documents/Download/xxxxx.xx”
    ///   - needResume: 是否需要断点续传
    func start(with urlString: String, highPriority: Bool = false, delegate: LNDownloadManagerDelegate?, filePath: String? = nil, needResume: Bool = true) {
        guard URL(string: urlString) != nil else { return }
        if downloadMap[urlString.uc_md5] != nil {
            delegateMap[urlString.uc_md5] = NSValue(nonretainedObject: delegate)
        } else {
            let operation = LNDownloadOperation(urlString: urlString, filePath: filePath, needResume: needResume)
            operation.downloadDidStart = { [weak self] target in
                self?.excuteStateCallback(state: .start, target: target)
            }
            operation.downloadCanceled = { [weak self] target in
                self?.excuteStateCallback(state: .pause(error: nil), target: target)
                self?.cleanCallback(target: target)
            }
            operation.downloadFinished = { [weak self] target in
                self?.excuteStateCallback(state: .complete, target: target)
                self?.cleanCallback(target: target)
            }
            operation.downloadFailed = { [weak self] (error, target) in
                self?.excuteStateCallback(state: .pause(error: error), target: target)
                self?.cleanCallback(target: target)
                
                // 下载超时失败，直接结束该任务，重新添加任务至队尾
                Log("[URLSession]--下载超时失败，直接结束该任务，重新添加任务至队尾--urlString:" + urlString)
                self?.start(with: urlString, highPriority: highPriority, delegate: delegate, filePath: filePath, needResume: needResume)
            }
            operation.downloadProgressChanged = { [weak self] (progress, receiveByte, allByte, target) in
                self?.excuteProgressCallback(progress: progress, receiveByte: receiveByte, allByte: allByte, target: target)
            }
            downloadMap[urlString.uc_md5] = operation
            delegateMap[urlString.uc_md5] = NSValue(nonretainedObject: delegate)
            excuteStateCallback(state: .wait, target: operation)
            if highPriority {
                highPriorityQueue.addOperation(operation)
            } else {
                downloadQueue.addOperation(operation)
            }
        }
    }
    
    /// 开始下载
    /// - Parameters:
    ///   - urlString: 下载地址
    ///   - filePath: 文件存储路劲，默认“xx/Documents/Download/xxxxx.xx”
    ///   - needResume: 是否断点续传
    ///   - progressChanged: 进度改变block
    ///   - stateChanged: 状态改变block
    func start(with urlString: String, highPriority: Bool = false, filePath: String? = nil, needResume: Bool = true, progressChanged: LNDownloadProgressChangedBlock? = nil, stateChanged: LNDownloadStateChangedBlock?) {
        guard URL(string: urlString) != nil else { return }
        if downloadMap[urlString.uc_md5] != nil {
            if let block = progressChanged {
                progressBlockMap[urlString.uc_md5] = block
            } else {
                progressBlockMap.removeValue(forKey: urlString.uc_md5)
            }
            if let block = stateChanged {
                stateBlockMap[urlString.uc_md5] = block
            } else {
                stateBlockMap.removeValue(forKey: urlString.uc_md5)
            }
        } else {
            let operation = LNDownloadOperation(urlString: urlString, filePath: filePath, needResume: needResume)
            operation.downloadDidStart = { [weak self] target in
                self?.excuteStateCallback(state: .start, target: target)
            }
            operation.downloadCanceled = { [weak self] target in
                self?.excuteStateCallback(state: .pause(error: nil), target: target)
                self?.cleanCallback(target: target)
            }
            operation.downloadFinished = { [weak self] target in
                self?.excuteStateCallback(state: .complete, target: target)
                self?.cleanCallback(target: target)
            }
            operation.downloadFailed = { [weak self] (error, target) in
                self?.excuteStateCallback(state: .pause(error: error), target: target)
                self?.cleanCallback(target: target)
                
                // 下载超时失败，直接结束该任务，重新添加任务至队尾
                Log("[URLSession]--下载超时失败，直接结束该任务，重新添加任务至队尾--urlString:" + urlString)
                self?.start(with: urlString, highPriority: highPriority, filePath: filePath, needResume: needResume, progressChanged: progressChanged, stateChanged: stateChanged)
            }
            operation.downloadProgressChanged = { [weak self] (progress, receiveByte, allByte, target) in
                self?.excuteProgressCallback(progress: progress, receiveByte: receiveByte, allByte: allByte, target: target)
            }
            downloadMap[urlString.uc_md5] = operation
            if let block = progressChanged {
                progressBlockMap[urlString.uc_md5] = block
            }
            if let block = stateChanged {
                stateBlockMap[urlString.uc_md5] = block
            }
            excuteStateCallback(state: .wait, target: operation)
            if highPriority {
                highPriorityQueue.addOperation(operation)
            } else {
                downloadQueue.addOperation(operation)
            }
        }
    }
    
    /// 暂停下载
    /// - Parameter urlString: 下载地址
    func pause(with urlString: String) {
        if let operation = downloadMap[urlString.uc_md5] {
            operation.cancel()
        }
    }
    
    /// 暂停全部
    func pauseAll() {
        downloadQueue.cancelAllOperations()
    }
 
    private func excuteStateCallback(state: LNDownloadState, target: LNDownloadOperation) {
        if let delegateValue = self.delegateMap[target.urlString.uc_md5],
              let delegate = delegateValue.nonretainedObjectValue as? LNDownloadManagerDelegate {
            delegate.download(self, stateChanged: state, with: target)
        }
        if let stateBlock = self.stateBlockMap[target.urlString.uc_md5] {
            stateBlock(self, state, target)
        }
    }
    
    private func excuteProgressCallback(progress: Float, receiveByte: Int64, allByte: Int64, target: LNDownloadOperation) {
        if let delegateValue = self.delegateMap[target.urlString.uc_md5],
              let delegate = delegateValue.nonretainedObjectValue as? LNDownloadManagerDelegate {
            delegate.download(self, progressChanged: progress, receiveByte: receiveByte, allByte: allByte, with: target)
        }
        if let block = self.progressBlockMap[target.urlString.uc_md5] {
            block(self, progress, receiveByte, allByte, target)
        }
    }
    
    private func cleanCallback(target: LNDownloadOperation) {
        self.delegateMap.removeValue(forKey: target.urlString.uc_md5)
        self.stateBlockMap.removeValue(forKey: target.urlString.uc_md5)
        self.progressBlockMap.removeValue(forKey: target.urlString.uc_md5)
        self.downloadMap.removeValue(forKey: target.urlString.uc_md5)
    }
    
    // 高优先级下载队列
    private lazy var highPriorityQueue: OperationQueue = {
        let queue = OperationQueue()
        queue.qualityOfService = .userInteractive  // 最高优先级
        queue.maxConcurrentOperationCount = 1
        queue.name = "HighPriorityDownloadQueue"
        return queue
    }()
    /// 下载队列
    private lazy var downloadQueue: OperationQueue = {
        let queue = OperationQueue()
        queue.maxConcurrentOperationCount = maxConcurrentDownloadCount
        queue.qualityOfService = qualityOfService
        return queue
    }()
    
    /// 下载实例列表
    private lazy var downloadMap: [String: LNDownloadOperation] = {
        let dict = [String: LNDownloadOperation]()
        return dict
    }()
    
    /// 下载代理列表
    private lazy var delegateMap: [String: NSValue] = {
        let map = [String: NSValue]()
        return map
    }()
    
    /// 状态block表
    lazy var stateBlockMap: [String: LNDownloadStateChangedBlock] = {
        let map = [String: LNDownloadStateChangedBlock]()
        return map
    }()
    
    /// 进度block表
    lazy var progressBlockMap: [String: LNDownloadProgressChangedBlock] = {
        let map = [String: LNDownloadProgressChangedBlock]()
        return map
    }()
}
