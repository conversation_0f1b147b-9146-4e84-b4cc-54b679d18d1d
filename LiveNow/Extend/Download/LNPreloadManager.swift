//
//  LNPreloadManager.swift
//  LiveNow
//
//  Created by edy on 2025/8/21.
//

import Foundation

/// 文件预加载管理类
public class LNPreloadManager: NSObject {
    
    /// 单例
    public static let shared = LNPreloadManager()
    private override init() {}
    
    /// 预加载文件，加入下载队列
    /// 如果文件已存在，不再下载
    /// - Parameter urlString: 文件下载地址
    public func preload(urlString: String) {
        guard URL(string: urlString) != nil, !urlString.isEmpty else { return }
        let filePath = LNDownloadHelper.filePath(urlString: urlString)
        if !FileManager.default.fileExists(atPath: filePath) {
            Log("[预加载开始]："+urlString)
            preloadUrlSet.add(urlString)
            downloadManager.start(with: urlString) { [weak self] (_, state, operation) in
                switch state {
                case .complete:
                    Log("[预加载结束]："+urlString)
                    self?.preloadUrlSet.remove(operation.urlString)
                default:
                    break
                }
            }
        }
    }
    
    /// 加载文件
    /// 1、如果文件已存在直接回调，不再下载
    /// 2、暂停所有文件预加载，指定高优先级单独下载，下载完成后恢复其他文件预加载
    /// - Parameters:
    ///   - urlString: 文件下载地址
    ///   - completion: 完成回调
    public func load(urlString: String, completion: ((_ result: Result<String, Error>) -> Void)?) {
        
        guard URL(string: urlString) != nil else { return }
        let filePath = LNDownloadHelper.filePath(urlString: urlString)
        if FileManager.default.fileExists(atPath: filePath) {
            completion?(.success(filePath))
            return
        }
        
        downloadManager.start(with: urlString, highPriority: true) { [weak self] (_, state, operation) in
            switch state {
            case .pause(let error):
                if let error = error {
                    completion?(.failure(error))
                }
                self?.resumePreload()
            case .complete:
                completion?(.success(operation.filePath))
                self?.resumePreload()
            default:
                break
            }
        }
    }
    
    /// 停止所有预加载
    public func stopAllPreload() {
        downloadManager.pauseAll()
        preloadUrlSet.removeAllObjects()
    }
    
    /// 暂停预加载
    private func pausePreload() {
        downloadManager.pauseAll()
    }
    
    /// 恢复预加载
    private func resumePreload() {
        downloadManager.qualityOfService = .background
        for urlString in preloadUrlSet {
            if let urlString = urlString as? String {
                preload(urlString: urlString)
            }
        }
    }
    
    /// 下载管理
    lazy var downloadManager: LNDownloadManager = {
        let manager = LNDownloadManager()
        manager.qualityOfService = .background
        return manager
    }()
    
    /// 预加载地址集合
    lazy var preloadUrlSet: NSMutableOrderedSet = {
        let list = NSMutableOrderedSet()
        return list
    }()
}
