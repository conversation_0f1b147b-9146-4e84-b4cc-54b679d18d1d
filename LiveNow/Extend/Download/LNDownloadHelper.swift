//
//  LNDownloadHelper.swift
//  LiveNow
//
//  Created by edy on 2025/8/21.
//

import Foundation
import CommonCrypto

extension String {
    
    /// 字符串MD5
    public var uc_md5: String {
        let utf8 = cString(using: .utf8)
        var digest = [UInt8](repeating: 0, count: Int(CC_MD5_DIGEST_LENGTH))
        CC_MD5(utf8, CC_LONG(utf8!.count - 1), &digest)
        return digest.reduce("") { $0 + String(format: "%02X", $1) }
    }
    /// 获取文件扩展名
    var `extension`: String {
        if let index = self.lastIndex(of: ".") {
            return String(self[index...])
        } else {
            return ""
        }
    }
}

/// 下载辅助类
class LNDownloadHelper: NSObject {
    
    /// 根据文件下载地址获取下载路劲
    /// - Parameter urlString: 文件下载地址
    /// - Returns: 发布文件存储路劲
    static func filePath(urlString: String) -> String {
        let dirPath = NSHomeDirectory().appendingFormat("/Documents/Download/")
        let fileManager = FileManager()
        if !fileManager.fileExists(atPath: dirPath, isDirectory: nil) {
            do {
                try fileManager.createDirectory(atPath: dirPath, withIntermediateDirectories: true, attributes: nil)
            } catch let error {
                print(error.localizedDescription)
            }
        }
        let filename = urlString.uc_md5
        return dirPath + filename + urlString.extension
    }
    
    /// 根据文件路劲获取文件的大小
    /// - Parameter path: 文件地址
    /// - Returns: 文件的大小
    static func fileSize(path: String) -> Int64 {
        var downloadedBytes: Int64 = 0
        let fileManager = FileManager()
        if fileManager.fileExists(atPath: path) {
            do {
                let fileDict = try fileManager.attributesOfItem(atPath: path)
                downloadedBytes = fileDict[.size] as? Int64 ?? 0
            } catch let error {
                print(error.localizedDescription)
            }
        } else {
            let fileUrl = URL(fileURLWithPath: path)
            let dirPath = fileUrl.deletingLastPathComponent().path
            if !fileManager.fileExists(atPath: dirPath, isDirectory: nil) {
                do {
                    try fileManager.createDirectory(atPath: dirPath, withIntermediateDirectories: true, attributes: nil)
                } catch let error {
                    print(error.localizedDescription)
                }
            }
            /* 文件不存在,创建文件 */
            if !fileManager.createFile(atPath: path as String, contents: nil, attributes: nil) {
                print("create File Error")
            }
        }
        return downloadedBytes
    }
    
    /// 移动文件
    /// - Parameters:
    ///   - oldPath: 文件旧路劲
    ///   - newPath: 文件新路径
    static func moveFile(oldPath: String, to newPath: String) {
        let fileManager = FileManager()
        do {
            try fileManager.moveItem(atPath: oldPath, toPath: newPath)
        } catch let error {
            print(error.localizedDescription)
        }
    }
    
    /// 根据文件地址删除已下载的文件
    /// - Parameter path: 文件地址
    static func deleteFile(path: String) {
        let fileManager = FileManager()
        do {
            try fileManager.removeItem(atPath: path)
        } catch let error {
            print(error.localizedDescription)
        }
    }
}
