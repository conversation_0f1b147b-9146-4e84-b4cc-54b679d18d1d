//
//  UIColor+Extend.swift
//  LiveNow
//
//  Created by po on 2025/8/10.
//

import UIKit

extension UIColor {
    class func hex(hexString: String) -> UIColor {
        return UIColor.hex(hexString: hexString, alpha: 1.0)
    }
    
    class func hex(hexString: String, alpha:CGFloat) -> UIColor {
        //删除字符串中的空格
        var cString: String = hexString.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // String should be 6 or 8 characters
        if cString.count < 6 { return UIColor.clear}
        
    
        let index = cString.index(cString.endIndex, offsetBy: -6)
        let subString = cString[index...]
        if cString.hasPrefix("0X") { cString = String(subString) }
        //如果是#开头的，那么截取字符串，字符串从索引为1的位置开始，一直到末尾
        if cString.hasPrefix("#") { cString = String(subString) }
        
        if cString.count != 6 { return UIColor.clear }
        // Separate into r, g, b substrings
        var range: NSRange = NSMakeRange(0, 2)
        //r
        let rString = (cString as NSString).substring(with: range)
        
        //g
        range.location = 2
        let gString = (cString as NSString).substring(with: range)
        
        //b
        range.location = 4
        let bString = (cString as NSString).substring(with: range)
        
        // Scan values
        var r: UInt32 = 0x0
        var g: UInt32 = 0x0
        var b: UInt32 = 0x0
        
        Scanner(string: rString).scanHexInt32(&r)
        Scanner(string: gString).scanHexInt32(&g)
        Scanner(string: bString).scanHexInt32(&b)
        
        return UIColor(r: r, g: g, b: b, a:alpha)
    }
    convenience init(r:UInt32 ,g:UInt32 , b:UInt32 , a:CGFloat = 1.0) {
        self.init(red: CGFloat(r) / 255.0,
                  green: CGFloat(g) / 255.0,
                  blue: CGFloat(b) / 255.0,
                  alpha: a)
    }

    convenience init(hexString: String) {
        self.init()
        let color = UIColor.hex(hexString: hexString)
        self.init(cgColor: color.cgColor)
    }

}
