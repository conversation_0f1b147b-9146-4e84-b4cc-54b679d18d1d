//
//  Header.swift
//  LiveNow
//
//  Created by po on 2025/8/10.
//

import UIKit

public var kscreenW: CGFloat { return UIScreen.main.bounds.width }
public var kscreenH: CGFloat { return UIScreen.main.bounds.height }

public var kstatusBarH: CGFloat {
    if #available(iOS 13.0, *) {
        let window: UIWindow? = UIApplication.shared.windows.first
        let statusBarHeight = (window?.windowScene?.statusBarManager?.statusBarFrame.height) ?? 0
        return statusBarHeight
    } else {
        return UIApplication.shared.statusBarFrame.height > 0 ? UIApplication.shared.statusBarFrame.height : 44
    }
}

public var knavH: CGFloat { return 44 + kstatusBarH }
public var kisIPhoneX: Bool { return kscreenH >= 812 }
public var kbottomSafeH: CGFloat { return kisIPhoneX ? 34 : 0 }
// 屏幕适配
public func s(_ p: CGFloat) -> CGFloat { return (kscreenW / 375.0) * p }

// 全局主题渐变封装
public struct LNGradient {
    /// 主按钮横向渐变颜色数组
    public static var primaryColors: [CGColor] {
        return [
            UIColor.hex(hexString: "#04E798").cgColor,
            UIColor.hex(hexString: "#0ADCE1").cgColor
        ]
    }

    /// 主按钮横向渐变的起止点
    public static var primaryStartPoint: CGPoint { CGPoint(x: 0, y: 0.5) }
    public static var primaryEndPoint: CGPoint { CGPoint(x: 1, y: 0.5) }

    /// 生成一个已配置好的横向主色渐变图层
    public static func makePrimaryHorizontalLayer() -> CAGradientLayer {
        let layer = CAGradientLayer()
        layer.colors = primaryColors
        layer.startPoint = primaryStartPoint
        layer.endPoint = primaryEndPoint
        return layer
    }

    // MARK: - 导航栏渐变（#D7FFF1 -> #EFFFEA）
    public static var navGradientColors: [UIColor] {
        return [UIColor.hex(hexString: "#D7FFF1"), UIColor.hex(hexString: "#EFFFEA")]
    }

    /// 渐变图生成（用于导航栏背景）
    public static func makeGradientImage(colors: [UIColor], size: CGSize, startPoint: CGPoint = CGPoint(x: 0, y: 0.5), endPoint: CGPoint = CGPoint(x: 1, y: 0.5)) -> UIImage? {
        guard size.width > 0 && size.height > 0 else { return nil }
        let layer = CAGradientLayer()
        layer.frame = CGRect(origin: .zero, size: size)
        layer.colors = colors.map { $0.cgColor }
        layer.startPoint = startPoint
        layer.endPoint = endPoint
        UIGraphicsBeginImageContextWithOptions(size, true, 0)
        defer { UIGraphicsEndImageContext() }
        guard let ctx = UIGraphicsGetCurrentContext() else { return nil }
        layer.render(in: ctx)
        let img = UIGraphicsGetImageFromCurrentImageContext()
        return img
    }

    /// 纯色图生成（用于导航栏背景）
    public static func makeSolidImage(color: UIColor, size: CGSize) -> UIImage? {
        guard size.width > 0 && size.height > 0 else { return nil }
        let rect = CGRect(origin: .zero, size: size)
        // 如果是透明颜色，使用透明背景
        let isOpaque = color != .clear && color.cgColor.alpha >= 1.0
        UIGraphicsBeginImageContextWithOptions(size, isOpaque, 0)
        color.setFill()
        UIRectFill(rect)
        let img = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return img
    }
}

// 统一的导航栏工具
public struct LNNav {
    /// 创建标准返回按钮（使用系统 chevron.left，自动适配深浅色）
    public static func makeBackBarButtonItem(target: Any?, action: Selector) -> UIBarButtonItem {
        let config = UIImage.SymbolConfiguration(pointSize: 18, weight: .semibold)
        let systemChevron = UIImage(systemName: "chevron.left", withConfiguration: config)
        let image = UIImage(named: "nav_back_icon") ?? systemChevron
        let item = UIBarButtonItem(image: image, style: .plain, target: target, action: action)
        item.tintColor = .label
        return item
    }
}
