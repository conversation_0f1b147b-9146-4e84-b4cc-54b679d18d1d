//
//  LNSwiftTimer.swift
//  LiveNow
//
//  Created by edy on 2025/8/8.
//

import UIKit
import Foundation

public class LNSwiftTimer {
    private let internalTimer: DispatchSourceTimer
    
    private var isRunning = false
    
    public let repeats: Bool
    
    public typealias LNSwiftTimerHandler = (LNSwiftTimer) -> Void
    
    private var handler: LNSwiftTimerHandler
    
    public init(interval: DispatchTimeInterval, repeats: Bool = false, leeway: DispatchTimeInterval = .seconds(0), queue: DispatchQueue = .main, handler: @escaping LNSwiftTimerHandler) {
        self.handler = handler
        self.repeats = repeats
        self.internalTimer = DispatchSource.makeTimerSource(queue: queue)
        internalTimer.setEventHandler { [weak self] in
            if let strongSelf = self {
                handler(strongSelf)
            }
        }
        
        if repeats {
            internalTimer.schedule(deadline: .now() + interval, repeating: interval, leeway: leeway)
        } else {
            internalTimer.schedule(deadline: .now() + interval, leeway: leeway)
        }
    }
    
    public static func repeaticTimer(interval: DispatchTimeInterval, leeway: DispatchTimeInterval = .seconds(0), queue: DispatchQueue = .main, handler: @escaping LNSwiftTimerHandler) -> LNSwiftTimer {
        return LNSwiftTimer(interval: interval, repeats: true, leeway: leeway, queue: queue, handler: handler)
    }
    
    deinit {
        if !self.isRunning {
            internalTimer.resume()
        }
    }
    
    // You can use this method to fire a repeating timer without interrupting its regular firing schedule. If the timer is non-repeating, it is automatically invalidated after firing, even if its scheduled fire date has not arrived.
    public func fire() {
        if repeats {
            handler(self)
        } else {
            handler(self)
            internalTimer.cancel()
        }
    }
    
    public func cancel() {
        internalTimer.cancel()
    }
    
    public func start() {
        if !isRunning {
            internalTimer.resume()
            isRunning = true
        }
    }
    
    public func suspend() {
        if isRunning {
            internalTimer.suspend()
            isRunning = false
        }
    }
    
    public func rescheduleRepeating(interval: DispatchTimeInterval) {
        if repeats {
            internalTimer.schedule(deadline: .now() + interval, repeating: interval)
        }
    }
    
    public func rescheduleHandler(handler: @escaping LNSwiftTimerHandler) {
        self.handler = handler
        internalTimer.setEventHandler { [weak self] in
            if let strongSelf = self {
                handler(strongSelf)
            }
        }
    }
}


public class LNSwiftCountDownTimer {
    private let internalTimer: LNSwiftTimer
    
    private var leftTimes: Int
    
    private let originalTimes: Int
    
    private let handler: (LNSwiftCountDownTimer, _ leftTimes: Int) -> Void
    
    public init(interval: DispatchTimeInterval, times: Int, queue: DispatchQueue = .main, handler: @escaping (LNSwiftCountDownTimer, _ leftTimes: Int) -> Void) {
        self.leftTimes = times
        self.originalTimes = times
        self.handler = handler
        self.internalTimer = LNSwiftTimer.repeaticTimer(interval: interval, queue: queue, handler: { _ in
        })
        internalTimer.rescheduleHandler { [weak self] _ in
            if let strongSelf = self {
                if strongSelf.leftTimes > 0 {
                    strongSelf.leftTimes -= 1
                    strongSelf.handler(strongSelf, strongSelf.leftTimes)
                } else {
                    strongSelf.internalTimer.suspend()
                }
            }
        }
    }
    
    public func start() {
        internalTimer.start()
    }
    
    public func suspend() {
        internalTimer.suspend()
    }
    
    public func reCountDown() {
        leftTimes = originalTimes
    }
    
    public func recountTime(time: Int) {
        leftTimes = time
    }
    
    public func finish() {
        internalTimer.fire()
    }
    
    public func cancel() {
        internalTimer.cancel()
    }
}

public extension DispatchTimeInterval {
    static func fromSeconds(_ seconds: Double) -> DispatchTimeInterval {
        return .milliseconds(Int(seconds * 1000))
    }
}
