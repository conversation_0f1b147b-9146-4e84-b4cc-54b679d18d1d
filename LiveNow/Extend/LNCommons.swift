//
//  LNCommons.swift
//  LiveNow
//
//  Created by pp on 2025/8/12.
//

import UIKit
import Foundation
import Security
import JKSwiftExtension

class LNCommons: NSObject {
    
    // get IDFV
    class func lngetIDFVString() -> String? {
        return UIDevice.current.identifierForVendor?.uuidString
    }

    // get UUID
    class func lngetUUIDString() -> String {
        return NSUUID().uuidString
    }
    
    class func lngetDeviceIdString() -> String {
        let cache = LNKeyChains.value(service: JKPOP.appBundleIdentifier, account: "live_now")

        if cache.count > 0 {
            return cache
        } else {
            let newValue = lngetIDFVString() ?? lngetUUIDString()
            _ = LNKeyChains.setValue(newValue, service: JKPOP.appBundleIdentifier, account: "live_now")
            return newValue
        }
    }

}






public enum KeyChainHandleError: Error {
    case itemNotFound
    case duplicateItem
    case invalidItemFormat
    case unexpectedStatus(OSStatus)
}

class LNKeyChains: NSObject {
    
    public static func value(service name: String, account: String) -> String {
        if let data = self.value(data: name, account: account) {
            return String(data: data, encoding: .utf8) ?? ""
        }
        return ""
    }

    public static func value(data name: String, account: String) -> Data? {
        do {
            let data: Data = try value(data: name, account: account)
            return data
        } catch {}
        return nil
    }

    public static func value(data name: String, account: String) throws -> Data {
        let query: [String: AnyObject] = [
            kSecAttrService as String: name as AnyObject,
            kSecAttrAccount as String: account as AnyObject,
            kSecMatchLimit as String: kSecMatchLimitOne,
            kSecReturnData as String: kCFBooleanTrue,
            kSecClass as String: kSecClassGenericPassword
        ]
        
        var itemCopy: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &itemCopy)
        
        if status == errSecDuplicateItem {
            throw KeyChainHandleError.duplicateItem
        }
        
        guard status == errSecSuccess else {
            throw KeyChainHandleError.unexpectedStatus(status)
        }
        
        guard let data = itemCopy as? Data else {
            throw KeyChainHandleError.invalidItemFormat
        }
        
        return data
    }

    public static func setValue(_ value: String, service name: String, account: String) -> Bool {
        if let data = value.data(using: .utf8) {
            return self.setValue(data: data, service: name, account: account)
        }
        return false
    }
    
    @discardableResult
    public static func setValue(data: Data, service name: String, account: String) -> Bool {
        do {
            return try setValue(data, service: name, account: account)
        } catch {}
        return false
    }
    
    @discardableResult
    public static func setValue(_ data: Data, service name: String, account: String) throws -> Bool {
        var query = [
            kSecAttrService as String: name as AnyObject,
            kSecAttrAccount as String: account as AnyObject,
            kSecClass as String: kSecClassGenericPassword
        ]
        let value: Data? = self.value(data: name, account: account)
        if value == data {
            return true
        }
        var status: OSStatus!
        if value == nil || value!.isEmpty {
            query[kSecValueData as String] = data as AnyObject
            status = SecItemAdd(query as CFDictionary, nil)
        } else {
            let attributes = [
                kSecValueData as String: data as AnyObject
            ]
            status = SecItemUpdate(query as CFDictionary, attributes as CFDictionary)
        }
        if status == errSecDuplicateItem {
            throw KeyChainHandleError.duplicateItem
        }
        
        guard status == errSecSuccess else {
            throw KeyChainHandleError.unexpectedStatus(status)
        }
        return true
    }
}
