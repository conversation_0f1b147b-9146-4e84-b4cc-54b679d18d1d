//
//  LNInit.swift
//  LiveNow
//
//  Created by edy on 2025/8/8.
//

// 基础参数管理
import JKSwiftExtension

// 声网
let agoraAppId: String = "7ddec5e2fc9247c8ac199947cd448625"

//融云Key
let kRongIMKey: String = "0vnjpoad0t9oz"
let kRongIMUrl: String = "api-sg01.ronghub.com"


public func Log(_ string: Any..., className: String = #file, line: Int = #line, funcName: String = #function) {
#if DEBUG
    
    let string = string.compactMap { "\($0)" }.joined(separator: " ")
    print("打印日志:\(JKPOP.currentDate.jk.toformatterTimeString(formatter: "MM-dd HH:mm:ss"))-\((className as NSString).lastPathComponent)[\(line)] - \(funcName): \(string)")
#endif
}
