//
//  LNImageLoader.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/23.
//

import UIKit
import Kingfisher

/// 统一的图片加载工具类
/// 基于Kingfisher封装，提供项目中所有远程图片加载的统一接口
class LNImageLoader {
    
    /// 单例
    static let shared = LNImageLoader()
    private init() {}
    
    // MARK: - 配置选项
    
    /// 默认占位图
    static let defaultPlaceholder = UIImage(named: "default_avatar")
    
    /// 默认加载选项
    private var defaultOptions: KingfisherOptionsInfo {
        return [
            .transition(.fade(0.3)),                    // 淡入动画
            .cacheOriginalImage,                        // 缓存原图
            .scaleFactor(UIScreen.main.scale),          // 适配屏幕密度
            .processor(DownsamplingImageProcessor(size: CGSize(width: 300, height: 300))), // 图片压缩
            .cacheSerializer(FormatIndicatedCacheSerializer.png) // PNG格式缓存
        ]
    }
    
    /// 头像加载选项
    private var avatarOptions: KingfisherOptionsInfo {
        return [
            .transition(.fade(0.3)),
            .cacheOriginalImage,
            .scaleFactor(UIScreen.main.scale),
            .processor(RoundCornerImageProcessor(cornerRadius: 8)), // 圆角处理
            .cacheSerializer(FormatIndicatedCacheSerializer.png)
        ]
    }
    
    /// 缩略图加载选项
    private var thumbnailOptions: KingfisherOptionsInfo {
        return [
            .transition(.fade(0.2)),
            .cacheOriginalImage,
            .scaleFactor(UIScreen.main.scale),
            .processor(DownsamplingImageProcessor(size: CGSize(width: 150, height: 150))),
            .cacheSerializer(FormatIndicatedCacheSerializer.jpeg(compressionQuality: 0.8))
        ]
    }
}

// MARK: - 主要加载方法
extension LNImageLoader {
    
    /// 通用图片加载方法
    /// - Parameters:
    ///   - imageView: 目标ImageView
    ///   - url: 图片URL字符串
    ///   - placeholder: 占位图（可选）
    ///   - completion: 完成回调（可选）
    @MainActor static func loadImage(
        _ imageView: UIImageView,
        url: String?,
        placeholder: UIImage? = defaultPlaceholder,
        completion: ((Result<RetrieveImageResult, KingfisherError>) -> Void)? = nil
    ) {
        shared.loadImage(imageView, url: url, placeholder: placeholder, options: shared.defaultOptions, completion: completion)
    }
    
    /// 加载头像图片
    /// - Parameters:
    ///   - imageView: 目标ImageView
    ///   - url: 头像URL字符串
    ///   - placeholder: 占位图（可选，默认使用头像占位图）
    ///   - completion: 完成回调（可选）
    @MainActor static func loadAvatar(
        _ imageView: UIImageView,
        url: String?,
        placeholder: UIImage? = UIImage(named: "default_avatar"),
        completion: ((Result<RetrieveImageResult, KingfisherError>) -> Void)? = nil
    ) {
        shared.loadImage(imageView, url: url, placeholder: placeholder, options: shared.avatarOptions, completion: completion)
    }
    
    /// 加载缩略图
    /// - Parameters:
    ///   - imageView: 目标ImageView
    ///   - url: 图片URL字符串
    ///   - placeholder: 占位图（可选）
    ///   - completion: 完成回调（可选）
    @MainActor static func loadThumbnail(
        _ imageView: UIImageView,
        url: String?,
        placeholder: UIImage? = UIImage(named: "default_thumbnail"),
        completion: ((Result<RetrieveImageResult, KingfisherError>) -> Void)? = nil
    ) {
        shared.loadImage(imageView, url: url, placeholder: placeholder, options: shared.thumbnailOptions, completion: completion)
    }
    
    /// 加载封面图片
    /// - Parameters:
    ///   - imageView: 目标ImageView
    ///   - url: 封面URL字符串
    ///   - placeholder: 占位图（可选）
    ///   - completion: 完成回调（可选）
    @MainActor static func loadCover(
        _ imageView: UIImageView,
        url: String?,
        placeholder: UIImage? = UIImage(named: "default_cover"),
        completion: ((Result<RetrieveImageResult, KingfisherError>) -> Void)? = nil
    ) {
        let coverOptions: KingfisherOptionsInfo = [
            .transition(.fade(0.3)),
            .cacheOriginalImage,
            .scaleFactor(UIScreen.main.scale),
            .processor(DownsamplingImageProcessor(size: CGSize(width: 400, height: 300))),
            .cacheSerializer(FormatIndicatedCacheSerializer.jpeg(compressionQuality: 0.9))
        ]
        shared.loadImage(imageView, url: url, placeholder: placeholder, options: coverOptions, completion: completion)
    }
}

// MARK: - 核心实现
extension LNImageLoader {
    
    /// 核心图片加载方法
    @MainActor private func loadImage(
        _ imageView: UIImageView,
        url: String?,
        placeholder: UIImage?,
        options: KingfisherOptionsInfo,
        completion: ((Result<RetrieveImageResult, KingfisherError>) -> Void)?
    ) {
        // 设置占位图
        imageView.image = placeholder
        
        // 检查URL有效性
        guard let urlString = url, !urlString.isEmpty else {
            Log("图片URL为空，使用占位图")
            return
        }
        
        // 创建URL对象
        guard let imageURL = URL(string: urlString) else {
            Log("无效的图片URL: \(urlString)")
            return
        }
        
        // 使用Kingfisher加载图片
        DispatchQueue.main.async {
            imageView.kf.setImage(
                with: imageURL,
                placeholder: placeholder,
                options: options,
                completionHandler: { result in
                    switch result {
                    case .success(let value):
                        Log("图片加载成功: \(imageURL)")
                        completion?(.success(value))
                    case .failure(let error):
                        Log("图片加载失败: \(error.localizedDescription)")
                        completion?(.failure(error))
                    }
                }
            )
        }
    }
}

// MARK: - 缓存管理
extension LNImageLoader {
    
    /// 清除所有图片缓存
    static func clearCache() {
        KingfisherManager.shared.cache.clearMemoryCache()
        KingfisherManager.shared.cache.clearDiskCache()
        Log("图片缓存已清除")
    }
    
    /// 清除内存缓存
    static func clearMemoryCache() {
        KingfisherManager.shared.cache.clearMemoryCache()
        Log("内存缓存已清除")
    }
    
    /// 获取缓存大小
    static func getCacheSize(completion: @escaping (UInt) -> Void) {
        KingfisherManager.shared.cache.calculateDiskStorageSize { result in
            switch result {
            case .success(let size):
                completion(size)
            case .failure:
                completion(0)
            }
        }
    }
    
    /// 预加载图片
    /// - Parameter urls: 图片URL数组
    static func preloadImages(_ urls: [String]) {
        let validURLs = urls.compactMap { URL(string: $0) }
        let prefetcher = ImagePrefetcher(urls: validURLs)
        prefetcher.start()
        Log("开始预加载 \(validURLs.count) 张图片")
    }
}

// MARK: - 便捷扩展
extension UIImageView {
    
    /// 加载远程图片（便捷方法）
    /// - Parameters:
    ///   - url: 图片URL字符串
    ///   - placeholder: 占位图
    func ln_setImage(url: String?, placeholder: UIImage? = LNImageLoader.defaultPlaceholder) {
        LNImageLoader.loadImage(self, url: url, placeholder: placeholder)
    }
    
    /// 加载头像图片（便捷方法）
    /// - Parameters:
    ///   - url: 头像URL字符串
    ///   - placeholder: 占位图
    func ln_setAvatar(url: String?, placeholder: UIImage? = UIImage(named: "default_avatar")) {
        LNImageLoader.loadAvatar(self, url: url, placeholder: placeholder)
    }
    
    /// 加载缩略图（便捷方法）
    /// - Parameters:
    ///   - url: 图片URL字符串
    ///   - placeholder: 占位图
    func ln_setThumbnail(url: String?, placeholder: UIImage? = UIImage(named: "default_thumbnail")) {
        LNImageLoader.loadThumbnail(self, url: url, placeholder: placeholder)
    }
    
    /// 加载封面图片（便捷方法）
    /// - Parameters:
    ///   - url: 封面URL字符串
    ///   - placeholder: 占位图
    func ln_setCover(url: String?, placeholder: UIImage? = UIImage(named: "default_cover")) {
        LNImageLoader.loadCover(self, url: url, placeholder: placeholder)
    }
}
