//
//  NSDate+Extend.swift
//  LiveNow
//
//  Created by po on 2025/8/24.
//

import Foundation

extension Date {
    static func timeString(timeInterval: TimeInterval) -> String {
        let date = Date(timeIntervalSince1970: timeInterval)
        let formatter = DateFormatter()
        if date.isToday() {
            // 是今天
            formatter.dateFormat = "HH:mm"
            return formatter.string(from: date)
            
        } else if date.isYesterday() {
            // 是昨天.yesterday
            formatter.dateFormat = "HH:mm"
            let timeStr = formatter.string(from: date)
            let dataStr = "yesterday \(timeStr)"
            
            return dataStr
        } else if date.isSameWeek() {
            // 是同一周
            let week = date.weekdayStringFromDate()
            formatter.dateFormat = "HH:mm"
            let timeStr = formatter.string(from: date)
            let weekStr = "\(week) \(timeStr)"
            return weekStr
        } else {
            formatter.dateFormat = "MM/dd/yyyy"
            return formatter.string(from: date)
        }
    }
    func isToday() -> Bool {
        let calendar = Calendar.current
        // 当前时间
        let nowComponents = calendar.dateComponents([.day, .month, .year], from: Date() )
        // self
        let selfComponents = calendar.dateComponents([.day, .month, .year], from: self as Date)
        
        return (selfComponents.year == nowComponents.year) && (selfComponents.month == nowComponents.month) && (selfComponents.day == nowComponents.day)
    }
    
    func isYesterday() -> Bool {
        let calendar = Calendar.current
        // 当前时间
        let nowComponents = calendar.dateComponents([.day], from: Date() )
        // self
        let selfComponents = calendar.dateComponents([.day], from: self as Date)
        let cmps = calendar.dateComponents([.day], from: selfComponents, to: nowComponents)
        return cmps.day == 1
        
    }
    
    func isSameWeek() -> Bool {
        let calendar = Calendar.current
        // 当前时间
        let nowComponents = calendar.dateComponents([.day, .month, .year], from: Date() )
        // self
        let selfComponents = calendar.dateComponents([.weekday, .month, .year], from: self as Date)
        
        return (selfComponents.year == nowComponents.year) && (selfComponents.month == nowComponents.month) && (selfComponents.weekday == nowComponents.weekday)
    }
    
    func weekdayStringFromDate() -> String {
        let weekdays: NSArray = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
        let calendar = Calendar.init(identifier: .gregorian)
        let theComponents = calendar.dateComponents([.weekday], from: self as Date)
        return weekdays.object(at: theComponents.weekday!) as! String
    }
}
