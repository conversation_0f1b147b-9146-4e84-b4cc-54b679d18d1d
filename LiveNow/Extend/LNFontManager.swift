//
//  LNFontManager.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/12.
//

import UIKit

/// 全局字体族管理与便捷方法
public enum LNFont {
    /// 自定义字体家族名称（与 TTF 内部 family 保持一致）
    private static let familyName: String = "HarmonyOS Sans"
    
    /// 常规
    public static func regular(_ size: CGFloat) -> UIFont {
        return makeFont(weight: .regular, size: s(size))
    }
    
    /// Medium
    public static func medium(_ size: CGFloat) -> UIFont {
        return makeFont(weight: .medium, size: s(size))
    }
    
    /// Bold
    public static func bold(_ size: CGFloat) -> UIFont {
        return makeFont(weight: .bold, size: s(size))
    }
    
    /// Black
    public static func black(_ size: CGFloat) -> UIFont {
        return makeFont(weight: .black, size: s(size))
    }
    
    /// 文本样式（支持动态字体）
    public static func forTextStyle(_ style: UIFont.TextStyle, weight: UIFont.Weight = .regular) -> UIFont {
        // 基于 textStyle 的 pointSize 推导一个基准尺寸
        let metrics = UIFontMetrics(forTextStyle: style)
        // 参照系统 17 作为默认基准尺寸
        let base = makeFont(weight: weight, size: s(17))
        return metrics.scaledFont(for: base)
    }
    
    private static func makeFont(weight: UIFont.Weight, size: CGFloat) -> UIFont {
        // 使用 family + traits 生成特定字重
        let baseDescriptor = UIFontDescriptor(fontAttributes: [UIFontDescriptor.AttributeName.family: familyName])
        let traits: [UIFontDescriptor.TraitKey: Any] = [UIFontDescriptor.TraitKey.weight: weight]
        let descriptor = baseDescriptor.addingAttributes([UIFontDescriptor.AttributeName.traits: traits])
        let candidate = UIFont(descriptor: descriptor, size: size)

        // 如果生成的是目标家族，直接返回未缩放字体（避免后续再次缩放导致崩溃）
        if candidate.familyName.range(of: "Harmony", options: .caseInsensitive) != nil ||
            candidate.familyName.range(of: "HarmonyOS", options: .caseInsensitive) != nil {
            return candidate
        }

        let fallback: UIFont
        switch weight {
        case .medium: fallback = .systemFont(ofSize: size, weight: .medium)
        case .bold: fallback = .systemFont(ofSize: size, weight: .bold)
        case .black: fallback = .systemFont(ofSize: size, weight: .black)
        default: fallback = .systemFont(ofSize: size, weight: .regular)
        }
        // 回退系统字体同样返回未缩放版本；仅在 forTextStyle 中进行动态缩放
        return fallback
    }
}

/// 负责一次性接入全局字体与外观
public final class LNFontManager {
    public static let shared = LNFontManager()
    private init() {}
    
    /// 在应用启动时调用，一次性生效
    public func activate() {
        // 动态保证字体已注册（Info.plist 配置后通常会自动注册，这里仅做健壮性校验/调试）
#if DEBUG
        debugPrintAvailableHarmonyFonts()
#endif
        
        // 不再使用运行时递归替换，统一通过代码处显式指定 LNFont
        
        // 导航栏/TabBar/BarButtonItem 全局外观
        let navTitleAttrs: [NSAttributedString.Key: Any] = [
            .font: LNFont.medium(18)
        ]
        UINavigationBar.appearance().titleTextAttributes = navTitleAttrs
        let appearance = UINavigationBarAppearance()
        appearance.configureWithDefaultBackground()
        appearance.titleTextAttributes = navTitleAttrs
        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance
        
        UIBarButtonItem.appearance().setTitleTextAttributes([.font: LNFont.regular(16)], for: .normal)
        UIBarButtonItem.appearance().setTitleTextAttributes([.font: LNFont.regular(16)], for: .highlighted)
        
        UITabBarItem.appearance().setTitleTextAttributes([.font: LNFont.regular(11)], for: .normal)
        UITabBarItem.appearance().setTitleTextAttributes([.font: LNFont.regular(11)], for: .selected)
    }
    
#if DEBUG
    private func debugPrintAvailableHarmonyFonts() {
        let families = UIFont.familyNames.sorted()
        let harmonyFamilies = families.filter { $0.lowercased().contains("harmony") }
        if harmonyFamilies.isEmpty {
            print("[LNFontManager] 未发现 Harmony 字体家族，请确认 Info.plist 中 UIAppFonts 配置是否正确。")
        } else {
            print("[LNFontManager] 可用字体家族: \(harmonyFamilies)")
            for family in harmonyFamilies {
                let names = UIFont.fontNames(forFamilyName: family)
                print("[LNFontManager] \(family) -> \(names)")
            }
        }
    }
#endif
}

// MARK: - 递归替换字体（安全）
extension LNFontManager {
    /// 替换指定视图树中的字体
    public func applyFonts(in rootView: UIView) {
        replaceFontIfNeeded(for: rootView)
        rootView.subviews.forEach { applyFonts(in: $0) }
    }

    /// 对所有窗口视图进行一次替换（应用启动后可调用一次）
    public func applyFontsForAllWindows() {
        UIApplication.shared.windows.forEach { window in
            applyFonts(in: window)
        }
    }

    private func replaceFontIfNeeded(for view: UIView) {
        switch view {
        case let label as UILabel:
            guard let currentFont = label.font else { return }
            if currentFont.familyName.range(of: "Harmony", options: .caseInsensitive) == nil {
                let traits = currentFont.fontDescriptor.symbolicTraits
                let weight: UIFont.Weight = traits.contains(.traitBold) ? .bold : .regular
                label.font = (weight == .bold) ? LNFont.bold(currentFont.pointSize) : LNFont.regular(currentFont.pointSize)
                label.adjustsFontForContentSizeCategory = true
            }
        case let button as UIButton:
            if let label = button.titleLabel, let currentFont = label.font,
               currentFont.familyName.range(of: "Harmony", options: .caseInsensitive) == nil {
                let isBold = currentFont.fontDescriptor.symbolicTraits.contains(.traitBold)
                label.font = isBold ? LNFont.medium(currentFont.pointSize) : LNFont.regular(currentFont.pointSize)
                label.adjustsFontForContentSizeCategory = true
            }
        case let textField as UITextField:
            if let currentFont = textField.font,
               currentFont.familyName.range(of: "Harmony", options: .caseInsensitive) == nil {
                textField.font = LNFont.regular(currentFont.pointSize)
                textField.adjustsFontForContentSizeCategory = true
            }
        case let textView as UITextView:
            if let currentFont = textView.font,
               currentFont.familyName.range(of: "Harmony", options: .caseInsensitive) == nil {
                textView.font = LNFont.regular(currentFont.pointSize)
                textView.adjustsFontForContentSizeCategory = true
            }
        default:
            break
        }
    }
}


