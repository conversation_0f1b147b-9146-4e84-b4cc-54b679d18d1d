//
//  AppDelegate.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/7/28.
//

import UIKit
import Kingfisher

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?
    static let shared = UIApplication.shared.delegate as! AppDelegate

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        
        setUps()
        
        window = UIWindow(frame: UIScreen.main.bounds)
        window?.backgroundColor = .white
        if #available(iOS 13.0, *) {
            self.window?.overrideUserInterfaceStyle = .light
        }
        resetTabVC()
        
        return true
    }

    func resetTabVC() {
        if LNUserManager.shared.isLogin() {
            
            let rootVc = self.window?.rootViewController
            let vc = LNTabBarController()
//            let nav = UINavigationController(rootViewController: vc)
            self.window?.rootViewController = vc
//            self.startTransitionAnimation(old: rootVc, new: nav)
            
        } else {
            let login = LNLoginViewController()
            let nav = LNNavigationViewController(rootViewController: login)
            window?.rootViewController = nav
        }
        
        // 显示窗口
        window?.makeKeyAndVisible()
    }
    
    private func setUps() {
        // 全局字体接入
        LNFontManager.shared.activate()

        // 配置Kingfisher图片缓存
        setupKingfisher()

        // Override point for customization after application launch.
        LNAgoraManager.shared.initRtcEngine()
        //融云
        LNMessageManager.shared.configIMSDK()
        
        LNUserManager.shared.loadUserInfo()
    }

    /// 配置Kingfisher图片加载框架
    private func setupKingfisher() {
        let cache = ImageCache.default

        // 内存缓存配置 - 100MB
        cache.memoryStorage.config.totalCostLimit = 100 * 1024 * 1024

        // 磁盘缓存配置 - 500MB，7天过期
        cache.diskStorage.config.sizeLimit = 500 * 1024 * 1024
        cache.diskStorage.config.expiration = .days(7)

        // 网络配置
        KingfisherManager.shared.downloader.downloadTimeout = 30.0

        Log("Kingfisher配置完成 - 内存缓存:100MB, 磁盘缓存:500MB, 过期时间:7天")
    }

    private func startTransitionAnimation(old: UIViewController?, new: UIViewController?) {
        guard let old = old, let new = new else { return }
        let duration = TimeInterval(UINavigationController.hideShowBarDuration)
        UIView.transition(from: old.view, to: new.view, duration: duration, options: .transitionCrossDissolve)
    }
}

