# 数据模型更新说明

## 概述

根据 `popularList`、`newList`、`followList` 三个接口返回的数据格式，我们更新了相关的数据模型以确保完全匹配API返回的数据结构。

**重要更新（2025/8/23）**: 已移除 `LNLiveStreamModel`，统一使用 `LNAnchorModel` 简化数据模型结构。

## API 返回数据格式

```json
{
  "code": 0,
  "data": {
    "current": 0,
    "hitCount": true,
    "pages": 0,
    "records": [
      {
        "age": 0,
        "birthday": "",
        "constellation": 0,
        "country": "",
        "coverVideoUrl": "",
        "followFlag": "",
        "gender": "",
        "groundFileName": "",
        "headFileName": "",
        "hotAnchor": "",
        "id": 0,
        "incomeDiamond": 0,
        "isNeedMute": true,
        "isTop": "",
        "isVirVideo": "",
        "language": "",
        "needMuteStr": "",
        "nickName": "",
        "normalHeadFileName": "",
        "onlineStatus": "",
        "repeatId": 0,
        "roomTitle": "",
        "showVideoUrl": "",
        "source": 0,
        "unionId": 0,
        "userCode": "",
        "videoPrice": 0,
        "vipFlag": "",
        "virVideoId": 0
      }
    ],
    "searchCount": true,
    "size": 0,
    "total": 0
  },
  "msg": "",
  "success": true
}
```

## 更新内容

### 1. LNUserModel.swift 更新

**主要变更：**
- `constellation`: `String` → `Int` (匹配API返回类型)
- `gender`: `Int` → `String` (匹配API返回类型)
- 新增字段：
  - `groundFileName: String`
  - `hotAnchor: String`
  - `isNeedMute: Bool`
  - `isTop: String`
  - `isVirVideo: String`
  - `language: String`
  - `needMuteStr: String`
  - `normalHeadFileName: String`
  - `repeatId: Int`
  - `roomTitle: String`
  - `source: Int`
  - `virVideoId: Int`

### 2. LNLiveStreamModel.swift 新增模型

**新增了专门的主播数据模型：**

#### LNAnchorModel
- 直接匹配API返回的数据结构
- 包含所有API字段
- 提供计算属性用于UI显示

#### LNAnchorListResponse
- 分页响应数据模型
- 包含 `records: [LNAnchorModel]` 数组

#### LNAnchorApiResponse
- 完整的API响应模型
- 包含 `code`、`data`、`msg`、`success` 字段

### 3. LNAnchorListViewModel.swift 更新

**主要变更：**
- 使用新的 `LNAnchorListResponse` 替代 `LNPageResponse<LNUserModel>`
- 使用 `LNLiveStreamModel(from: anchorModel)` 进行数据转换
- 移除了旧的 `convertUserToLiveStream` 方法

## 使用方法

### 数据转换示例

```swift
// API响应解析
if let dataDict = result["data"] as? [String: Any],
   let jsonData = try? JSONSerialization.data(withJSONObject: dataDict),
   let pageResponse = LNAnchorListResponse.deserialize(from: String(data: jsonData, encoding: .utf8)) {
    
    // 转换为UI显示模型
    let liveStreams = pageResponse.records.map { anchorModel in
        LNLiveStreamModel(from: anchorModel)
    }
}
```

### 计算属性使用

```swift
let anchorModel = LNAnchorModel()
// 使用计算属性
let status = anchorModel.userStatus        // LNUserStatus枚举
let flag = anchorModel.countryFlag         // 国家旗帜emoji
let name = anchorModel.displayName         // 显示名称
let price = anchorModel.priceDisplay       // 价格显示
let avatar = anchorModel.avatarName        // 头像名称
let hasVideo = anchorModel.hasVideoCall    // 是否支持视频
let hasGift = anchorModel.hasGift          // 是否有礼物
```

## 兼容性

- 保持了原有的 `LNLiveStreamModel` 结构，确保UI层代码无需修改
- 提供了两种初始化方式：
  1. `init(from anchorModel: LNAnchorModel)` - 从新模型转换
  2. 原有的完整参数初始化方法 - 保持向后兼容

## 注意事项

1. **类型匹配**：确保所有字段类型与API返回完全一致
2. **空值处理**：所有字段都有默认值，避免解析失败
3. **计算属性**：业务逻辑封装在模型内部，便于维护
4. **HandyJSON**：使用HandyJSON进行JSON解析，支持自动映射

## 测试建议

1. 测试三个接口的数据解析是否正常
2. 验证UI显示是否正确
3. 检查分页功能是否工作正常
4. 确认所有新增字段都能正确解析
