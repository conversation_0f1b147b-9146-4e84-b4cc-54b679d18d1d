//
//  LNSimplifiedModelExample.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/23.
//

import Foundation
import UIKit

/// 简化数据模型使用示例
/// 展示如何使用统一的LNAnchorModel
class LNSimplifiedModelExample {
    
    /// 示例：创建测试数据
    static func createTestAnchor() -> LNAnchorModel {
        let anchor = LNAnchorModel()
        anchor.id = 12345
        anchor.nickName = "Sophia"
        anchor.country = "Indonesia"
        anchor.onlineStatus = "1"
        anchor.videoPrice = 50
        anchor.headFileName = "avatar_001.jpg"
        anchor.incomeDiamond = 1500
        return anchor
    }
    
    /// 示例：在UI中使用LNAnchorModel
    static func configureCell(_ cell: LNLiveStreamCell, with anchor: LNAnchorModel) {
        cell.configure(with: anchor)
    }
    
    /// 示例：显示模型属性
    static func printAnchorInfo(_ anchor: LNAnchorModel) {
        print("=== 主播信息 ===")
        print("ID: \(anchor.id)")
        print("显示名称: \(anchor.displayName)")
        print("国家: \(anchor.country) -> \(anchor.countryName)")
        print("在线状态: \(anchor.onlineStatus) -> \(anchor.userStatus.displayText)")
        print("价格: \(anchor.videoPrice) -> \(anchor.priceDisplay)")
        print("头像: \(anchor.headFileName) -> \(anchor.avatarName)")
        print("支持视频: \(anchor.hasVideoCall)")
        print("有礼物: \(anchor.hasGift)")
        print("===============")
    }
    
    /// 示例：测试不同国家
    static func testCountries() {
        let countries = ["Indonesia", "Philippines", "Vietnam", "Thailand", "Palestine", "Jordan", "Unknown"]
        
        for country in countries {
            let anchor = LNAnchorModel()
            anchor.country = country
            print("\(country) -> \(anchor.countryName)")
        }
    }
    
    /// 示例：测试不同状态
    static func testStatuses() {
        let statuses = ["0", "1", "2"]
        let statusNames = ["离线", "在线", "忙碌"]
        
        for (index, status) in statuses.enumerated() {
            let anchor = LNAnchorModel()
            anchor.onlineStatus = status
            print("\(statusNames[index])(\(status)) -> \(anchor.userStatus.displayText)")
        }
    }
}

// MARK: - 使用说明
/*
 简化后的数据模型使用方式：
 
 1. 创建测试数据：
    let anchor = LNSimplifiedModelExample.createTestAnchor()
 
 2. 配置UI：
    LNSimplifiedModelExample.configureCell(cell, with: anchor)
 
 3. 显示信息：
    LNSimplifiedModelExample.printAnchorInfo(anchor)
 
 4. 测试功能：
    LNSimplifiedModelExample.testCountries()
    LNSimplifiedModelExample.testStatuses()
 */
