//
//  LNHomeViewController.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/7/28.
//

import UIKit
import SnapKit
import JKSwiftExtension
import JXSegmentedView

/// 首页视图控制器 - 展示直播内容
class LNHomeViewController: LNBaseController {

    // 隐藏导航栏
    override var prefersNavigationBarHide: Bool { return true }

    var numbers = [0, Int.random(in: 30...90), 0]

    // MARK: - UI Elements
    // 顶部背景图片视图
    private lazy var topBackgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_home_top")
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        return imageView
    }()
    // 分段控制器
    private lazy var segmentedDataSource: JXSegmentedNumberDataSource = {
        let dataSource = JXSegmentedNumberDataSource()
        dataSource.titles = ["HOT", "NEW", "FOLLOW"]
        dataSource.titleNormalFont = LNFont.black(18)
        dataSource.titleSelectedFont = LNFont.bold(18)
        dataSource.titleNormalColor = UIColor(hexString: "#B3EBE0")
        dataSource.titleSelectedColor = UIColor(hexString: "#00DFAB")
        dataSource.isTitleColorGradientEnabled = true
        dataSource.isItemSpacingAverageEnabled = false
        dataSource.itemSpacing = 50
        dataSource.numbers = numbers
        dataSource.numberStringFormatterClosure = {(number) -> String in
            if number > 99 {
                return "99+"
            }
            return "\(number)"
        }
        dataSource.numberOffset = CGPoint(x: 12, y: 8)
        return dataSource
    }()

    private lazy var segmentedView: JXSegmentedView = {
        let segmentedView = JXSegmentedView()
        segmentedView.backgroundColor = .clear
        segmentedView.dataSource = segmentedDataSource
        segmentedView.delegate = self

        // 指示器
        let indicator = JXSegmentedIndicatorImageView()
        indicator.indicatorWidth = s(26)
        indicator.indicatorHeight = s(10)
        indicator.image = UIImage(named: "ic_home_seg")
        indicator.indicatorCornerRadius = s(1.5)
        segmentedView.indicators = [indicator]
        return segmentedView
    }()

    // 内容容器
    private lazy var listContainerView: JXSegmentedListContainerView = {
        let containerView = JXSegmentedListContainerView(dataSource: self)
        return containerView
    }()

    // 爱心图标容器视图
    private lazy var heartContainerView: UIView = {
        let view = UIView()
        view.isUserInteractionEnabled = true
        return view
    }()

    // 爱心图标
    private lazy var heartImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_home_likeme")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    // 红色数字标识
    private lazy var heartBadgeLabel: UILabel = {
        let label = UILabel()
        // 生成30-90之间的随机数
        let randomNumber = Int.random(in: 30...90)
        label.text = "\(randomNumber)"
        label.textColor = .white
        label.backgroundColor = UIColor(hexString: "#FF4757")
        label.font = LNFont.medium(10)
        label.textAlignment = .center
        label.layer.cornerRadius = s(8)
        label.clipsToBounds = true
        return label
    }()

    // 子视图
    private lazy var hotView = LNHotView()
    private lazy var newView = LNNewView()
    private lazy var followView = LNFollowView()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 如果是 VIP,则不展示红点数字
        if LNUserManager.shared.userModel?.vipFlag == "1" {
            heartBadgeLabel.isHidden = true
            numbers = [0, 0, 0]
            segmentedDataSource.numbers = numbers
            segmentedView.reloadDataWithoutListContainer()
        } else {
            heartBadgeLabel.isHidden = false
        }


        setupUI()
        setupConstraints()
        setupSegmentedView()
    }



    // MARK: - Private Methods
    private func setupUI() {

        edgesForExtendedLayout = .all

        // 添加子视图
        view.addSubview(topBackgroundImageView)
        view.addSubview(segmentedView)
        view.addSubview(heartContainerView)
        view.addSubview(listContainerView)

        // 添加爱心相关子视图
        heartContainerView.addSubview(heartImageView)
        heartContainerView.addSubview(heartBadgeLabel)

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(heartButtonTapped))
        heartContainerView.addGestureRecognizer(tapGesture)
    }

    private func setupConstraints() {
        // 顶部背景图片视图约束
        topBackgroundImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview()
            make.right.equalToSuperview()
            make.height.equalTo(s(180))
        }

        // 分段控制器约束 - 为右侧爱心图标留出空间
        segmentedView.snp.makeConstraints { make in
            make.top.equalTo(kstatusBarH)
            make.left.equalTo(-35)
            make.right.equalTo(heartContainerView.snp.right)
            make.height.equalTo(s(50))
        }

        // 爱心容器视图约束
        heartContainerView.snp.makeConstraints { make in
            make.top.equalTo(kstatusBarH)
            make.right.equalToSuperview().offset(-15)
            make.width.equalTo(s(40))
            make.height.equalTo(s(50))
        }

        // 爱心图标约束
        heartImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(30))
        }

        // 红色数字标识约束 - 位于爱心图标正下方，左右居中
        heartBadgeLabel.snp.makeConstraints { make in
            make.top.equalTo(heartImageView.snp.bottom).offset(s(-12))
            make.centerX.equalTo(heartImageView.snp.centerX)
            make.width.height.equalTo(s(16))
        }

        // 内容容器约束
        listContainerView.snp.makeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
    }

    private func setupSegmentedView() {
        // 关联分段控制器和容器视图
        segmentedView.listContainer = listContainerView

        // 设置默认选中第一个
        segmentedView.defaultSelectedIndex = 0
    }

    // MARK: - Actions
    @objc private func heartButtonTapped() {
        print("爱心按钮被点击了")

        // 跳转到"谁喜欢我"页面
        // let whoLikedMeVC = LNWhoLikedMeViewController()
        // whoLikedMeVC.hidesBottomBarWhenPushed = true
        // navigationController?.pushViewController(whoLikedMeVC, animated: true)

        let whoLikedMeVC = LNGiftSelectionDemo()
        whoLikedMeVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(whoLikedMeVC, animated: true)
    }
}

// MARK: - JXSegmentedViewDelegate
extension LNHomeViewController: JXSegmentedViewDelegate {
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        // 分段控制器选中回调
        print("选中了第\(index)个分段")
        if index == 1 {
            let numbers = [0, 0, 0]
            segmentedDataSource.numbers = numbers
            segmentedView.reloadDataWithoutListContainer()
        }
    }
}

// MARK: - JXSegmentedListContainerViewDataSource
extension LNHomeViewController: JXSegmentedListContainerViewDataSource {

    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return segmentedDataSource.titles.count
    }

    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        switch index {
        case 0:
            return hotView
        case 1:
            return newView
        case 2:
            return followView
        default:
            return hotView
        }
    }
}
