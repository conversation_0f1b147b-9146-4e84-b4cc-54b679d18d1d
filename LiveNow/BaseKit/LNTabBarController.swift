//
//  LNTabBarController.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/7/28.
//

import UIKit
import SnapKit

/// 主Tab Bar控制器，管理应用的四个主要功能模块
class LNTabBarController: UITabBarController {

    // 自定义TabBar背景视图（仅左上/右上圆角 + 轻阴影）
    private lazy var customTabBarBackgroundView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.systemBackground
        v.layer.cornerRadius = 22
        v.layer.cornerCurve = .continuous
        v.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        v.layer.masksToBounds = false
        // 阴影（轻微）
        v.layer.shadowColor = UIColor.black.cgColor
        v.layer.shadowOpacity = 0.08
        v.layer.shadowRadius = 8
        v.layer.shadowOffset = CGSize(width: 0, height: -2)
        return v
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupTabBar()
        setupViewControllers()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 更新阴影路径，性能更好
        let path = UIBezierPath(
            roundedRect: customTabBarBackgroundView.bounds,
            byRoundingCorners: [.topLeft, .topRight],
            cornerRadii: CGSize(width: 16, height: 16)
        )
        customTabBarBackgroundView.layer.shadowPath = path.cgPath
    }

    /// 设置Tab Bar外观
    private func setupTabBar() {
        // 让系统的TabBar背景透明，以便显示我们的自定义背景
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = .clear
        appearance.shadowImage = nil
        appearance.shadowColor = nil
        tabBar.standardAppearance = appearance
        if #available(iOS 15.0, *) {
            tabBar.scrollEdgeAppearance = appearance
        }
        tabBar.backgroundImage = UIImage()
        tabBar.shadowImage = UIImage()
        tabBar.isTranslucent = true
        tabBar.clipsToBounds = false
        tabBar.tintColor = UIColor.hex(hexString: "#00DFAB")
        tabBar.unselectedItemTintColor = UIColor.systemGray2

        // 添加自定义背景视图
        if customTabBarBackgroundView.superview == nil {
            tabBar.insertSubview(customTabBarBackgroundView, at: 0)
            // 使用 SnapKit 约束，左右收缩一点，顶部留出少量空间
            customTabBarBackgroundView.snp.makeConstraints { make in
                make.left.equalTo(tabBar)
                make.right.equalTo(tabBar)
                make.top.equalTo(tabBar)
                // 与安全区底部对齐，保证适配有Home指示条的设备
                make.bottom.equalTo(0)
            }
        }
    }

    /// 设置所有视图控制器
    private func setupViewControllers() {
        // 创建各个页面的视图控制器
        let homeVC = LNHomeViewController()
        let matchVC = LNCompareViewController()
        let messagesVC = LNMessagesViewController()
        let profileVC = LNProfileViewController()

        // 包装到导航控制器中
        let homeNav = LNNavigationViewController(rootViewController: homeVC)
        let matchNav = LNNavigationViewController(rootViewController: matchVC)
        let messagesNav = LNNavigationViewController(rootViewController: messagesVC)
        let profileNav = LNNavigationViewController(rootViewController: profileVC)

        // 设置Tab Bar项目
        setupTabBarItem(for: homeNav, title: "Home", imageName: "ic_home_nor", selectedImageName: "ic_home_sel")
        setupTabBarItem(for: matchNav, title: "Explore", imageName: "ic_explore_nor", selectedImageName: "ic_explore_sel")
        setupTabBarItem(for: messagesNav, title: "Message", imageName: "ic_message_nor", selectedImageName: "ic_message_sel")
        setupTabBarItem(for: profileNav, title: "Me", imageName: "ic_me_nor", selectedImageName: "ic_me_sel")

        // 设置视图控制器数组
        viewControllers = [homeNav, matchNav, messagesNav, profileNav]

        // 默认选中首页
        selectedIndex = 0
    }

    /// 设置单个Tab Bar项目
    /// - Parameters:
    ///   - navigationController: 导航控制器
    ///   - title: 标题
    ///   - imageName: 普通状态图标名称
    ///   - selectedImageName: 选中状态图标名称
    private func setupTabBarItem(for navigationController: UINavigationController,
                                 title: String,
                                 imageName: String,
                                 selectedImageName: String) {
        let normalImage = UIImage(named: imageName)
        let selectedImage = UIImage(named: selectedImageName)

        navigationController.tabBarItem = UITabBarItem(
            title: title,
            image: normalImage,
            selectedImage: selectedImage
        )

        // 设置标题字体
        navigationController.tabBarItem.setTitleTextAttributes(
            [NSAttributedString.Key.font: LNFont.regular(10)],
            for: .normal
        )
    }
}
