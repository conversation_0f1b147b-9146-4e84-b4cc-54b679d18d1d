//
//  LNNavigationViewController.swift
//  LiveNow
//
//  Created by po on 2025/8/14.
//

import UIKit

protocol LNPopGestureRecognizerDelegate: NSObjectProtocol {
    func isEnablePopGestureRecognizer(_ gestureRecognizer: UIPanGestureRecognizer) -> Bool
}

class LNNavigationViewController: UINavigationController, UINavigationControllerDelegate {

    var backTarget: Any?
    var backAction: Selector?


    
    // 一体化导航背景（覆盖状态栏 + 导航栏高度，避免出现分层/色差）
    private let navBackgroundView: UIImageView = {
        let iv = UIImageView()
        iv.isUserInteractionEnabled = false
        iv.contentMode = .scaleToFill
        iv.clipsToBounds = true
        return iv
    }()
    private var lastBackgroundKey: String?

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        self.delegate = self
        setupGestureRecognizer()
        setupBackgroundView()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 扩展导航栏背景到屏幕外，填充侧滑时的空白区域
        navBackgroundView.frame = CGRect(x: 0, y: 0, width: view.bounds.width, height: knavH)
    }
    
    // MARK: - UINavigationControllerDelegate
    func navigationController(_ navigationController: UINavigationController, willShow viewController: UIViewController, animated: Bool) {
        applyBackground(from: viewController)
    }

    func navigationController(_ navigationController: UINavigationController, didShow viewController: UIViewController, animated: Bool) {
        // 确保转场完成后背景正确
        applyBackground(from: viewController)
    }

    private func setupGestureRecognizer() {
        guard let oldGesture = self.interactivePopGestureRecognizer,
              let navTargets = oldGesture.value(forKey: "_targets") as? NSArray,
              let firstTarget = navTargets.firstObject as? NSObject,
              let backTarget = firstTarget.value(forKey: "_target")
        else { return }
        
        oldGesture.isEnabled = false
        let gestureView = oldGesture.view
        let backAction = NSSelectorFromString("handleNavigationTransition:")
        
        let newGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        newGesture.delegate = self
        newGesture.maximumNumberOfTouches = 1
        gestureView?.addGestureRecognizer(newGesture)
        
        self.backTarget = backTarget
        self.backAction = backAction
    }

    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        // 调用原始的手势处理
        if let target = backTarget as? NSObject, let action = backAction {
            _ = target.perform(action, with: gesture)
        }
    }
    
    private func setupBackgroundView() {
        // 放在导航栏下方，覆盖状态栏 + 导航栏区域
        if navBackgroundView.superview == nil {
            view.insertSubview(navBackgroundView, belowSubview: navigationBar)
        }
        // 扩展导航栏背景到屏幕外，填充侧滑时的空白区域
        navBackgroundView.frame = CGRect(x: 0, y: 0, width: view.bounds.width, height: knavH)
    }
    
    override func pushViewController(_ viewController: UIViewController, animated: Bool) {
        if self.viewControllers.count > 0 {
            viewController.hidesBottomBarWhenPushed = true
        }
        super.pushViewController(viewController, animated: animated)
        // 在 push 后根据新的 topViewController 统一应用背景
        syncBackgroundWithTopController()
    }
    
    override func setViewControllers(_ viewControllers: [UIViewController], animated: Bool) {
        super.setViewControllers(viewControllers, animated: animated)
        syncBackgroundWithTopController()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        syncBackgroundWithTopController()
    }
    
    private func syncBackgroundWithTopController() {
        if let top = viewControllers.last as? LNBaseController {
            applyBackground(from: top)
        } else {
            // 非 LNBaseController 时，保持纯白
            navBackgroundView.image = LNGradient.makeSolidImage(color: .white, size: CGSize(width: view.bounds.width, height: knavH))
            lastBackgroundKey = nil
        }
    }
    
    /// 由子控制器调用，统一绘制状态栏+导航栏背景，避免出现分层
    func applyBackground(from controller: UIViewController) {
        // 如果当前需要隐藏导航栏，则同时隐藏背景视图，避免白色遮挡
        var shouldHideBackground = self.isNavigationBarHidden || self.navigationBar.isHidden
        if let base = controller as? LNBaseController {
            shouldHideBackground = shouldHideBackground || base.prefersNavigationBarHide
        }
        if shouldHideBackground {
            navBackgroundView.isHidden = true
            lastBackgroundKey = nil
            return
        } else {
            navBackgroundView.isHidden = false
        }

        guard let base = controller as? LNBaseController else {
            navBackgroundView.image = LNGradient.makeSolidImage(color: .white, size: CGSize(width: view.bounds.width, height: knavH))
            return
        }

        // 扩展图片尺寸，填充侧滑时的空白区域
        let size = CGSize(width: view.bounds.width, height: knavH)
        let backgroundColor = base.navigationSolidColor

        let key = "s_\(size.width)x\(size.height)_\(backgroundColor.description)"
        if lastBackgroundKey != key {
            navBackgroundView.image = LNGradient.makeSolidImage(color: backgroundColor, size: size)
            navBackgroundView.backgroundColor = .clear
            lastBackgroundKey = key
        }
    }
}

extension LNNavigationViewController: UIGestureRecognizerDelegate {
    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        guard let panGesture = gestureRecognizer as? UIPanGestureRecognizer else { return false }
        
        // 只有一个视图控制器时忽略
        if viewControllers.count <= 1 {
            return false
        }
        
        // 当导航控制器正在过渡时忽略
        if let transition = value(forKey: "_isTransitioning") as? Bool, transition {
            return false
        }
        
        // 向左和竖划时忽略
        let translation = panGesture.translation(in: panGesture.view)
        if translation.x <= 0 {
            return false
        }
        
        // 子视图控制器自己控制
        if let vc = viewControllers.last as? LNPopGestureRecognizerDelegate {
            return vc.isEnablePopGestureRecognizer(panGesture)
        }
        return true
    }
}
