//
//  LNFreeChanceModal.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/18.
//

import UIKit
import SnapKit
import JKSwiftExtension

/// 免费次数弹框
class LNFreeChanceModal: UIView {
    
    // MARK: - 回调
    var onRechargeNow: (() -> Void)?
    var onClose: (() -> Void)?
    
    // MARK: - UI Elements
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        return view
    }()
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = s(30)
        return view
    }()
    
    private lazy var heartIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.image = UIImage(named: "ic_flower_icon")
        return imageView
    }()
    
    private lazy var countdownLabel: UILabel = {
        let label = UILabel()
        label.text = "01:05:33"
        label.font = LNFont.bold(24)
        label.textColor = UIColor(hexString: "#00DFAB")
        label.textAlignment = .center
        return label
    }()
    
    private lazy var nextChanceLabel: UILabel = {
        let label = UILabel()
        label.text = "Next free chance"
        label.font = LNFont.regular(14)
        label.textColor = UIColor(hexString: "#151515")
        label.textAlignment = .center
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "you can get her whatsapp\nnumber by paying 80 diamonds"
        label.font = LNFont.regular(14)
        label.textColor = UIColor(hexString: "#151515")
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var rechargeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.title("Recharge Now").font(LNFont.medium(16)).textColor(UIColor(hexString: "#FFFFFF")).bgImage(UIImage.jk.gradient(["#04E798", "#0ADCE1"], size: CGSize(width: s(260), height: s(44)), direction: .horizontal)).corner(s(22))
        button.addTarget(self, action: #selector(rechargeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - 倒计时相关
    private var countdownTimer: Timer?
    private var remainingSeconds: Int = 3933 // 01:05:33 = 3933秒
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupActions()
        startCountdown()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        countdownTimer?.invalidate()
    }
    
    // MARK: - 私有方法
    private func setupUI() {
        addSubview(backgroundView)
        addSubview(containerView)
        
        containerView.addSubview(heartIconView)
        containerView.addSubview(countdownLabel)
        containerView.addSubview(nextChanceLabel)
        containerView.addSubview(descriptionLabel)
        containerView.addSubview(rechargeButton)
    }
    
    private func setupConstraints() {
        // 背景视图
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 容器视图
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(s(312))
            make.height.equalTo(s(225))
        }
        
        // 爱心图标
        heartIconView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(-55))
            make.centerX.equalToSuperview()
            make.width.height.equalTo(s(98))
        }
        
        // 倒计时标签
        countdownLabel.snp.makeConstraints { make in
            make.top.equalTo(s(39))
            make.centerX.equalToSuperview()
        }
        
        // 下次免费机会标签
        nextChanceLabel.snp.makeConstraints { make in
            make.top.equalTo(countdownLabel.snp.bottom)
            make.centerX.equalToSuperview()
            make.width.lessThanOrEqualTo(s(312))
        }
        
        // 描述标签
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(nextChanceLabel.snp.bottom).offset(s(21))
            make.centerX.equalToSuperview()
            make.width.lessThanOrEqualTo(s(312))
        }
        
        // 充值按钮
        rechargeButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(s(-21))
            make.centerX.equalToSuperview()
            make.width.equalTo(s(260))
            make.height.equalTo(s(44))
        }
    }
    
    private func setupActions() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundView.addGestureRecognizer(tapGesture)
    }

    private func startCountdown() {
        countdownTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateCountdown()
        }
    }

    private func updateCountdown() {
        guard remainingSeconds > 0 else {
            countdownTimer?.invalidate()
            countdownTimer = nil
            countdownLabel.text = "00:00:00"
            return
        }

        remainingSeconds -= 1

        let hours = remainingSeconds / 3600
        let minutes = (remainingSeconds % 3600) / 60
        let seconds = remainingSeconds % 60

        countdownLabel.text = String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }

    // MARK: - 事件处理
    @objc private func backgroundTapped() {
        dismiss()
    }

    @objc private func rechargeButtonTapped() {
        onRechargeNow?()

        // 添加按钮点击动画
        UIView.animate(withDuration: 0.1, animations: {
            self.rechargeButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.rechargeButton.transform = .identity
            }
        }
    }

    // MARK: - 公共方法
    func show(in parentView: UIView) {
        
        parentView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 显示动画
        containerView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        containerView.alpha = 0
        backgroundView.alpha = 0

        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseOut) {
            self.containerView.transform = .identity
            self.containerView.alpha = 1
            self.backgroundView.alpha = 1
        }
    }

    func dismiss() {
        countdownTimer?.invalidate()
        countdownTimer = nil

        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseIn, animations: {
            self.containerView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
            self.containerView.alpha = 0
            self.backgroundView.alpha = 0
        }) { _ in
            self.removeFromSuperview()
            self.onClose?()
        }
    }

    func updateCountdown(hours: Int, minutes: Int, seconds: Int) {
        remainingSeconds = hours * 3600 + minutes * 60 + seconds
        updateCountdown()
    }
}
